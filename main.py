"""
Main entry point for GestureFlow application.
Implements EPIC GFLOW-E01: Core Gesture Recognition Engine (MVP)
Implements EPIC GFLOW-E02: User-Defined Static Gesture Customization (MVP)
"""

import sys
import os
import warnings
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# Suppress protobuf deprecation warnings from MediaPipe
warnings.filterwarnings("ignore", category=UserWarning, module="google.protobuf.symbol_database")
warnings.filterwarnings("ignore", message=".*SymbolDatabase.GetPrototype.*")
warnings.filterwarnings("ignore", message=".*deprecated.*", module="google.protobuf.*")

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.main_window import MainWindow
from src.utils.logger import logger


def main():
    """Main application entry point."""
    try:
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("GestureFlow")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("GestureFlow")

        # Set application properties
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

        logger.info("Starting GestureFlow application")
        logger.info(f"Python version: {sys.version}")
        logger.info(f"Qt version: {app.applicationVersion()}")

        # Create and show main window
        main_window = MainWindow()
        main_window.show()

        logger.info("GestureFlow application started successfully")

        # Run application
        exit_code = app.exec_()

        logger.info(f"GestureFlow application exited with code: {exit_code}")
        return exit_code

    except Exception as e:
        logger.error(f"Failed to start GestureFlow application: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
