# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/gpu/scale_mode.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1emediapipe/gpu/scale_mode.proto\x12\tmediapipe\"I\n\tScaleMode\"<\n\x04Mode\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x12\x0b\n\x07STRETCH\x10\x01\x12\x07\n\x03\x46IT\x10\x02\x12\x11\n\rFILL_AND_CROP\x10\x03')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.gpu.scale_mode_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_SCALEMODE']._serialized_start=45
  _globals['_SCALEMODE']._serialized_end=118
  _globals['_SCALEMODE_MODE']._serialized_start=58
  _globals['_SCALEMODE_MODE']._serialized_end=118
# @@protoc_insertion_point(module_scope)
