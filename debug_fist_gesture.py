"""
Debug script for fist gesture recognition.
Tests the fist gesture detection logic and provides detailed output.
"""

import sys
import os
import numpy as np

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.gesture_recognizer import StaticGestureRecognizer
from src.core.hand_tracker import HandLandmarks
from src.utils.logger import logger


def create_test_fist_landmarks():
    """Create test landmarks for a fist gesture."""
    # Create landmarks where all fingertips are below or at MCP level
    landmarks = [
        (0.5, 0.8, 0.0),   # WRIST
        (0.4, 0.7, 0.0),   # THUMB_CMC
        (0.42, 0.72, 0.0), # THUMB_MCP
        (0.44, 0.74, 0.0), # THUMB_IP
        (0.46, 0.76, 0.0), # THUMB_TIP (curled - closer to wrist than MCP)
        (0.45, 0.7, 0.0),  # INDEX_MCP
        (0.45, 0.75, 0.0), # INDEX_PIP
        (0.45, 0.78, 0.0), # INDEX_DIP
        (0.45, 0.8, 0.0),  # INDEX_TIP (below MCP)
        (0.5, 0.7, 0.0),   # MIDDLE_MCP
        (0.5, 0.75, 0.0),  # MIDDLE_PIP
        (0.5, 0.78, 0.0),  # MIDDLE_DIP
        (0.5, 0.82, 0.0),  # MIDDLE_TIP (below MCP)
        (0.55, 0.7, 0.0),  # RING_MCP
        (0.55, 0.75, 0.0), # RING_PIP
        (0.55, 0.78, 0.0), # RING_DIP
        (0.55, 0.8, 0.0),  # RING_TIP (below MCP)
        (0.6, 0.7, 0.0),   # PINKY_MCP
        (0.6, 0.75, 0.0),  # PINKY_PIP
        (0.6, 0.78, 0.0),  # PINKY_DIP
        (0.6, 0.79, 0.0),  # PINKY_TIP (below MCP)
    ]
    return landmarks


def create_test_open_palm_landmarks():
    """Create test landmarks for an open palm gesture."""
    landmarks = [
        (0.5, 0.8, 0.0),   # WRIST
        (0.4, 0.7, 0.0),   # THUMB_CMC
        (0.35, 0.65, 0.0), # THUMB_MCP
        (0.3, 0.6, 0.0),   # THUMB_IP
        (0.25, 0.55, 0.0), # THUMB_TIP (extended)
        (0.45, 0.7, 0.0),  # INDEX_MCP
        (0.45, 0.6, 0.0),  # INDEX_PIP
        (0.45, 0.5, 0.0),  # INDEX_DIP
        (0.45, 0.4, 0.0),  # INDEX_TIP (above MCP)
        (0.5, 0.7, 0.0),   # MIDDLE_MCP
        (0.5, 0.6, 0.0),   # MIDDLE_PIP
        (0.5, 0.5, 0.0),   # MIDDLE_DIP
        (0.5, 0.35, 0.0),  # MIDDLE_TIP (above MCP)
        (0.55, 0.7, 0.0),  # RING_MCP
        (0.55, 0.6, 0.0),  # RING_PIP
        (0.55, 0.5, 0.0),  # RING_DIP
        (0.55, 0.4, 0.0),  # RING_TIP (above MCP)
        (0.6, 0.7, 0.0),   # PINKY_MCP
        (0.6, 0.6, 0.0),   # PINKY_PIP
        (0.6, 0.5, 0.0),   # PINKY_DIP
        (0.6, 0.45, 0.0),  # PINKY_TIP (above MCP)
    ]
    return landmarks


def debug_fist_detection(recognizer, landmarks, gesture_name):
    """Debug the fist detection logic step by step."""
    print(f"\n=== Debugging {gesture_name} ===")
    
    # Create HandLandmarks object
    hand_landmarks = HandLandmarks(landmarks=landmarks, handedness="Right", confidence=1.0)
    
    # Test fist detection
    fist_confidence = recognizer._check_fist(hand_landmarks)
    print(f"Fist confidence: {fist_confidence:.3f}")
    
    # Manual step-by-step analysis
    print("\nStep-by-step analysis:")
    
    # Check thumb
    thumb_tip = landmarks[recognizer.LANDMARK_INDICES['THUMB_TIP']]
    thumb_mcp = landmarks[recognizer.LANDMARK_INDICES['THUMB_MCP']]
    wrist = landmarks[recognizer.LANDMARK_INDICES['WRIST']]
    
    thumb_tip_dist = recognizer._distance(thumb_tip, wrist)
    thumb_mcp_dist = recognizer._distance(thumb_mcp, wrist)
    thumb_curled = thumb_tip_dist < thumb_mcp_dist
    
    print(f"Thumb:")
    print(f"  Tip distance from wrist: {thumb_tip_dist:.3f}")
    print(f"  MCP distance from wrist: {thumb_mcp_dist:.3f}")
    print(f"  Curled: {thumb_curled}")
    
    # Check other fingers
    finger_names = ['INDEX', 'MIDDLE', 'RING', 'PINKY']
    fingers_curled = [thumb_curled]
    
    for finger_name in finger_names:
        tip = landmarks[recognizer.LANDMARK_INDICES[f'{finger_name}_TIP']]
        mcp = landmarks[recognizer.LANDMARK_INDICES[f'{finger_name}_MCP']]
        
        finger_curled = tip[1] >= mcp[1]  # Tip below or at MCP level
        fingers_curled.append(finger_curled)
        
        print(f"{finger_name}:")
        print(f"  Tip Y: {tip[1]:.3f}, MCP Y: {mcp[1]:.3f}")
        print(f"  Curled: {finger_curled}")
    
    curled_count = sum(fingers_curled)
    print(f"\nTotal curled fingers: {curled_count}/5")
    print(f"Final confidence: {curled_count/5.0:.3f}")
    
    # Test all gestures for comparison
    print(f"\nAll gesture confidences for {gesture_name}:")
    all_scores = {
        'OPEN_PALM': recognizer._check_open_palm(hand_landmarks),
        'FIST': recognizer._check_fist(hand_landmarks),
        'PEACE_SIGN': recognizer._check_peace_sign(hand_landmarks),
        'THUMBS_UP': recognizer._check_thumbs_up(hand_landmarks),
        'POINTING': recognizer._check_pointing(hand_landmarks)
    }
    
    for gesture, score in all_scores.items():
        print(f"  {gesture}: {score:.3f}")
    
    # Test full recognition
    result = recognizer.recognize_gesture(hand_landmarks)
    print(f"\nFinal recognition result:")
    print(f"  Gesture: {result.gesture_type.value}")
    print(f"  Confidence: {result.confidence:.3f}")
    print(f"  Threshold: {recognizer.confidence_threshold}")


def test_various_fist_positions():
    """Test different fist positions to see what works."""
    print("\n" + "="*60)
    print("Testing Various Fist Positions")
    print("="*60)
    
    recognizer = StaticGestureRecognizer(confidence_threshold=0.7)
    
    # Test 1: Perfect fist (all fingers well below MCP)
    print("\n--- Test 1: Perfect Fist ---")
    perfect_fist = create_test_fist_landmarks()
    debug_fist_detection(recognizer, perfect_fist, "Perfect Fist")
    
    # Test 2: Loose fist (fingers just at MCP level)
    print("\n--- Test 2: Loose Fist ---")
    loose_fist = create_test_fist_landmarks()
    # Adjust fingertips to be exactly at MCP level
    for i, finger in enumerate(['INDEX', 'MIDDLE', 'RING', 'PINKY']):
        tip_idx = recognizer.LANDMARK_INDICES[f'{finger}_TIP']
        mcp_idx = recognizer.LANDMARK_INDICES[f'{finger}_MCP']
        loose_fist[tip_idx] = (loose_fist[tip_idx][0], loose_fist[mcp_idx][1], loose_fist[tip_idx][2])
    
    debug_fist_detection(recognizer, loose_fist, "Loose Fist")
    
    # Test 3: Open palm for comparison
    print("\n--- Test 3: Open Palm (should NOT be fist) ---")
    open_palm = create_test_open_palm_landmarks()
    debug_fist_detection(recognizer, open_palm, "Open Palm")
    
    # Test 4: Lower confidence threshold
    print("\n--- Test 4: Lower Threshold (0.5) ---")
    recognizer_low = StaticGestureRecognizer(confidence_threshold=0.5)
    debug_fist_detection(recognizer_low, perfect_fist, "Perfect Fist (Low Threshold)")


def main():
    """Main function."""
    print("Fist Gesture Recognition Debug Tool")
    print("="*50)
    
    try:
        test_various_fist_positions()
        
        print("\n" + "="*60)
        print("Debug Summary:")
        print("- Check if your fist has all fingertips below MCP joints")
        print("- Ensure thumb is closer to wrist than thumb MCP")
        print("- Try adjusting confidence threshold if needed")
        print("- The algorithm uses Y-coordinates (vertical position)")
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        logger.error(f"Debug script failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
