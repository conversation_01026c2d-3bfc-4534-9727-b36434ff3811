# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/components/processors/proto/text_model_type.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nDmediapipe/tasks/cc/components/processors/proto/text_model_type.proto\x12+mediapipe.tasks.components.processors.proto\"u\n\rTextModelType\"d\n\tModelType\x12\x15\n\x11UNSPECIFIED_MODEL\x10\x00\x12\x0e\n\nBERT_MODEL\x10\x01\x12\x0f\n\x0bREGEX_MODEL\x10\x02\x12\x10\n\x0cSTRING_MODEL\x10\x03\x12\r\n\tUSE_MODEL\x10\x04')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.components.processors.proto.text_model_type_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_TEXTMODELTYPE']._serialized_start=117
  _globals['_TEXTMODELTYPE']._serialized_end=234
  _globals['_TEXTMODELTYPE_MODELTYPE']._serialized_start=134
  _globals['_TEXTMODELTYPE_MODELTYPE']._serialized_end=234
# @@protoc_insertion_point(module_scope)
