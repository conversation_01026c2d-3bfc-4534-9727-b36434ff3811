"""
Test script for EPIC GFLOW-E02: User-Defined Static Gesture Customization (MVP)
Tests all components of the custom gesture system.
"""

import sys
import os
import numpy as np

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.custom_gesture_manager import CustomGestureManager
from src.core.feature_extractor import HandFeatureExtractor
from src.core.gesture_trainer import G<PERSON>ure<PERSON>rainer
from src.core.ambiguity_detector import GestureAmbiguityDetector
from src.core.hand_tracker import HandLandmarks
from src.utils.logger import logger


def create_mock_landmarks(gesture_type="open_palm"):
    """Create mock hand landmarks for testing."""
    landmarks = []
    
    if gesture_type == "open_palm":
        # Mock open palm landmarks (all fingers extended)
        landmarks = [
            (0.5, 0.8, 0.0),   # WRIST
            (0.4, 0.7, 0.0),   # THUMB_CMC
            (0.35, 0.65, 0.0), # THUMB_MCP
            (0.3, 0.6, 0.0),   # THUMB_IP
            (0.25, 0.55, 0.0), # THUMB_TIP
            (0.45, 0.7, 0.0),  # INDEX_MCP
            (0.45, 0.6, 0.0),  # INDEX_PIP
            (0.45, 0.5, 0.0),  # INDEX_DIP
            (0.45, 0.4, 0.0),  # INDEX_TIP
            (0.5, 0.7, 0.0),   # MIDDLE_MCP
            (0.5, 0.6, 0.0),   # MIDDLE_PIP
            (0.5, 0.5, 0.0),   # MIDDLE_DIP
            (0.5, 0.35, 0.0),  # MIDDLE_TIP
            (0.55, 0.7, 0.0),  # RING_MCP
            (0.55, 0.6, 0.0),  # RING_PIP
            (0.55, 0.5, 0.0),  # RING_DIP
            (0.55, 0.4, 0.0),  # RING_TIP
            (0.6, 0.7, 0.0),   # PINKY_MCP
            (0.6, 0.6, 0.0),   # PINKY_PIP
            (0.6, 0.5, 0.0),   # PINKY_DIP
            (0.6, 0.45, 0.0),  # PINKY_TIP
        ]
    elif gesture_type == "fist":
        # Mock fist landmarks (all fingers curled)
        landmarks = [
            (0.5, 0.8, 0.0),   # WRIST
            (0.4, 0.7, 0.0),   # THUMB_CMC
            (0.42, 0.72, 0.0), # THUMB_MCP
            (0.44, 0.74, 0.0), # THUMB_IP
            (0.46, 0.76, 0.0), # THUMB_TIP
            (0.45, 0.7, 0.0),  # INDEX_MCP
            (0.45, 0.75, 0.0), # INDEX_PIP
            (0.45, 0.78, 0.0), # INDEX_DIP
            (0.45, 0.8, 0.0),  # INDEX_TIP
            (0.5, 0.7, 0.0),   # MIDDLE_MCP
            (0.5, 0.75, 0.0),  # MIDDLE_PIP
            (0.5, 0.78, 0.0),  # MIDDLE_DIP
            (0.5, 0.82, 0.0),  # MIDDLE_TIP
            (0.55, 0.7, 0.0),  # RING_MCP
            (0.55, 0.75, 0.0), # RING_PIP
            (0.55, 0.78, 0.0), # RING_DIP
            (0.55, 0.8, 0.0),  # RING_TIP
            (0.6, 0.7, 0.0),   # PINKY_MCP
            (0.6, 0.75, 0.0),  # PINKY_PIP
            (0.6, 0.78, 0.0),  # PINKY_DIP
            (0.6, 0.79, 0.0),  # PINKY_TIP
        ]
    
    return landmarks


def test_custom_gesture_manager():
    """Test CustomGestureManager functionality."""
    print("\n=== Testing Custom Gesture Manager ===")
    
    # Create test data directory
    test_data_dir = "test_data"
    if not os.path.exists(test_data_dir):
        os.makedirs(test_data_dir)
    
    manager = CustomGestureManager(test_data_dir)
    
    # Test creating new gesture
    gesture_id = manager.create_new_gesture("Test Wave", "A simple wave gesture")
    print(f"✓ Created gesture: {gesture_id}")
    
    # Test adding training samples
    for i in range(10):
        landmarks = create_mock_landmarks("open_palm")
        # Add some variation
        for j in range(len(landmarks)):
            x, y, z = landmarks[j]
            landmarks[j] = (x + np.random.normal(0, 0.01), 
                          y + np.random.normal(0, 0.01), 
                          z + np.random.normal(0, 0.001))
        
        hand_landmarks = HandLandmarks(landmarks=landmarks, handedness="Right", confidence=0.9)
        success = manager.add_training_sample(gesture_id, hand_landmarks)
        if not success:
            print(f"✗ Failed to add sample {i}")
            return False
    
    print(f"✓ Added 10 training samples")
    
    # Test getting gesture
    gesture_data = manager.get_gesture(gesture_id)
    if gesture_data:
        print(f"✓ Retrieved gesture: {gesture_data.name}")
        print(f"  Samples: {len(gesture_data.training_samples)}")
    else:
        print("✗ Failed to retrieve gesture")
        return False
    
    # Test statistics
    stats = manager.get_gesture_statistics()
    print(f"✓ Statistics: {stats}")
    
    return True


def test_feature_extractor():
    """Test HandFeatureExtractor functionality."""
    print("\n=== Testing Feature Extractor ===")
    
    extractor = HandFeatureExtractor()
    
    # Test feature extraction
    landmarks = create_mock_landmarks("open_palm")
    hand_landmarks = HandLandmarks(landmarks=landmarks, handedness="Right", confidence=0.9)
    
    features = extractor.extract_features(hand_landmarks)
    
    if features:
        print(f"✓ Extracted {len(features)} features")
        print(f"  Feature range: {min(features):.3f} to {max(features):.3f}")
        
        # Test feature names
        feature_names = extractor.get_feature_names()
        print(f"✓ Feature names: {len(feature_names)} names")
        
        if len(features) == len(feature_names):
            print("✓ Feature count matches name count")
        else:
            print(f"✗ Feature count mismatch: {len(features)} vs {len(feature_names)}")
            return False
    else:
        print("✗ Failed to extract features")
        return False
    
    return True


def test_gesture_trainer():
    """Test GestureTrainer functionality."""
    print("\n=== Testing Gesture Trainer ===")
    
    # Create test data
    test_data_dir = "test_data"
    manager = CustomGestureManager(test_data_dir)
    trainer = GestureTrainer(os.path.join(test_data_dir, "models"))
    
    # Create gesture with training data
    gesture_id = manager.create_new_gesture("Test Gesture", "Test gesture for training")
    
    # Add training samples
    for i in range(15):
        landmarks = create_mock_landmarks("open_palm")
        # Add variation
        for j in range(len(landmarks)):
            x, y, z = landmarks[j]
            landmarks[j] = (x + np.random.normal(0, 0.02), 
                          y + np.random.normal(0, 0.02), 
                          z + np.random.normal(0, 0.002))
        
        hand_landmarks = HandLandmarks(landmarks=landmarks, handedness="Right", confidence=0.9)
        manager.add_training_sample(gesture_id, hand_landmarks)
    
    gesture_data = manager.get_gesture(gesture_id)
    
    # Test training
    success, accuracy, model_path = trainer.train_gesture(gesture_data)
    
    if success:
        print(f"✓ Training successful: accuracy={accuracy:.3f}")
        print(f"  Model saved to: {model_path}")
        
        # Test model loading
        model_data = trainer.load_model(model_path)
        if model_data:
            print("✓ Model loaded successfully")
            
            # Test prediction
            test_landmarks = create_mock_landmarks("open_palm")
            predicted_id, confidence = trainer.predict_gesture(model_data, test_landmarks)
            print(f"✓ Prediction: {predicted_id}, confidence: {confidence:.3f}")
        else:
            print("✗ Failed to load model")
            return False
    else:
        print("✗ Training failed")
        return False
    
    return True


def test_ambiguity_detector():
    """Test GestureAmbiguityDetector functionality."""
    print("\n=== Testing Ambiguity Detector ===")
    
    # Create test data
    test_data_dir = "test_data"
    manager = CustomGestureManager(test_data_dir)
    detector = GestureAmbiguityDetector(similarity_threshold=0.8)
    
    # Create two similar gestures
    gesture1_id = manager.create_new_gesture("Similar Gesture 1", "First similar gesture")
    gesture2_id = manager.create_new_gesture("Similar Gesture 2", "Second similar gesture")
    
    # Add similar training samples
    base_landmarks = create_mock_landmarks("open_palm")
    
    for gesture_id in [gesture1_id, gesture2_id]:
        for i in range(10):
            landmarks = base_landmarks.copy()
            # Add small variation
            for j in range(len(landmarks)):
                x, y, z = landmarks[j]
                landmarks[j] = (x + np.random.normal(0, 0.005), 
                              y + np.random.normal(0, 0.005), 
                              z + np.random.normal(0, 0.0005))
            
            hand_landmarks = HandLandmarks(landmarks=landmarks, handedness="Right", confidence=0.9)
            manager.add_training_sample(gesture_id, hand_landmarks)
    
    # Test ambiguity analysis
    warnings = detector.analyze_gesture_ambiguity(manager)
    
    if warnings:
        print(f"✓ Found {len(warnings)} ambiguity warnings")
        for warning in warnings:
            print(f"  Warning: {warning.description}")
            print(f"  Similarity: {warning.similarity_score:.3f}")
    else:
        print("✓ No ambiguity warnings (or gestures not similar enough)")
    
    # Test similarity matrix
    similarity_matrix = detector.get_similarity_matrix(manager)
    print(f"✓ Similarity matrix calculated: {len(similarity_matrix)} gestures")
    
    return True


def cleanup_test_data():
    """Clean up test data directory."""
    import shutil
    test_data_dir = "test_data"
    if os.path.exists(test_data_dir):
        shutil.rmtree(test_data_dir)
        print("✓ Cleaned up test data")


def main():
    """Run all tests for EPIC GFLOW-E02."""
    print("Testing EPIC GFLOW-E02: User-Defined Static Gesture Customization")
    print("=" * 70)
    
    tests = [
        ("Custom Gesture Manager", test_custom_gesture_manager),
        ("Feature Extractor", test_feature_extractor),
        ("Gesture Trainer", test_gesture_trainer),
        ("Ambiguity Detector", test_ambiguity_detector),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\nRunning {test_name} test...")
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✓ {test_name} test PASSED")
            else:
                print(f"✗ {test_name} test FAILED")
                
        except Exception as e:
            print(f"✗ {test_name} test CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All EPIC GFLOW-E02 components are working correctly!")
    else:
        print("⚠ Some tests failed. Please check the implementation.")
    
    # Cleanup
    cleanup_test_data()
    
    return 0 if passed == len(results) else 1


if __name__ == "__main__":
    sys.exit(main())
