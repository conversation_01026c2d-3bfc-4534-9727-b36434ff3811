"""
Static Gesture Recognizer for GestureFlow application.
Recognizes pre-defined static hand gestures using rule-based approach.
Also integrates custom user-defined gestures using ML models.
Implements GFLOW-3: Implement Pre-defined Static Gesture Recognizer
Implements GFLOW-8: Integrate Custom Static Gestures into Recognition Engine
"""

import numpy as np
import math
from typing import List, Optional, Dict, Any, Tuple
from enum import Enum
from dataclasses import dataclass
from src.core.hand_tracker import HandLandmarks
from src.utils.logger import logger


class GestureType(Enum):
    """Enumeration of supported gesture types."""
    UNKNOWN = "unknown"
    OPEN_PALM = "open_palm"
    FIST = "fist"
    PEACE_SIGN = "peace_sign"
    THUMBS_UP = "thumbs_up"
    POINTING = "pointing"
    CUSTOM = "custom"  # For user-defined gestures


@dataclass
class GestureResult:
    """Result of gesture recognition."""
    gesture_type: GestureType
    confidence: float
    hand_landmarks: HandLandmarks
    gesture_id: Optional[str] = None  # For custom gestures
    gesture_name: Optional[str] = None  # For custom gestures

    def __str__(self):
        if self.gesture_type == GestureType.CUSTOM and self.gesture_name:
            return f"{self.gesture_name} (confidence: {self.confidence:.2f})"
        return f"{self.gesture_type.value} (confidence: {self.confidence:.2f})"


class StaticGestureRecognizer:
    """
    Static gesture recognizer supporting both pre-defined and custom gestures.
    Uses rule-based approach for pre-defined gestures and ML models for custom gestures.
    """

    def __init__(self, confidence_threshold: float = 0.7):
        """
        Initialize gesture recognizer.

        Args:
            confidence_threshold: Minimum confidence for gesture recognition
        """
        self.confidence_threshold = confidence_threshold

        # Custom gesture support
        self.custom_gesture_manager = None
        self.gesture_trainer = None
        self.loaded_custom_models: Dict[str, Dict[str, Any]] = {}

        # MediaPipe hand landmark indices
        self.LANDMARK_INDICES = {
            'WRIST': 0,
            'THUMB_CMC': 1, 'THUMB_MCP': 2, 'THUMB_IP': 3, 'THUMB_TIP': 4,
            'INDEX_MCP': 5, 'INDEX_PIP': 6, 'INDEX_DIP': 7, 'INDEX_TIP': 8,
            'MIDDLE_MCP': 9, 'MIDDLE_PIP': 10, 'MIDDLE_DIP': 11, 'MIDDLE_TIP': 12,
            'RING_MCP': 13, 'RING_PIP': 14, 'RING_DIP': 15, 'RING_TIP': 16,
            'PINKY_MCP': 17, 'PINKY_PIP': 18, 'PINKY_DIP': 19, 'PINKY_TIP': 20
        }

        logger.info(f"StaticGestureRecognizer initialized with threshold: {confidence_threshold}")

    def set_custom_gesture_support(self, gesture_manager, gesture_trainer):
        """
        Set custom gesture manager and trainer for custom gesture recognition.

        Args:
            gesture_manager: CustomGestureManager instance
            gesture_trainer: GestureTrainer instance
        """
        self.custom_gesture_manager = gesture_manager
        self.gesture_trainer = gesture_trainer
        self.load_custom_models()
        logger.info("Custom gesture support enabled")

    def load_custom_models(self):
        """Load all trained custom gesture models."""
        if not self.custom_gesture_manager or not self.gesture_trainer:
            return

        self.loaded_custom_models.clear()

        try:
            enabled_gestures = self.custom_gesture_manager.get_enabled_gestures()

            for gesture_id, gesture_data in enabled_gestures.items():
                if gesture_data.model_path:
                    model_data = self.gesture_trainer.load_model(gesture_data.model_path)
                    if model_data:
                        self.loaded_custom_models[gesture_id] = model_data
                        logger.info(f"Loaded custom gesture model: {gesture_data.name}")

            logger.info(f"Loaded {len(self.loaded_custom_models)} custom gesture models")

        except Exception as e:
            logger.error(f"Error loading custom models: {e}")

    def recognize_gesture(self, hand_landmarks: HandLandmarks) -> GestureResult:
        """
        Recognize gesture from hand landmarks.
        Prioritizes pre-defined gestures to ensure they work reliably.

        Args:
            hand_landmarks: Hand landmarks data

        Returns:
            GestureResult with recognized gesture and confidence
        """
        try:
            # Check pre-defined gestures FIRST (they are more reliable)
            predefined_scores = {
                GestureType.OPEN_PALM: self._check_open_palm(hand_landmarks),
                GestureType.FIST: self._check_fist(hand_landmarks),
                GestureType.PEACE_SIGN: self._check_peace_sign(hand_landmarks),
                GestureType.THUMBS_UP: self._check_thumbs_up(hand_landmarks),
                GestureType.POINTING: self._check_pointing(hand_landmarks)
            }

            # Find best pre-defined gesture
            best_predefined = max(predefined_scores.items(), key=lambda x: x[1])
            best_predefined_type, best_predefined_confidence = best_predefined

            # If we have a strong pre-defined gesture match, use it immediately
            if best_predefined_confidence >= self.confidence_threshold:
                return GestureResult(
                    gesture_type=best_predefined_type,
                    confidence=best_predefined_confidence,
                    hand_landmarks=hand_landmarks
                )

            # Only check custom gestures if no strong pre-defined match
            best_custom_result = self._check_custom_gestures(hand_landmarks)

            # Use custom gesture if it's strong enough
            if (best_custom_result and
                best_custom_result.confidence >= 0.8):  # High threshold for custom gestures
                return best_custom_result

            # If we have a moderate pre-defined gesture, prefer it over weak custom gestures
            if best_predefined_confidence >= 0.5:
                return GestureResult(
                    gesture_type=best_predefined_type,
                    confidence=best_predefined_confidence,
                    hand_landmarks=hand_landmarks
                )

            # If we have a moderate custom gesture match and no decent pre-defined match
            if (best_custom_result and
                best_custom_result.confidence >= 0.6):
                return best_custom_result

            # No gesture recognized
            return GestureResult(
                gesture_type=GestureType.UNKNOWN,
                confidence=0.0,
                hand_landmarks=hand_landmarks
            )

        except Exception as e:
            logger.error(f"Error recognizing gesture: {e}")
            return GestureResult(
                gesture_type=GestureType.UNKNOWN,
                confidence=0.0,
                hand_landmarks=hand_landmarks
            )

    def _check_custom_gestures(self, hand_landmarks: HandLandmarks) -> Optional[GestureResult]:
        """
        Check custom gestures using trained ML models.

        Args:
            hand_landmarks: Hand landmarks data

        Returns:
            Best custom gesture result or None
        """
        if not self.loaded_custom_models or not self.gesture_trainer:
            return None

        best_result = None
        best_confidence = 0.0
        all_predictions = []

        try:
            # Get predictions from all custom gesture models
            for gesture_id, model_data in self.loaded_custom_models.items():
                predicted_id, confidence = self.gesture_trainer.predict_gesture(
                    model_data, hand_landmarks.landmarks
                )

                # Store all predictions for analysis
                all_predictions.append((gesture_id, predicted_id, confidence))

                if predicted_id == gesture_id:
                    # Get gesture data for name and threshold
                    gesture_data = self.custom_gesture_manager.get_gesture(gesture_id)
                    if gesture_data:
                        # Apply individual gesture confidence threshold
                        min_threshold = max(gesture_data.confidence_threshold, 0.5)  # At least 50%

                        if confidence >= min_threshold and confidence > best_confidence:
                            best_confidence = confidence
                            best_result = GestureResult(
                                gesture_type=GestureType.CUSTOM,
                                confidence=confidence,
                                hand_landmarks=hand_landmarks,
                                gesture_id=gesture_id,
                                gesture_name=gesture_data.name
                            )

            # Additional validation: check for conflicting predictions
            if best_result and len(all_predictions) > 1:
                # Count how many models gave positive predictions
                positive_predictions = [p for p in all_predictions if p[1] is not None and p[2] >= 0.5]

                if len(positive_predictions) > 1:
                    # Multiple gestures detected, be more conservative
                    # Only accept if the best one is significantly better
                    second_best = sorted(positive_predictions, key=lambda x: x[2], reverse=True)
                    if len(second_best) > 1:
                        best_conf = second_best[0][2]
                        second_conf = second_best[1][2]

                        # Require at least 20% confidence gap
                        if best_conf - second_conf < 0.2:
                            logger.debug(f"Ambiguous custom gesture detection: {best_conf:.3f} vs {second_conf:.3f}")
                            return None

            return best_result

        except Exception as e:
            logger.error(f"Error checking custom gestures: {e}")
            return None

    def _check_open_palm(self, hand_landmarks: HandLandmarks) -> float:
        """Check if gesture is an open palm."""
        try:
            landmarks = hand_landmarks.landmarks

            # Check if all fingers are extended
            fingers_extended = []

            # Thumb (special case - check x-coordinate relative to hand)
            thumb_tip = landmarks[self.LANDMARK_INDICES['THUMB_TIP']]
            thumb_mcp = landmarks[self.LANDMARK_INDICES['THUMB_MCP']]
            wrist = landmarks[self.LANDMARK_INDICES['WRIST']]

            # For thumb, check if tip is further from wrist than MCP
            thumb_extended = self._distance(thumb_tip, wrist) > self._distance(thumb_mcp, wrist)
            fingers_extended.append(thumb_extended)

            # Other fingers (check y-coordinate - tip should be above MCP)
            finger_tips = ['INDEX_TIP', 'MIDDLE_TIP', 'RING_TIP', 'PINKY_TIP']
            finger_mcps = ['INDEX_MCP', 'MIDDLE_MCP', 'RING_MCP', 'PINKY_MCP']

            for tip_name, mcp_name in zip(finger_tips, finger_mcps):
                tip = landmarks[self.LANDMARK_INDICES[tip_name]]
                mcp = landmarks[self.LANDMARK_INDICES[mcp_name]]

                # Finger is extended if tip is above MCP (lower y value)
                finger_extended = tip[1] < mcp[1]
                fingers_extended.append(finger_extended)

            # Calculate confidence based on how many fingers are extended
            extended_count = sum(fingers_extended)
            confidence = extended_count / 5.0  # 5 fingers total

            return confidence

        except Exception as e:
            logger.error(f"Error checking open palm: {e}")
            return 0.0

    def _check_fist(self, hand_landmarks: HandLandmarks) -> float:
        """Check if gesture is a fist."""
        try:
            landmarks = hand_landmarks.landmarks

            # Check if all fingers are curled (opposite of open palm)
            fingers_curled = []

            # Thumb
            thumb_tip = landmarks[self.LANDMARK_INDICES['THUMB_TIP']]
            thumb_mcp = landmarks[self.LANDMARK_INDICES['THUMB_MCP']]
            wrist = landmarks[self.LANDMARK_INDICES['WRIST']]

            thumb_curled = self._distance(thumb_tip, wrist) < self._distance(thumb_mcp, wrist)
            fingers_curled.append(thumb_curled)

            # Other fingers
            finger_tips = ['INDEX_TIP', 'MIDDLE_TIP', 'RING_TIP', 'PINKY_TIP']
            finger_mcps = ['INDEX_MCP', 'MIDDLE_MCP', 'RING_MCP', 'PINKY_MCP']

            for tip_name, mcp_name in zip(finger_tips, finger_mcps):
                tip = landmarks[self.LANDMARK_INDICES[tip_name]]
                mcp = landmarks[self.LANDMARK_INDICES[mcp_name]]

                # Finger is curled if tip is below or at same level as MCP
                finger_curled = tip[1] >= mcp[1]
                fingers_curled.append(finger_curled)

            # Calculate confidence
            curled_count = sum(fingers_curled)
            confidence = curled_count / 5.0

            return confidence

        except Exception as e:
            logger.error(f"Error checking fist: {e}")
            return 0.0

    def _check_peace_sign(self, hand_landmarks: HandLandmarks) -> float:
        """Check if gesture is a peace sign (index and middle fingers extended)."""
        try:
            landmarks = hand_landmarks.landmarks

            # Check index and middle fingers are extended
            index_tip = landmarks[self.LANDMARK_INDICES['INDEX_TIP']]
            index_mcp = landmarks[self.LANDMARK_INDICES['INDEX_MCP']]
            middle_tip = landmarks[self.LANDMARK_INDICES['MIDDLE_TIP']]
            middle_mcp = landmarks[self.LANDMARK_INDICES['MIDDLE_MCP']]

            index_extended = index_tip[1] < index_mcp[1]
            middle_extended = middle_tip[1] < middle_mcp[1]

            # Check other fingers are curled
            ring_tip = landmarks[self.LANDMARK_INDICES['RING_TIP']]
            ring_mcp = landmarks[self.LANDMARK_INDICES['RING_MCP']]
            pinky_tip = landmarks[self.LANDMARK_INDICES['PINKY_TIP']]
            pinky_mcp = landmarks[self.LANDMARK_INDICES['PINKY_MCP']]

            ring_curled = ring_tip[1] >= ring_mcp[1]
            pinky_curled = pinky_tip[1] >= pinky_mcp[1]

            # Thumb should be curled or neutral
            thumb_tip = landmarks[self.LANDMARK_INDICES['THUMB_TIP']]
            thumb_mcp = landmarks[self.LANDMARK_INDICES['THUMB_MCP']]
            wrist = landmarks[self.LANDMARK_INDICES['WRIST']]
            thumb_curled = self._distance(thumb_tip, wrist) <= self._distance(thumb_mcp, wrist) * 1.1

            # Calculate confidence
            conditions = [index_extended, middle_extended, ring_curled, pinky_curled, thumb_curled]
            confidence = sum(conditions) / len(conditions)

            return confidence

        except Exception as e:
            logger.error(f"Error checking peace sign: {e}")
            return 0.0

    def _check_thumbs_up(self, hand_landmarks: HandLandmarks) -> float:
        """Check if gesture is thumbs up."""
        try:
            landmarks = hand_landmarks.landmarks

            # Thumb should be extended upward
            thumb_tip = landmarks[self.LANDMARK_INDICES['THUMB_TIP']]
            thumb_mcp = landmarks[self.LANDMARK_INDICES['THUMB_MCP']]
            wrist = landmarks[self.LANDMARK_INDICES['WRIST']]

            # Thumb extended and pointing up
            thumb_extended = self._distance(thumb_tip, wrist) > self._distance(thumb_mcp, wrist)
            thumb_up = thumb_tip[1] < thumb_mcp[1]  # Tip above MCP

            # Other fingers should be curled
            finger_tips = ['INDEX_TIP', 'MIDDLE_TIP', 'RING_TIP', 'PINKY_TIP']
            finger_mcps = ['INDEX_MCP', 'MIDDLE_MCP', 'RING_MCP', 'PINKY_MCP']

            fingers_curled = []
            for tip_name, mcp_name in zip(finger_tips, finger_mcps):
                tip = landmarks[self.LANDMARK_INDICES[tip_name]]
                mcp = landmarks[self.LANDMARK_INDICES[mcp_name]]
                finger_curled = tip[1] >= mcp[1]
                fingers_curled.append(finger_curled)

            # Calculate confidence
            conditions = [thumb_extended, thumb_up] + fingers_curled
            confidence = sum(conditions) / len(conditions)

            return confidence

        except Exception as e:
            logger.error(f"Error checking thumbs up: {e}")
            return 0.0

    def _check_pointing(self, hand_landmarks: HandLandmarks) -> float:
        """Check if gesture is pointing (index finger extended)."""
        try:
            landmarks = hand_landmarks.landmarks

            # Index finger should be extended
            index_tip = landmarks[self.LANDMARK_INDICES['INDEX_TIP']]
            index_mcp = landmarks[self.LANDMARK_INDICES['INDEX_MCP']]
            index_extended = index_tip[1] < index_mcp[1]

            # Other fingers should be curled
            finger_tips = ['MIDDLE_TIP', 'RING_TIP', 'PINKY_TIP']
            finger_mcps = ['MIDDLE_MCP', 'RING_MCP', 'PINKY_MCP']

            fingers_curled = []
            for tip_name, mcp_name in zip(finger_tips, finger_mcps):
                tip = landmarks[self.LANDMARK_INDICES[tip_name]]
                mcp = landmarks[self.LANDMARK_INDICES[mcp_name]]
                finger_curled = tip[1] >= mcp[1]
                fingers_curled.append(finger_curled)

            # Thumb can be curled or neutral
            thumb_tip = landmarks[self.LANDMARK_INDICES['THUMB_TIP']]
            thumb_mcp = landmarks[self.LANDMARK_INDICES['THUMB_MCP']]
            wrist = landmarks[self.LANDMARK_INDICES['WRIST']]
            thumb_neutral = self._distance(thumb_tip, wrist) <= self._distance(thumb_mcp, wrist) * 1.2

            # Calculate confidence
            conditions = [index_extended] + fingers_curled + [thumb_neutral]
            confidence = sum(conditions) / len(conditions)

            return confidence

        except Exception as e:
            logger.error(f"Error checking pointing: {e}")
            return 0.0

    def _distance(self, point1: tuple, point2: tuple) -> float:
        """Calculate Euclidean distance between two points."""
        return math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)

    def get_supported_gestures(self) -> List[GestureType]:
        """Get list of supported pre-defined gesture types."""
        return [gesture for gesture in GestureType if gesture not in [GestureType.UNKNOWN, GestureType.CUSTOM]]

    def get_all_gestures(self) -> Dict[str, str]:
        """
        Get all available gestures (pre-defined and custom).

        Returns:
            Dictionary mapping gesture identifier to display name
        """
        gestures = {}

        # Add pre-defined gestures
        for gesture_type in self.get_supported_gestures():
            gestures[gesture_type.value] = gesture_type.value.replace('_', ' ').title()

        # Add custom gestures
        if self.custom_gesture_manager:
            enabled_gestures = self.custom_gesture_manager.get_enabled_gestures()
            for gesture_id, gesture_data in enabled_gestures.items():
                if gesture_data.model_path:  # Only include trained gestures
                    gestures[gesture_id] = gesture_data.name

        return gestures

    def reload_custom_gestures(self):
        """Reload custom gesture models (call after gestures are added/removed)."""
        if self.custom_gesture_manager and self.gesture_trainer:
            self.load_custom_models()
            logger.info("Reloaded custom gesture models")
