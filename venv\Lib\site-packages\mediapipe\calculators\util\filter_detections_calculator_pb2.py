# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/util/filter_detections_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n=mediapipe/calculators/util/filter_detections_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\xc3\x01\n!FilterDetectionsCalculatorOptions\x12\x11\n\tmin_score\x18\x01 \x01(\x02\x12\x16\n\x0emin_pixel_size\x18\x02 \x01(\x02\x12\x16\n\x0emax_pixel_size\x18\x03 \x01(\x02\x32[\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xf4\x88\xca\xbc\x01 \x01(\x0b\x32,.mediapipe.FilterDetectionsCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.util.filter_detections_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_FILTERDETECTIONSCALCULATOROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_FILTERDETECTIONSCALCULATOROPTIONS']._serialized_start=115
  _globals['_FILTERDETECTIONSCALCULATOROPTIONS']._serialized_end=310
# @@protoc_insertion_point(module_scope)
