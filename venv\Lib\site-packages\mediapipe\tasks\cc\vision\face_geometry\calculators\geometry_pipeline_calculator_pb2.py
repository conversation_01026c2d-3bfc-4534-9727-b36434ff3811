# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/vision/face_geometry/calculators/geometry_pipeline_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_options_pb2 as mediapipe_dot_framework_dot_calculator__options__pb2
from mediapipe.tasks.cc.core.proto import external_file_pb2 as mediapipe_dot_tasks_dot_cc_dot_core_dot_proto_dot_external__file__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nVmediapipe/tasks/cc/vision/face_geometry/calculators/geometry_pipeline_calculator.proto\x12$mediapipe.tasks.vision.face_geometry\x1a,mediapipe/framework/calculator_options.proto\x1a\x31mediapipe/tasks/cc/core/proto/external_file.proto\"\xe4\x01\n%FaceGeometryPipelineCalculatorOptions\x12?\n\rmetadata_file\x18\x01 \x01(\x0b\x32(.mediapipe.tasks.core.proto.ExternalFile2z\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\x80\xbc\xb0\xf4\x01 \x01(\x0b\x32K.mediapipe.tasks.vision.face_geometry.FaceGeometryPipelineCalculatorOptionsBn\<EMAIL>*FaceGeometryPipelineCalculatorOptionsProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.vision.face_geometry.calculators.geometry_pipeline_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_FACEGEOMETRYPIPELINECALCULATOROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\<EMAIL>*FaceGeometryPipelineCalculatorOptionsProto'
  _globals['_FACEGEOMETRYPIPELINECALCULATOROPTIONS']._serialized_start=226
  _globals['_FACEGEOMETRYPIPELINECALCULATOROPTIONS']._serialized_end=454
# @@protoc_insertion_point(module_scope)
