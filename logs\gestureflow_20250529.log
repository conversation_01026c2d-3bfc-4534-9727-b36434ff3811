2025-05-29 20:24:12,141 - GestureFlow - INFO - Using MediaPipe-based hand tracking
2025-05-29 20:24:12,192 - GestureFlow - INFO - Starting GestureFlow application
2025-05-29 20:24:12,192 - GestureFlow - INFO - Python version: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
2025-05-29 20:24:12,192 - GestureFlow - INFO - Qt version: 1.0.0
2025-05-29 20:24:12,196 - GestureFlow - INFO - WebcamManager initialized with camera 0, target FPS: 30
2025-05-29 20:24:12,202 - GestureFlow - INFO - HandTracker initialized: max_hands=2, detection_conf=0.7, tracking_conf=0.5
2025-05-29 20:24:12,202 - GestureFlow - INFO - StaticGestureRecognizer initialized with threshold: 0.7
2025-05-29 20:24:12,203 - GestureFlow - INFO - PerformanceMonitor initialized with history size: 100
2025-05-29 20:24:12,227 - GestureFlow - INFO - <PERSON><PERSON><PERSON><PERSON> initialized
2025-05-29 20:24:13,090 - GestureFlow - INFO - GestureFlow application started successfully
2025-05-29 20:24:20,676 - GestureFlow - INFO - Camera opened: 640x480 @ 30.0 FPS
2025-05-29 20:24:20,676 - GestureFlow - INFO - Performance monitoring started
2025-05-29 20:24:20,677 - GestureFlow - INFO - Gesture recognition started from UI
2025-05-29 20:24:20,677 - GestureFlow - INFO - Gesture recognition thread started
2025-05-29 20:25:54,151 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 20:25:54,741 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 20:26:03,869 - GestureFlow - WARNING - Failed to read frame from webcam
2025-05-29 20:26:03,870 - GestureFlow - INFO - Gesture recognition thread stopped
2025-05-29 20:26:03,870 - GestureFlow - INFO - Gesture recognition stopped from UI
2025-05-29 20:26:03,883 - GestureFlow - INFO - Application closed
2025-05-29 20:26:03,901 - GestureFlow - INFO - Application closed
2025-05-29 20:26:03,903 - GestureFlow - INFO - GestureFlow application exited with code: 0
2025-05-29 20:26:03,909 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 20:26:03,920 - GestureFlow - INFO - HandTracker cleaned up
2025-05-29 20:26:03,920 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 20:28:19,312 - GestureFlow - INFO - Using MediaPipe-based hand tracking
2025-05-29 20:28:19,323 - GestureFlow - INFO - Starting GestureFlow application
2025-05-29 20:28:19,324 - GestureFlow - INFO - Python version: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
2025-05-29 20:28:19,324 - GestureFlow - INFO - Qt version: 1.0.0
2025-05-29 20:28:19,324 - GestureFlow - INFO - WebcamManager initialized with camera 0, target FPS: 30
2025-05-29 20:28:19,330 - GestureFlow - INFO - HandTracker initialized: max_hands=2, detection_conf=0.7, tracking_conf=0.5
2025-05-29 20:28:19,330 - GestureFlow - INFO - StaticGestureRecognizer initialized with threshold: 0.7
2025-05-29 20:28:19,330 - GestureFlow - INFO - PerformanceMonitor initialized with history size: 100
2025-05-29 20:28:19,337 - GestureFlow - INFO - MainWindow initialized
2025-05-29 20:28:19,669 - GestureFlow - INFO - GestureFlow application started successfully
2025-05-29 20:28:28,330 - GestureFlow - INFO - Camera opened: 640x480 @ 30.0 FPS
2025-05-29 20:28:28,330 - GestureFlow - INFO - Performance monitoring started
2025-05-29 20:28:28,330 - GestureFlow - INFO - Gesture recognition started from UI
2025-05-29 20:28:28,330 - GestureFlow - INFO - Gesture recognition thread started
2025-05-29 20:28:55,622 - GestureFlow - INFO - Gesture recognition thread stopped
2025-05-29 20:28:55,877 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 20:28:56,251 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 20:28:56,252 - GestureFlow - INFO - Gesture recognition stopped from UI
2025-05-29 20:28:58,658 - GestureFlow - INFO - Application closed
2025-05-29 20:28:58,680 - GestureFlow - INFO - GestureFlow application exited with code: 0
2025-05-29 20:28:58,688 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 20:28:58,697 - GestureFlow - INFO - HandTracker cleaned up
2025-05-29 20:28:58,698 - GestureFlow - INFO - Performance monitoring stopped
