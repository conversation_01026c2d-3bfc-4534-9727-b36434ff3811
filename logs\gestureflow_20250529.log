2025-05-29 20:24:12,141 - GestureFlow - INFO - Using MediaPipe-based hand tracking
2025-05-29 20:24:12,192 - GestureFlow - INFO - Starting GestureFlow application
2025-05-29 20:24:12,192 - GestureFlow - INFO - Python version: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
2025-05-29 20:24:12,192 - GestureFlow - INFO - Qt version: 1.0.0
2025-05-29 20:24:12,196 - GestureFlow - INFO - WebcamManager initialized with camera 0, target FPS: 30
2025-05-29 20:24:12,202 - GestureFlow - INFO - HandTracker initialized: max_hands=2, detection_conf=0.7, tracking_conf=0.5
2025-05-29 20:24:12,202 - GestureFlow - INFO - StaticGestureRecognizer initialized with threshold: 0.7
2025-05-29 20:24:12,203 - GestureFlow - INFO - PerformanceMonitor initialized with history size: 100
2025-05-29 20:24:12,227 - GestureFlow - INFO - <PERSON><PERSON><PERSON><PERSON> initialized
2025-05-29 20:24:13,090 - GestureFlow - INFO - GestureFlow application started successfully
2025-05-29 20:24:20,676 - GestureFlow - INFO - Camera opened: 640x480 @ 30.0 FPS
2025-05-29 20:24:20,676 - GestureFlow - INFO - Performance monitoring started
2025-05-29 20:24:20,677 - GestureFlow - INFO - Gesture recognition started from UI
2025-05-29 20:24:20,677 - GestureFlow - INFO - Gesture recognition thread started
2025-05-29 20:25:54,151 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 20:25:54,741 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 20:26:03,869 - GestureFlow - WARNING - Failed to read frame from webcam
2025-05-29 20:26:03,870 - GestureFlow - INFO - Gesture recognition thread stopped
2025-05-29 20:26:03,870 - GestureFlow - INFO - Gesture recognition stopped from UI
2025-05-29 20:26:03,883 - GestureFlow - INFO - Application closed
2025-05-29 20:26:03,901 - GestureFlow - INFO - Application closed
2025-05-29 20:26:03,903 - GestureFlow - INFO - GestureFlow application exited with code: 0
2025-05-29 20:26:03,909 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 20:26:03,920 - GestureFlow - INFO - HandTracker cleaned up
2025-05-29 20:26:03,920 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 20:28:19,312 - GestureFlow - INFO - Using MediaPipe-based hand tracking
2025-05-29 20:28:19,323 - GestureFlow - INFO - Starting GestureFlow application
2025-05-29 20:28:19,324 - GestureFlow - INFO - Python version: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
2025-05-29 20:28:19,324 - GestureFlow - INFO - Qt version: 1.0.0
2025-05-29 20:28:19,324 - GestureFlow - INFO - WebcamManager initialized with camera 0, target FPS: 30
2025-05-29 20:28:19,330 - GestureFlow - INFO - HandTracker initialized: max_hands=2, detection_conf=0.7, tracking_conf=0.5
2025-05-29 20:28:19,330 - GestureFlow - INFO - StaticGestureRecognizer initialized with threshold: 0.7
2025-05-29 20:28:19,330 - GestureFlow - INFO - PerformanceMonitor initialized with history size: 100
2025-05-29 20:28:19,337 - GestureFlow - INFO - MainWindow initialized
2025-05-29 20:28:19,669 - GestureFlow - INFO - GestureFlow application started successfully
2025-05-29 20:28:28,330 - GestureFlow - INFO - Camera opened: 640x480 @ 30.0 FPS
2025-05-29 20:28:28,330 - GestureFlow - INFO - Performance monitoring started
2025-05-29 20:28:28,330 - GestureFlow - INFO - Gesture recognition started from UI
2025-05-29 20:28:28,330 - GestureFlow - INFO - Gesture recognition thread started
2025-05-29 20:28:55,622 - GestureFlow - INFO - Gesture recognition thread stopped
2025-05-29 20:28:55,877 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 20:28:56,251 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 20:28:56,252 - GestureFlow - INFO - Gesture recognition stopped from UI
2025-05-29 20:28:58,658 - GestureFlow - INFO - Application closed
2025-05-29 20:28:58,680 - GestureFlow - INFO - GestureFlow application exited with code: 0
2025-05-29 20:28:58,688 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 20:28:58,697 - GestureFlow - INFO - HandTracker cleaned up
2025-05-29 20:28:58,698 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 20:50:08,026 - GestureFlow - INFO - Created directory: test_data\custom_gestures
2025-05-29 20:50:08,027 - GestureFlow - INFO - Created directory: test_data\models
2025-05-29 20:50:08,027 - GestureFlow - INFO - Loaded 0 custom gestures
2025-05-29 20:50:08,027 - GestureFlow - INFO - CustomGestureManager initialized with 0 gestures
2025-05-29 20:50:08,028 - GestureFlow - INFO - Created new custom gesture: Test Wave (ID: 77709c5a-daae-4d48-9641-c7d74709e69e)
2025-05-29 20:50:08,029 - GestureFlow - INFO - Added training sample to gesture Test Wave (total samples: 1)
2025-05-29 20:50:08,030 - GestureFlow - INFO - Added training sample to gesture Test Wave (total samples: 2)
2025-05-29 20:50:08,031 - GestureFlow - INFO - Added training sample to gesture Test Wave (total samples: 3)
2025-05-29 20:50:08,032 - GestureFlow - INFO - Added training sample to gesture Test Wave (total samples: 4)
2025-05-29 20:50:08,033 - GestureFlow - INFO - Added training sample to gesture Test Wave (total samples: 5)
2025-05-29 20:50:08,034 - GestureFlow - INFO - Added training sample to gesture Test Wave (total samples: 6)
2025-05-29 20:50:08,035 - GestureFlow - INFO - Added training sample to gesture Test Wave (total samples: 7)
2025-05-29 20:50:08,037 - GestureFlow - INFO - Added training sample to gesture Test Wave (total samples: 8)
2025-05-29 20:50:08,039 - GestureFlow - INFO - Added training sample to gesture Test Wave (total samples: 9)
2025-05-29 20:50:08,041 - GestureFlow - INFO - Added training sample to gesture Test Wave (total samples: 10)
2025-05-29 20:50:08,041 - GestureFlow - INFO - HandFeatureExtractor initialized
2025-05-29 20:50:08,042 - GestureFlow - INFO - Loaded 1 custom gestures
2025-05-29 20:50:08,043 - GestureFlow - INFO - CustomGestureManager initialized with 1 gestures
2025-05-29 20:50:08,043 - GestureFlow - INFO - HandFeatureExtractor initialized
2025-05-29 20:50:08,043 - GestureFlow - INFO - GestureTrainer initialized
2025-05-29 20:50:08,044 - GestureFlow - INFO - Created new custom gesture: Test Gesture (ID: 6df5dfcc-a15c-4217-bb6f-9707f3ae7176)
2025-05-29 20:50:08,045 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 1)
2025-05-29 20:50:08,045 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 2)
2025-05-29 20:50:08,046 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 3)
2025-05-29 20:50:08,047 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 4)
2025-05-29 20:50:08,049 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 5)
2025-05-29 20:50:08,050 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 6)
2025-05-29 20:50:08,052 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 7)
2025-05-29 20:50:08,053 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 8)
2025-05-29 20:50:08,055 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 9)
2025-05-29 20:50:08,057 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 10)
2025-05-29 20:50:08,059 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 11)
2025-05-29 20:50:08,061 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 12)
2025-05-29 20:50:08,064 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 13)
2025-05-29 20:50:08,066 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 14)
2025-05-29 20:50:08,069 - GestureFlow - INFO - Added training sample to gesture Test Gesture (total samples: 15)
2025-05-29 20:50:08,069 - GestureFlow - INFO - Extracting features for gesture: Test Gesture
2025-05-29 20:50:08,405 - GestureFlow - INFO - Trained model for Test Gesture: accuracy=1.000, samples=15
2025-05-29 20:50:08,407 - GestureFlow - INFO - Loaded model from test_data\models\6df5dfcc-a15c-4217-bb6f-9707f3ae7176_random_forest.pkl
2025-05-29 20:50:08,413 - GestureFlow - INFO - Loaded 2 custom gestures
2025-05-29 20:50:08,413 - GestureFlow - INFO - CustomGestureManager initialized with 2 gestures
2025-05-29 20:50:08,413 - GestureFlow - INFO - HandFeatureExtractor initialized
2025-05-29 20:50:08,414 - GestureFlow - INFO - GestureAmbiguityDetector initialized with threshold: 0.8
2025-05-29 20:50:08,414 - GestureFlow - INFO - Created new custom gesture: Similar Gesture 1 (ID: 411051c3-8fb9-442c-a511-53f5a45d2b74)
2025-05-29 20:50:08,416 - GestureFlow - INFO - Created new custom gesture: Similar Gesture 2 (ID: 6b691777-39bf-4dcd-a70a-b4f9bcd1e318)
2025-05-29 20:50:08,416 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 1 (total samples: 1)
2025-05-29 20:50:08,417 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 1 (total samples: 2)
2025-05-29 20:50:08,418 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 1 (total samples: 3)
2025-05-29 20:50:08,419 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 1 (total samples: 4)
2025-05-29 20:50:08,420 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 1 (total samples: 5)
2025-05-29 20:50:08,421 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 1 (total samples: 6)
2025-05-29 20:50:08,423 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 1 (total samples: 7)
2025-05-29 20:50:08,424 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 1 (total samples: 8)
2025-05-29 20:50:08,426 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 1 (total samples: 9)
2025-05-29 20:50:08,428 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 1 (total samples: 10)
2025-05-29 20:50:08,429 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 2 (total samples: 1)
2025-05-29 20:50:08,429 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 2 (total samples: 2)
2025-05-29 20:50:08,430 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 2 (total samples: 3)
2025-05-29 20:50:08,432 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 2 (total samples: 4)
2025-05-29 20:50:08,433 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 2 (total samples: 5)
2025-05-29 20:50:08,434 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 2 (total samples: 6)
2025-05-29 20:50:08,436 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 2 (total samples: 7)
2025-05-29 20:50:08,437 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 2 (total samples: 8)
2025-05-29 20:50:08,439 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 2 (total samples: 9)
2025-05-29 20:50:08,441 - GestureFlow - INFO - Added training sample to gesture Similar Gesture 2 (total samples: 10)
2025-05-29 20:50:08,445 - GestureFlow - INFO - Analyzed 4 gestures, found 6 potential conflicts
2025-05-29 20:50:32,567 - GestureFlow - INFO - Using MediaPipe-based hand tracking
2025-05-29 20:50:32,581 - GestureFlow - INFO - Starting GestureFlow application
2025-05-29 20:50:32,582 - GestureFlow - INFO - Python version: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
2025-05-29 20:50:32,582 - GestureFlow - INFO - Qt version: 1.0.0
2025-05-29 20:50:32,582 - GestureFlow - INFO - WebcamManager initialized with camera 0, target FPS: 30
2025-05-29 20:50:32,589 - GestureFlow - INFO - HandTracker initialized: max_hands=2, detection_conf=0.7, tracking_conf=0.5
2025-05-29 20:50:32,589 - GestureFlow - INFO - StaticGestureRecognizer initialized with threshold: 0.7
2025-05-29 20:50:32,589 - GestureFlow - INFO - PerformanceMonitor initialized with history size: 100
2025-05-29 20:50:32,590 - GestureFlow - INFO - Created directory: data
2025-05-29 20:50:32,590 - GestureFlow - INFO - Created directory: data\custom_gestures
2025-05-29 20:50:32,591 - GestureFlow - INFO - Created directory: data\models
2025-05-29 20:50:32,591 - GestureFlow - INFO - Loaded 0 custom gestures
2025-05-29 20:50:32,591 - GestureFlow - INFO - CustomGestureManager initialized with 0 gestures
2025-05-29 20:50:32,591 - GestureFlow - INFO - HandFeatureExtractor initialized
2025-05-29 20:50:32,592 - GestureFlow - INFO - GestureTrainer initialized
2025-05-29 20:50:32,592 - GestureFlow - INFO - Loaded 0 custom gesture models
2025-05-29 20:50:32,592 - GestureFlow - INFO - Custom gesture support enabled
2025-05-29 20:50:32,598 - GestureFlow - INFO - MainWindow initialized
2025-05-29 20:50:33,419 - GestureFlow - INFO - GestureFlow application started successfully
2025-05-29 20:50:41,723 - GestureFlow - INFO - Camera opened: 640x480 @ 30.0 FPS
2025-05-29 20:50:41,724 - GestureFlow - INFO - Performance monitoring started
2025-05-29 20:50:41,725 - GestureFlow - INFO - Gesture recognition started from UI
2025-05-29 20:50:41,725 - GestureFlow - INFO - Gesture recognition thread started
2025-05-29 20:51:07,610 - GestureFlow - INFO - Refreshed gesture list: 0 gestures
2025-05-29 20:51:07,610 - GestureFlow - INFO - GestureManagementDialog initialized
2025-05-29 20:51:16,376 - GestureFlow - INFO - CustomGestureRecordingDialog initialized
2025-05-29 20:52:16,005 - GestureFlow - INFO - Created new custom gesture: pointer (ID: 272f2c18-3af7-4243-b90b-10382cf045de)
2025-05-29 20:52:16,005 - GestureFlow - INFO - Started recording gesture: pointer
2025-05-29 20:52:20,002 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 1)
2025-05-29 20:52:26,002 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 2)
2025-05-29 20:52:32,001 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 3)
2025-05-29 20:52:38,003 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 4)
2025-05-29 20:52:44,008 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 5)
2025-05-29 20:52:50,002 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 6)
2025-05-29 20:52:56,006 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 7)
2025-05-29 20:53:02,008 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 8)
2025-05-29 20:53:08,013 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 9)
2025-05-29 20:53:14,017 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 10)
2025-05-29 20:53:14,017 - GestureFlow - INFO - Stopped recording gesture
2025-05-29 20:53:22,435 - GestureFlow - INFO - Extracting features for gesture: pointer
2025-05-29 20:53:22,823 - GestureFlow - INFO - Trained model for pointer: accuracy=1.000, samples=10
2025-05-29 20:53:30,114 - GestureFlow - INFO - Loaded model from data/models\272f2c18-3af7-4243-b90b-10382cf045de_random_forest.pkl
2025-05-29 20:53:30,114 - GestureFlow - INFO - Loaded custom gesture model: pointer
2025-05-29 20:53:30,115 - GestureFlow - INFO - Loaded 1 custom gesture models
2025-05-29 20:53:30,115 - GestureFlow - INFO - Reloaded custom gesture models
2025-05-29 20:53:30,115 - GestureFlow - INFO - New gesture created: 272f2c18-3af7-4243-b90b-10382cf045de
2025-05-29 20:53:30,115 - GestureFlow - INFO - Gesture training completed: success=True, accuracy=1.000
2025-05-29 20:53:51,304 - GestureFlow - ERROR - Error opening new gesture dialog: name 'QDialog' is not defined
2025-05-29 20:54:11,913 - GestureFlow - INFO - Refreshed gesture list: 1 gestures
2025-05-29 20:54:11,914 - GestureFlow - INFO - GestureManagementDialog initialized
2025-05-29 20:56:50,785 - GestureFlow - INFO - Using MediaPipe-based hand tracking
2025-05-29 20:56:50,829 - GestureFlow - INFO - Starting GestureFlow application
2025-05-29 20:56:50,830 - GestureFlow - INFO - Python version: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
2025-05-29 20:56:50,830 - GestureFlow - INFO - Qt version: 1.0.0
2025-05-29 20:56:50,834 - GestureFlow - INFO - WebcamManager initialized with camera 0, target FPS: 30
2025-05-29 20:56:50,855 - GestureFlow - INFO - HandTracker initialized: max_hands=2, detection_conf=0.7, tracking_conf=0.5
2025-05-29 20:56:50,855 - GestureFlow - INFO - StaticGestureRecognizer initialized with threshold: 0.7
2025-05-29 20:56:50,855 - GestureFlow - INFO - PerformanceMonitor initialized with history size: 100
2025-05-29 20:56:50,857 - GestureFlow - INFO - Loaded 1 custom gestures
2025-05-29 20:56:50,857 - GestureFlow - INFO - CustomGestureManager initialized with 1 gestures
2025-05-29 20:56:50,857 - GestureFlow - INFO - HandFeatureExtractor initialized
2025-05-29 20:56:50,857 - GestureFlow - INFO - GestureTrainer initialized
2025-05-29 20:56:50,860 - GestureFlow - INFO - Loaded model from data/models\272f2c18-3af7-4243-b90b-10382cf045de_random_forest.pkl
2025-05-29 20:56:50,860 - GestureFlow - INFO - Loaded custom gesture model: pointer
2025-05-29 20:56:50,860 - GestureFlow - INFO - Loaded 1 custom gesture models
2025-05-29 20:56:50,861 - GestureFlow - INFO - Custom gesture support enabled
2025-05-29 20:56:50,891 - GestureFlow - INFO - MainWindow initialized
2025-05-29 20:56:51,649 - GestureFlow - INFO - GestureFlow application started successfully
2025-05-29 20:56:58,342 - GestureFlow - INFO - Camera opened: 640x480 @ 30.0 FPS
2025-05-29 20:56:58,343 - GestureFlow - INFO - Performance monitoring started
2025-05-29 20:56:58,344 - GestureFlow - INFO - Gesture recognition started from UI
2025-05-29 20:56:58,344 - GestureFlow - INFO - Gesture recognition thread started
2025-05-29 20:57:16,489 - GestureFlow - INFO - Refreshed gesture list: 1 gestures
2025-05-29 20:57:16,489 - GestureFlow - INFO - GestureManagementDialog initialized
2025-05-29 20:58:37,033 - GestureFlow - INFO - Using MediaPipe-based hand tracking
2025-05-29 20:58:37,044 - GestureFlow - INFO - Starting GestureFlow application
2025-05-29 20:58:37,044 - GestureFlow - INFO - Python version: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
2025-05-29 20:58:37,045 - GestureFlow - INFO - Qt version: 1.0.0
2025-05-29 20:58:37,045 - GestureFlow - INFO - WebcamManager initialized with camera 0, target FPS: 30
2025-05-29 20:58:37,051 - GestureFlow - INFO - HandTracker initialized: max_hands=2, detection_conf=0.7, tracking_conf=0.5
2025-05-29 20:58:37,051 - GestureFlow - INFO - StaticGestureRecognizer initialized with threshold: 0.7
2025-05-29 20:58:37,052 - GestureFlow - INFO - PerformanceMonitor initialized with history size: 100
2025-05-29 20:58:37,053 - GestureFlow - INFO - Loaded 1 custom gestures
2025-05-29 20:58:37,053 - GestureFlow - INFO - CustomGestureManager initialized with 1 gestures
2025-05-29 20:58:37,053 - GestureFlow - INFO - HandFeatureExtractor initialized
2025-05-29 20:58:37,053 - GestureFlow - INFO - GestureTrainer initialized
2025-05-29 20:58:37,055 - GestureFlow - INFO - Loaded model from data/models\272f2c18-3af7-4243-b90b-10382cf045de_random_forest.pkl
2025-05-29 20:58:37,055 - GestureFlow - INFO - Loaded custom gesture model: pointer
2025-05-29 20:58:37,055 - GestureFlow - INFO - Loaded 1 custom gesture models
2025-05-29 20:58:37,055 - GestureFlow - INFO - Custom gesture support enabled
2025-05-29 20:58:37,061 - GestureFlow - INFO - MainWindow initialized
2025-05-29 20:58:37,499 - GestureFlow - INFO - GestureFlow application started successfully
2025-05-29 20:58:42,924 - GestureFlow - INFO - Camera opened: 640x480 @ 30.0 FPS
2025-05-29 20:58:42,925 - GestureFlow - INFO - Performance monitoring started
2025-05-29 20:58:42,925 - GestureFlow - INFO - Gesture recognition started from UI
2025-05-29 20:58:42,925 - GestureFlow - INFO - Gesture recognition thread started
2025-05-29 20:58:54,216 - GestureFlow - INFO - Refreshed gesture list: 1 gestures
2025-05-29 20:58:54,216 - GestureFlow - INFO - GestureManagementDialog initialized
2025-05-29 20:59:31,356 - GestureFlow - INFO - Refreshed gesture list: 1 gestures
2025-05-29 20:59:37,038 - GestureFlow - INFO - Refreshed gesture list: 1 gestures
2025-05-29 20:59:37,038 - GestureFlow - INFO - Loaded 0 custom gesture models
2025-05-29 20:59:37,038 - GestureFlow - INFO - Reloaded custom gesture models
2025-05-29 20:59:37,039 - GestureFlow - INFO - Gesture updated: 
2025-05-29 20:59:37,039 - GestureFlow - INFO - Disabled gesture: pointer
2025-05-29 20:59:44,030 - GestureFlow - INFO - Refreshed gesture list: 1 gestures
2025-05-29 20:59:44,032 - GestureFlow - INFO - Loaded model from data/models\272f2c18-3af7-4243-b90b-10382cf045de_random_forest.pkl
2025-05-29 20:59:44,032 - GestureFlow - INFO - Loaded custom gesture model: pointer
2025-05-29 20:59:44,032 - GestureFlow - INFO - Loaded 1 custom gesture models
2025-05-29 20:59:44,032 - GestureFlow - INFO - Reloaded custom gesture models
2025-05-29 20:59:44,033 - GestureFlow - INFO - Gesture updated: 
2025-05-29 20:59:44,033 - GestureFlow - INFO - Enabled gesture: pointer
2025-05-29 21:00:07,816 - GestureFlow - INFO - Deleted custom gesture: pointer (ID: 272f2c18-3af7-4243-b90b-10382cf045de)
2025-05-29 21:00:08,936 - GestureFlow - INFO - Loaded 0 custom gesture models
2025-05-29 21:00:08,937 - GestureFlow - INFO - Reloaded custom gesture models
2025-05-29 21:00:08,937 - GestureFlow - INFO - Gesture deleted: 272f2c18-3af7-4243-b90b-10382cf045de
2025-05-29 21:00:08,937 - GestureFlow - INFO - Refreshed gesture list: 0 gestures
2025-05-29 21:00:08,937 - GestureFlow - INFO - Deleted gesture: pointer
2025-05-29 21:00:26,791 - GestureFlow - INFO - Gesture recognition thread stopped
2025-05-29 21:00:27,038 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 21:00:27,457 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 21:00:27,457 - GestureFlow - INFO - Gesture recognition stopped from UI
2025-05-29 21:00:27,458 - GestureFlow - INFO - Application closed
2025-05-29 21:00:27,491 - GestureFlow - INFO - GestureFlow application exited with code: 0
2025-05-29 21:00:27,497 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 21:00:27,509 - GestureFlow - INFO - HandTracker cleaned up
2025-05-29 21:00:27,509 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 21:05:46,780 - GestureFlow - INFO - Using MediaPipe-based hand tracking
2025-05-29 21:05:46,791 - GestureFlow - INFO - Starting GestureFlow application
2025-05-29 21:05:46,791 - GestureFlow - INFO - Python version: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
2025-05-29 21:05:46,791 - GestureFlow - INFO - Qt version: 1.0.0
2025-05-29 21:05:46,791 - GestureFlow - INFO - WebcamManager initialized with camera 0, target FPS: 30
2025-05-29 21:05:46,797 - GestureFlow - INFO - HandTracker initialized: max_hands=2, detection_conf=0.7, tracking_conf=0.5
2025-05-29 21:05:46,798 - GestureFlow - INFO - StaticGestureRecognizer initialized with threshold: 0.7
2025-05-29 21:05:46,798 - GestureFlow - INFO - PerformanceMonitor initialized with history size: 100
2025-05-29 21:05:46,799 - GestureFlow - INFO - Loaded 0 custom gestures
2025-05-29 21:05:46,799 - GestureFlow - INFO - CustomGestureManager initialized with 0 gestures
2025-05-29 21:05:46,799 - GestureFlow - INFO - HandFeatureExtractor initialized
2025-05-29 21:05:46,799 - GestureFlow - INFO - GestureTrainer initialized
2025-05-29 21:05:46,799 - GestureFlow - INFO - Loaded 0 custom gesture models
2025-05-29 21:05:46,799 - GestureFlow - INFO - Custom gesture support enabled
2025-05-29 21:05:46,805 - GestureFlow - INFO - MainWindow initialized
2025-05-29 21:05:47,158 - GestureFlow - INFO - GestureFlow application started successfully
2025-05-29 21:05:52,434 - GestureFlow - INFO - Camera opened: 640x480 @ 30.0 FPS
2025-05-29 21:05:52,434 - GestureFlow - INFO - Performance monitoring started
2025-05-29 21:05:52,435 - GestureFlow - INFO - Gesture recognition started from UI
2025-05-29 21:05:52,435 - GestureFlow - INFO - Gesture recognition thread started
2025-05-29 21:05:55,287 - GestureFlow - INFO - Refreshed gesture list: 0 gestures
2025-05-29 21:05:55,288 - GestureFlow - INFO - GestureManagementDialog initialized
2025-05-29 21:05:59,612 - GestureFlow - INFO - Gesture recognition thread stopped
2025-05-29 21:05:59,823 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 21:06:00,323 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 21:06:00,323 - GestureFlow - INFO - Gesture recognition stopped from UI
2025-05-29 21:06:00,323 - GestureFlow - INFO - Application closed
2025-05-29 21:06:00,350 - GestureFlow - INFO - GestureFlow application exited with code: 0
2025-05-29 21:06:00,356 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 21:06:00,365 - GestureFlow - INFO - HandTracker cleaned up
2025-05-29 21:06:00,365 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 21:06:18,371 - GestureFlow - INFO - Using MediaPipe-based hand tracking
2025-05-29 21:06:18,382 - GestureFlow - INFO - Starting GestureFlow application
2025-05-29 21:06:18,382 - GestureFlow - INFO - Python version: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
2025-05-29 21:06:18,382 - GestureFlow - INFO - Qt version: 1.0.0
2025-05-29 21:06:18,383 - GestureFlow - INFO - WebcamManager initialized with camera 0, target FPS: 30
2025-05-29 21:06:18,388 - GestureFlow - INFO - HandTracker initialized: max_hands=2, detection_conf=0.7, tracking_conf=0.5
2025-05-29 21:06:18,389 - GestureFlow - INFO - StaticGestureRecognizer initialized with threshold: 0.7
2025-05-29 21:06:18,389 - GestureFlow - INFO - PerformanceMonitor initialized with history size: 100
2025-05-29 21:06:18,389 - GestureFlow - INFO - Loaded 0 custom gestures
2025-05-29 21:06:18,389 - GestureFlow - INFO - CustomGestureManager initialized with 0 gestures
2025-05-29 21:06:18,390 - GestureFlow - INFO - HandFeatureExtractor initialized
2025-05-29 21:06:18,390 - GestureFlow - INFO - GestureTrainer initialized
2025-05-29 21:06:18,390 - GestureFlow - INFO - Loaded 0 custom gesture models
2025-05-29 21:06:18,390 - GestureFlow - INFO - Custom gesture support enabled
2025-05-29 21:06:18,395 - GestureFlow - INFO - MainWindow initialized
2025-05-29 21:06:18,757 - GestureFlow - INFO - GestureFlow application started successfully
2025-05-29 21:06:23,796 - GestureFlow - INFO - Camera opened: 640x480 @ 30.0 FPS
2025-05-29 21:06:23,797 - GestureFlow - INFO - Performance monitoring started
2025-05-29 21:06:23,797 - GestureFlow - INFO - Gesture recognition started from UI
2025-05-29 21:06:23,797 - GestureFlow - INFO - Gesture recognition thread started
2025-05-29 21:06:28,290 - GestureFlow - INFO - Gesture recognition thread stopped
2025-05-29 21:06:28,596 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 21:06:28,657 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 21:06:28,657 - GestureFlow - INFO - Gesture recognition stopped from UI
2025-05-29 21:06:28,657 - GestureFlow - INFO - Application closed
2025-05-29 21:06:28,684 - GestureFlow - INFO - GestureFlow application exited with code: 0
2025-05-29 21:06:28,691 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 21:06:28,698 - GestureFlow - INFO - HandTracker cleaned up
2025-05-29 21:06:28,698 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 21:09:29,959 - GestureFlow - INFO - Using MediaPipe-based hand tracking
2025-05-29 21:09:29,970 - GestureFlow - INFO - Starting GestureFlow application
2025-05-29 21:09:29,970 - GestureFlow - INFO - Python version: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
2025-05-29 21:09:29,970 - GestureFlow - INFO - Qt version: 1.0.0
2025-05-29 21:09:29,971 - GestureFlow - INFO - WebcamManager initialized with camera 0, target FPS: 30
2025-05-29 21:09:29,977 - GestureFlow - INFO - HandTracker initialized: max_hands=2, detection_conf=0.7, tracking_conf=0.5
2025-05-29 21:09:29,977 - GestureFlow - INFO - StaticGestureRecognizer initialized with threshold: 0.7
2025-05-29 21:09:29,977 - GestureFlow - INFO - PerformanceMonitor initialized with history size: 100
2025-05-29 21:09:29,978 - GestureFlow - INFO - Loaded 0 custom gestures
2025-05-29 21:09:29,978 - GestureFlow - INFO - CustomGestureManager initialized with 0 gestures
2025-05-29 21:09:29,978 - GestureFlow - INFO - HandFeatureExtractor initialized
2025-05-29 21:09:29,978 - GestureFlow - INFO - GestureTrainer initialized
2025-05-29 21:09:29,978 - GestureFlow - INFO - Loaded 0 custom gesture models
2025-05-29 21:09:29,978 - GestureFlow - INFO - Custom gesture support enabled
2025-05-29 21:09:29,984 - GestureFlow - INFO - MainWindow initialized
2025-05-29 21:09:30,374 - GestureFlow - INFO - GestureFlow application started successfully
2025-05-29 21:09:45,539 - GestureFlow - INFO - Camera opened: 640x480 @ 30.0 FPS
2025-05-29 21:09:45,539 - GestureFlow - INFO - Performance monitoring started
2025-05-29 21:09:45,540 - GestureFlow - INFO - Gesture recognition started from UI
2025-05-29 21:09:45,540 - GestureFlow - INFO - Gesture recognition thread started
2025-05-29 21:09:53,692 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 21:09:54,038 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 21:10:03,414 - GestureFlow - WARNING - Failed to read frame from webcam
2025-05-29 21:10:03,414 - GestureFlow - INFO - Gesture recognition thread stopped
2025-05-29 21:10:03,415 - GestureFlow - INFO - Gesture recognition stopped from UI
2025-05-29 21:10:03,420 - GestureFlow - INFO - Application closed
2025-05-29 21:10:03,438 - GestureFlow - INFO - GestureFlow application exited with code: 0
2025-05-29 21:10:03,442 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 21:10:03,452 - GestureFlow - INFO - HandTracker cleaned up
2025-05-29 21:10:03,453 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 21:12:04,389 - GestureFlow - INFO - Using MediaPipe-based hand tracking
2025-05-29 21:12:04,400 - GestureFlow - INFO - Starting GestureFlow application
2025-05-29 21:12:04,401 - GestureFlow - INFO - Python version: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
2025-05-29 21:12:04,401 - GestureFlow - INFO - Qt version: 1.0.0
2025-05-29 21:12:04,401 - GestureFlow - INFO - WebcamManager initialized with camera 0, target FPS: 30
2025-05-29 21:12:04,407 - GestureFlow - INFO - HandTracker initialized: max_hands=2, detection_conf=0.7, tracking_conf=0.5
2025-05-29 21:12:04,408 - GestureFlow - INFO - StaticGestureRecognizer initialized with threshold: 0.7
2025-05-29 21:12:04,408 - GestureFlow - INFO - PerformanceMonitor initialized with history size: 100
2025-05-29 21:12:04,409 - GestureFlow - INFO - Loaded 0 custom gestures
2025-05-29 21:12:04,409 - GestureFlow - INFO - CustomGestureManager initialized with 0 gestures
2025-05-29 21:12:04,409 - GestureFlow - INFO - HandFeatureExtractor initialized
2025-05-29 21:12:04,409 - GestureFlow - INFO - GestureTrainer initialized
2025-05-29 21:12:04,409 - GestureFlow - INFO - Loaded 0 custom gesture models
2025-05-29 21:12:04,409 - GestureFlow - INFO - Custom gesture support enabled
2025-05-29 21:12:04,415 - GestureFlow - INFO - MainWindow initialized
2025-05-29 21:12:04,761 - GestureFlow - INFO - GestureFlow application started successfully
2025-05-29 21:12:10,104 - GestureFlow - INFO - Camera opened: 640x480 @ 30.0 FPS
2025-05-29 21:12:10,105 - GestureFlow - INFO - Performance monitoring started
2025-05-29 21:12:10,106 - GestureFlow - INFO - Gesture recognition started from UI
2025-05-29 21:12:10,117 - GestureFlow - INFO - Gesture recognition thread started
2025-05-29 21:12:22,690 - GestureFlow - INFO - Gesture recognition thread stopped
2025-05-29 21:12:22,939 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 21:12:23,469 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 21:12:23,469 - GestureFlow - INFO - Gesture recognition stopped from UI
2025-05-29 21:12:27,965 - GestureFlow - INFO - Camera opened: 640x480 @ 30.0 FPS
2025-05-29 21:12:27,966 - GestureFlow - INFO - Performance monitoring started
2025-05-29 21:12:27,966 - GestureFlow - INFO - Gesture recognition started from UI
2025-05-29 21:12:27,966 - GestureFlow - INFO - Gesture recognition thread started
2025-05-29 21:12:31,080 - GestureFlow - INFO - CustomGestureRecordingDialog initialized
2025-05-29 21:13:16,189 - GestureFlow - INFO - Created new custom gesture: pointer (ID: 96070166-d5d9-4432-9521-be78ee37edee)
2025-05-29 21:13:16,189 - GestureFlow - INFO - Started recording gesture: pointer
2025-05-29 21:13:20,189 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 1)
2025-05-29 21:13:26,191 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 2)
2025-05-29 21:13:32,229 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 3)
2025-05-29 21:13:38,235 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 4)
2025-05-29 21:13:44,239 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 5)
2025-05-29 21:13:50,240 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 6)
2025-05-29 21:13:56,239 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 7)
2025-05-29 21:14:02,237 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 8)
2025-05-29 21:14:08,239 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 9)
2025-05-29 21:14:14,239 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 10)
2025-05-29 21:14:20,239 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 11)
2025-05-29 21:14:26,238 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 12)
2025-05-29 21:14:32,242 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 13)
2025-05-29 21:14:38,239 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 14)
2025-05-29 21:14:44,239 - GestureFlow - INFO - Added training sample to gesture pointer (total samples: 15)
2025-05-29 21:14:44,240 - GestureFlow - INFO - Stopped recording gesture
2025-05-29 21:14:49,843 - GestureFlow - INFO - Extracting features for gesture: pointer
2025-05-29 21:14:50,212 - GestureFlow - INFO - Trained model for pointer: accuracy=1.000, samples=15
2025-05-29 21:14:51,779 - GestureFlow - INFO - Loaded model from data/models\96070166-d5d9-4432-9521-be78ee37edee_random_forest.pkl
2025-05-29 21:14:51,779 - GestureFlow - INFO - Loaded custom gesture model: pointer
2025-05-29 21:14:51,780 - GestureFlow - INFO - Loaded 1 custom gesture models
2025-05-29 21:14:51,780 - GestureFlow - INFO - Reloaded custom gesture models
2025-05-29 21:14:51,780 - GestureFlow - INFO - New gesture created: 96070166-d5d9-4432-9521-be78ee37edee
2025-05-29 21:14:51,780 - GestureFlow - INFO - Gesture training completed: success=True, accuracy=1.000
2025-05-29 21:14:56,359 - GestureFlow - ERROR - Error opening new gesture dialog: name 'QDialog' is not defined
2025-05-29 21:15:35,902 - GestureFlow - INFO - Gesture recognition thread stopped
2025-05-29 21:15:36,153 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 21:15:36,218 - GestureFlow - INFO - Performance monitoring stopped
2025-05-29 21:15:36,218 - GestureFlow - INFO - Gesture recognition stopped from UI
2025-05-29 21:15:36,218 - GestureFlow - INFO - Application closed
2025-05-29 21:15:36,244 - GestureFlow - INFO - GestureFlow application exited with code: 0
2025-05-29 21:15:36,249 - GestureFlow - INFO - Webcam capture stopped
2025-05-29 21:15:36,261 - GestureFlow - INFO - HandTracker cleaned up
2025-05-29 21:15:36,261 - GestureFlow - INFO - Performance monitoring stopped
