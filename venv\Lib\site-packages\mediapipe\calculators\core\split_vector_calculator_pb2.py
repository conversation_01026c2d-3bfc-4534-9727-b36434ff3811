# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/core/split_vector_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8mediapipe/calculators/core/split_vector_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"#\n\x05Range\x12\r\n\x05\x62\x65gin\x18\x01 \x01(\x05\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x05\"\xd4\x01\n\x1cSplitVectorCalculatorOptions\x12 \n\x06ranges\x18\x01 \x03(\x0b\x32\x10.mediapipe.Range\x12\x1b\n\x0c\x65lement_only\x18\x02 \x01(\x08:\x05\x66\x61lse\x12\x1e\n\x0f\x63ombine_outputs\x18\x03 \x01(\x08:\x05\x66\x61lse2U\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\x8e\xed\xda{ \x01(\x0b\x32\'.mediapipe.SplitVectorCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.core.split_vector_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_SPLITVECTORCALCULATOROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_RANGE']._serialized_start=109
  _globals['_RANGE']._serialized_end=144
  _globals['_SPLITVECTORCALCULATOROPTIONS']._serialized_start=147
  _globals['_SPLITVECTORCALCULATOROPTIONS']._serialized_end=359
# @@protoc_insertion_point(module_scope)
