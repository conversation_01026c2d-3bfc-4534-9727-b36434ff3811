# GestureFlow - Hand Gesture Recognition System

GestureFlow is a webcam-based hand gesture computer control software that enables users to control their computers using intuitive hand gestures. This implementation covers **EPIC GFLOW-E01: Core Gesture Recognition Engine (MVP)**.

## Features Implemented

### ✅ EPIC GFLOW-E01: Core Gesture Recognition Engine (MVP)

#### ✅ GFLOW-1: Setup Project & Integrate Webcam Input
- **WebcamManager**: Handles webcam input with OpenCV
- Smooth video capture (>15 FPS target)
- Error handling for missing webcam
- Camera configuration and status monitoring

#### ✅ GFLOW-2: Integrate MediaPipe Hand Tracking
- **HandTracker**: Real-time hand landmark detection using MediaPipe
- 21-point hand landmark tracking per hand
- Support for up to 2 hands simultaneously
- Configurable detection and tracking confidence thresholds

#### ✅ GFLOW-3: Implement Pre-defined Static Gesture Recognizer
- **StaticGestureRecognizer**: Rule-based gesture recognition
- **Supported Gestures**:
  - Open Palm (all fingers extended)
  - Fist (all fingers curled)
  - Peace Sign (index and middle fingers extended)
  - Thumbs Up (thumb extended upward)
  - Pointing (index finger extended)
- Confidence-based recognition with configurable thresholds

#### ✅ GFLOW-4: Basic Performance & Accuracy Benchmarking
- **PerformanceMonitor**: Real-time performance tracking
- **Metrics Tracked**:
  - FPS (Frames Per Second)
  - CPU usage percentage
  - Memory usage percentage
  - Recognition latency (milliseconds)
  - Gesture recognition accuracy and confidence

### ✅ EPIC GFLOW-E02: User-Defined Static Gesture Customization (MVP)

#### ✅ GFLOW-5: Design Custom Static Gesture Data Structure & Storage
- **CustomGestureManager**: Manages user-defined gesture data
- JSON-based storage with unique IDs
- Training sample management and metadata tracking
- Gesture enable/disable functionality

#### ✅ GFLOW-6: Develop GUI for Custom Static Gesture Recording & Labeling
- **CustomGestureRecordingDialog**: Interactive gesture recording interface
- Real-time countdown and sample collection
- Configurable number of training samples (10-50)
- Live hand tracking feedback during recording

#### ✅ GFLOW-7: Implement Training Pipeline for Custom Static Gestures
- **GestureTrainer**: ML training pipeline with multiple algorithms
- **Supported Algorithms**: Random Forest, SVM, K-Nearest Neighbors
- **HandFeatureExtractor**: 107-dimensional feature extraction from landmarks
- Cross-validation and accuracy reporting

#### ✅ GFLOW-8: Integrate Custom Static Gestures into Recognition Engine
- Seamless integration with pre-defined gesture recognition
- Priority-based recognition (custom vs pre-defined)
- Real-time model loading and prediction
- Individual confidence thresholds per custom gesture

#### ✅ GFLOW-9: Develop GUI for Managing Custom Gestures (View, Delete)
- **GestureManagementDialog**: Comprehensive gesture management interface
- View all custom gestures with statistics
- Enable/disable gestures individually
- Delete gestures with confirmation
- Training accuracy and sample count display

#### ✅ GFLOW-10: Implement Basic Gesture Ambiguity Warning
- **GestureAmbiguityDetector**: Analyzes gesture similarity
- Cosine similarity-based conflict detection
- Warnings for high similarity between gestures
- Recommendations for improving gesture distinctiveness

## Project Structure

```
GestureFlow/
├── main.py                              # Application entry point
├── requirements.txt                     # Python dependencies
├── README.md                           # This file
├── epics.md                            # Epic definitions
├── prd.md                              # Product requirements
├── test_epic_e02.py                    # Test script for EPIC E02
├── test_installation.py               # Installation verification
├── install_windows.py                 # Windows-specific installation
├── TROUBLESHOOTING.md                  # Troubleshooting guide
├── config/
│   └── gestures.json                   # Gesture configuration
├── data/                               # Data storage (created at runtime)
│   ├── custom_gestures/                # Custom gesture data files
│   ├── models/                         # Trained ML models
│   └── custom_gestures_metadata.json  # Gesture metadata
├── src/
│   ├── core/                           # Core recognition engine
│   │   ├── webcam_manager.py           # Webcam input handling
│   │   ├── hand_tracker.py             # MediaPipe hand tracking
│   │   ├── hand_tracker_fallback.py    # Fallback OpenCV tracking
│   │   ├── gesture_recognizer.py       # Static gesture recognition
│   │   ├── performance_monitor.py      # Performance benchmarking
│   │   ├── custom_gesture_manager.py   # Custom gesture data management
│   │   ├── gesture_trainer.py          # ML training pipeline
│   │   ├── feature_extractor.py        # Hand landmark feature extraction
│   │   └── ambiguity_detector.py       # Gesture conflict detection
│   ├── ui/                             # User interface
│   │   ├── main_window.py              # Main Qt application window
│   │   ├── custom_gesture_dialog.py    # Custom gesture recording dialog
│   │   └── gesture_management_dialog.py # Gesture management interface
│   └── utils/                          # Utilities
│       └── logger.py                   # Logging configuration
├── tests/                              # Unit tests
│   └── test_gesture_recognizer.py      # Gesture recognition tests
└── logs/                               # Application logs (created at runtime)
```

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd GestureFlow
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python main.py
   ```

## Dependencies

- **opencv-python**: Webcam input and image processing
- **mediapipe**: Hand landmark detection and tracking
- **PyQt5**: Cross-platform GUI framework
- **numpy**: Numerical operations
- **scikit-learn**: Machine learning utilities
- **pyautogui**: System automation (for future action execution)
- **psutil**: System performance monitoring

## Usage

### Basic Gesture Recognition

1. **Start the Application**: Run `python main.py`
2. **Start Recognition**: Click "Start Recognition" button
3. **Perform Gestures**: Make hand gestures in front of your webcam
4. **View Results**: See recognized gestures and performance metrics in real-time
5. **Toggle Landmarks**: Use "Toggle Landmarks" to show/hide hand tracking points

### Custom Gesture Creation

1. **Start Recognition**: Ensure gesture recognition is running
2. **Create New Gesture**: Click "New Custom Gesture" button
3. **Enter Details**: Provide a unique name and optional description
4. **Record Samples**: Follow the countdown and perform your gesture consistently
5. **Train Model**: Click "Train Gesture" after recording sufficient samples
6. **Use Gesture**: Your custom gesture will now be recognized in real-time

### Gesture Management

1. **Open Manager**: Click "Manage Gestures" button
2. **View Gestures**: See all custom gestures with statistics
3. **Enable/Disable**: Toggle gestures on/off as needed
4. **Delete Gestures**: Remove unwanted gestures with confirmation
5. **Monitor Performance**: Check training accuracy and sample counts

## Performance Targets

Based on the configuration in `config/gestures.json`:

- **Target FPS**: 30 FPS
- **Max CPU Usage**: 50%
- **Max Recognition Latency**: 100ms
- **Min Accuracy**: 85%

## Testing

### Unit Tests

Run the basic unit tests:

```bash
python -m pytest tests/
# or
python tests/test_gesture_recognizer.py
```

### EPIC Testing

Test specific epic implementations:

```bash
# Test EPIC GFLOW-E02 (Custom Gestures)
python test_epic_e02.py

# Test installation and dependencies
python test_installation.py
```

### Integration Testing

The application includes comprehensive testing for:
- **Custom Gesture Manager**: Data storage and retrieval
- **Feature Extractor**: 107-dimensional feature extraction
- **Gesture Trainer**: ML model training with multiple algorithms
- **Ambiguity Detector**: Gesture conflict detection
- **End-to-End Workflow**: Complete custom gesture creation pipeline

## Architecture

### Core Components

1. **WebcamManager**: Manages camera input and video capture
2. **HandTracker**: Uses MediaPipe for hand landmark detection
3. **StaticGestureRecognizer**: Rule-based gesture classification
4. **PerformanceMonitor**: Tracks system and recognition performance
5. **MainWindow**: Qt-based user interface

### Recognition Pipeline

1. **Frame Capture**: WebcamManager captures video frames
2. **Hand Detection**: HandTracker processes frames to detect hand landmarks
3. **Gesture Recognition**: StaticGestureRecognizer analyzes landmarks to identify gestures
4. **Performance Tracking**: PerformanceMonitor records metrics
5. **UI Update**: Results displayed in real-time interface

## Configuration

Gesture recognition parameters can be configured in `config/gestures.json`:

- **Recognition Settings**: Detection confidence, tracking confidence, max hands
- **Gesture Thresholds**: Individual confidence thresholds per gesture
- **Performance Targets**: FPS, CPU usage, latency targets

## Logging

Application logs are stored in the `logs/` directory with daily rotation. Log levels can be configured in `src/utils/logger.py`.

## Next Steps

This implementation completes **EPIC GFLOW-E01** and **EPIC GFLOW-E02**. Future epics will add:

- **GFLOW-E03**: Gesture-to-action mapping and execution
- **GFLOW-E04**: Enhanced UI and profile management
- **GFLOW-E05**: Dynamic gesture support and advanced features

### Completed Epics

✅ **EPIC GFLOW-E01**: Core Gesture Recognition Engine (MVP)
- Basic hand tracking and pre-defined gesture recognition
- Performance monitoring and benchmarking
- Solid foundation for gesture-based control

✅ **EPIC GFLOW-E02**: User-Defined Static Gesture Customization (MVP)
- Complete custom gesture creation workflow
- ML-based training with multiple algorithms
- Gesture management and conflict detection
- Seamless integration with existing recognition system

## Troubleshooting

### Common Issues

1. **No camera detected**: Ensure webcam is connected and not used by other applications
2. **Poor recognition**: Ensure good lighting and clear hand visibility
3. **Low FPS**: Close other applications or reduce video resolution
4. **High CPU usage**: Adjust MediaPipe confidence thresholds

### System Requirements

- **OS**: Windows 10/11, macOS, or Linux
- **Python**: 3.7 or higher
- **Webcam**: Any standard USB webcam
- **RAM**: 4GB minimum, 8GB recommended
- **CPU**: Multi-core processor recommended for real-time processing

## License

[License information to be added]

## Contributing

[Contributing guidelines to be added]
