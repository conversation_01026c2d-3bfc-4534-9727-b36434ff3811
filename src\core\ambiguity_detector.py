"""
Gesture Ambiguity Detector for GestureFlow application.
Detects potential conflicts between gestures and warns users.
Implements GFLOW-10: Implement Basic Gesture Ambiguity Warning
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
from sklearn.metrics.pairwise import cosine_similarity
from src.core.feature_extractor import HandFeatureExtractor
from src.core.custom_gesture_manager import CustomGestureManager, CustomGestureData
from src.core.gesture_recognizer import StaticGestureRecognizer, GestureType
from src.utils.logger import logger


@dataclass
class AmbiguityWarning:
    """Data class for gesture ambiguity warnings."""
    gesture1_id: str
    gesture1_name: str
    gesture2_id: str
    gesture2_name: str
    similarity_score: float
    warning_type: str  # 'high_similarity', 'overlapping_features', 'confusion_risk'
    description: str
    recommendation: str


class GestureAmbiguityDetector:
    """
    Detects potential ambiguity between gestures.
    Analyzes feature similarity and provides warnings to users.
    """
    
    def __init__(self, similarity_threshold: float = 0.85):
        """
        Initialize ambiguity detector.
        
        Args:
            similarity_threshold: Threshold for considering gestures similar
        """
        self.similarity_threshold = similarity_threshold
        self.feature_extractor = HandFeatureExtractor()
        
        logger.info(f"GestureAmbiguityDetector initialized with threshold: {similarity_threshold}")
    
    def analyze_gesture_ambiguity(self, gesture_manager: CustomGestureManager) -> List[AmbiguityWarning]:
        """
        Analyze all gestures for potential ambiguity.
        
        Args:
            gesture_manager: Custom gesture manager instance
            
        Returns:
            List of ambiguity warnings
        """
        warnings = []
        
        try:
            # Get all gestures with training data
            gestures = gesture_manager.get_all_gestures()
            trained_gestures = {
                gid: gesture for gid, gesture in gestures.items()
                if len(gesture.training_samples) >= 5  # Minimum samples for analysis
            }
            
            if len(trained_gestures) < 2:
                return warnings
            
            # Extract features for all gestures
            gesture_features = {}
            for gesture_id, gesture_data in trained_gestures.items():
                features = self._extract_gesture_features(gesture_data)
                if features is not None:
                    gesture_features[gesture_id] = features
            
            # Compare all pairs of gestures
            gesture_ids = list(gesture_features.keys())
            for i in range(len(gesture_ids)):
                for j in range(i + 1, len(gesture_ids)):
                    gesture1_id = gesture_ids[i]
                    gesture2_id = gesture_ids[j]
                    
                    warning = self._compare_gestures(
                        gesture1_id, trained_gestures[gesture1_id], gesture_features[gesture1_id],
                        gesture2_id, trained_gestures[gesture2_id], gesture_features[gesture2_id]
                    )
                    
                    if warning:
                        warnings.append(warning)
            
            logger.info(f"Analyzed {len(trained_gestures)} gestures, found {len(warnings)} potential conflicts")
            return warnings
            
        except Exception as e:
            logger.error(f"Error analyzing gesture ambiguity: {e}")
            return warnings
    
    def check_new_gesture_conflicts(self, new_gesture_data: CustomGestureData,
                                  gesture_manager: CustomGestureManager) -> List[AmbiguityWarning]:
        """
        Check if a new gesture conflicts with existing gestures.
        
        Args:
            new_gesture_data: New gesture to check
            gesture_manager: Custom gesture manager instance
            
        Returns:
            List of ambiguity warnings for the new gesture
        """
        warnings = []
        
        try:
            if len(new_gesture_data.training_samples) < 5:
                return warnings
            
            # Extract features for new gesture
            new_features = self._extract_gesture_features(new_gesture_data)
            if new_features is None:
                return warnings
            
            # Get existing gestures
            existing_gestures = gesture_manager.get_all_gestures()
            
            for gesture_id, gesture_data in existing_gestures.items():
                if gesture_id == new_gesture_data.id:
                    continue  # Skip self
                
                if len(gesture_data.training_samples) < 5:
                    continue  # Skip gestures with insufficient data
                
                existing_features = self._extract_gesture_features(gesture_data)
                if existing_features is None:
                    continue
                
                warning = self._compare_gestures(
                    new_gesture_data.id, new_gesture_data, new_features,
                    gesture_id, gesture_data, existing_features
                )
                
                if warning:
                    warnings.append(warning)
            
            return warnings
            
        except Exception as e:
            logger.error(f"Error checking new gesture conflicts: {e}")
            return warnings
    
    def _extract_gesture_features(self, gesture_data: CustomGestureData) -> Optional[np.ndarray]:
        """
        Extract average features for a gesture from its training samples.
        
        Args:
            gesture_data: Gesture data with training samples
            
        Returns:
            Average feature vector or None if extraction failed
        """
        try:
            feature_vectors = self.feature_extractor.extract_features_from_landmarks_list(
                gesture_data.training_samples
            )
            
            if not feature_vectors:
                return None
            
            # Calculate average features
            features_array = np.array(feature_vectors)
            average_features = np.mean(features_array, axis=0)
            
            return average_features
            
        except Exception as e:
            logger.error(f"Error extracting features for gesture {gesture_data.name}: {e}")
            return None
    
    def _compare_gestures(self, gesture1_id: str, gesture1_data: CustomGestureData, features1: np.ndarray,
                         gesture2_id: str, gesture2_data: CustomGestureData, features2: np.ndarray) -> Optional[AmbiguityWarning]:
        """
        Compare two gestures for similarity and potential conflicts.
        
        Args:
            gesture1_id: ID of first gesture
            gesture1_data: Data of first gesture
            features1: Feature vector of first gesture
            gesture2_id: ID of second gesture
            gesture2_data: Data of second gesture
            features2: Feature vector of second gesture
            
        Returns:
            AmbiguityWarning if conflict detected, None otherwise
        """
        try:
            # Calculate cosine similarity
            similarity = cosine_similarity([features1], [features2])[0][0]
            
            # Check for high similarity
            if similarity >= self.similarity_threshold:
                warning_type = "high_similarity"
                description = f"Gestures '{gesture1_data.name}' and '{gesture2_data.name}' have very similar features (similarity: {similarity:.2f})"
                recommendation = "Consider recording more distinct training samples or adjusting gesture poses to make them more different."
                
                return AmbiguityWarning(
                    gesture1_id=gesture1_id,
                    gesture1_name=gesture1_data.name,
                    gesture2_id=gesture2_id,
                    gesture2_name=gesture2_data.name,
                    similarity_score=similarity,
                    warning_type=warning_type,
                    description=description,
                    recommendation=recommendation
                )
            
            # Check for moderate similarity (potential confusion)
            elif similarity >= 0.7:
                warning_type = "confusion_risk"
                description = f"Gestures '{gesture1_data.name}' and '{gesture2_data.name}' may be confused (similarity: {similarity:.2f})"
                recommendation = "Monitor recognition accuracy and consider retraining if confusion occurs."
                
                return AmbiguityWarning(
                    gesture1_id=gesture1_id,
                    gesture1_name=gesture1_data.name,
                    gesture2_id=gesture2_id,
                    gesture2_name=gesture2_data.name,
                    similarity_score=similarity,
                    warning_type=warning_type,
                    description=description,
                    recommendation=recommendation
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error comparing gestures {gesture1_data.name} and {gesture2_data.name}: {e}")
            return None
    
    def check_predefined_gesture_conflicts(self, custom_gesture_data: CustomGestureData,
                                         recognizer: StaticGestureRecognizer) -> List[AmbiguityWarning]:
        """
        Check if a custom gesture conflicts with pre-defined gestures.
        
        Args:
            custom_gesture_data: Custom gesture to check
            recognizer: Static gesture recognizer instance
            
        Returns:
            List of warnings for conflicts with pre-defined gestures
        """
        warnings = []
        
        try:
            if len(custom_gesture_data.training_samples) < 5:
                return warnings
            
            # Create dummy hand landmarks for testing pre-defined gestures
            from src.core.hand_tracker import HandLandmarks
            
            # Test custom gesture against each training sample
            confusion_counts = {gesture_type: 0 for gesture_type in recognizer.get_supported_gestures()}
            
            for landmarks in custom_gesture_data.training_samples:
                hand_landmarks = HandLandmarks(landmarks=landmarks, handedness="Right", confidence=1.0)
                
                # Test against pre-defined gesture recognizer (without custom gestures)
                temp_recognizer = StaticGestureRecognizer(confidence_threshold=0.5)
                result = temp_recognizer.recognize_gesture(hand_landmarks)
                
                if result.gesture_type != GestureType.UNKNOWN:
                    confusion_counts[result.gesture_type] += 1
            
            # Check for significant confusion
            total_samples = len(custom_gesture_data.training_samples)
            for gesture_type, count in confusion_counts.items():
                confusion_rate = count / total_samples
                
                if confusion_rate >= 0.3:  # 30% or more confusion
                    warning = AmbiguityWarning(
                        gesture1_id=custom_gesture_data.id,
                        gesture1_name=custom_gesture_data.name,
                        gesture2_id=gesture_type.value,
                        gesture2_name=gesture_type.value.replace('_', ' ').title(),
                        similarity_score=confusion_rate,
                        warning_type="predefined_conflict",
                        description=f"Custom gesture '{custom_gesture_data.name}' may be confused with pre-defined gesture '{gesture_type.value}' ({confusion_rate:.1%} of samples)",
                        recommendation="Consider using more distinct hand poses or adjusting confidence thresholds."
                    )
                    warnings.append(warning)
            
            return warnings
            
        except Exception as e:
            logger.error(f"Error checking predefined gesture conflicts: {e}")
            return warnings
    
    def get_similarity_matrix(self, gesture_manager: CustomGestureManager) -> Dict[str, Dict[str, float]]:
        """
        Get similarity matrix for all gestures.
        
        Args:
            gesture_manager: Custom gesture manager instance
            
        Returns:
            Dictionary mapping gesture pairs to similarity scores
        """
        similarity_matrix = {}
        
        try:
            gestures = gesture_manager.get_all_gestures()
            trained_gestures = {
                gid: gesture for gid, gesture in gestures.items()
                if len(gesture.training_samples) >= 5
            }
            
            # Extract features for all gestures
            gesture_features = {}
            for gesture_id, gesture_data in trained_gestures.items():
                features = self._extract_gesture_features(gesture_data)
                if features is not None:
                    gesture_features[gesture_id] = features
            
            # Calculate similarity matrix
            for gesture1_id in gesture_features:
                similarity_matrix[gesture1_id] = {}
                for gesture2_id in gesture_features:
                    if gesture1_id == gesture2_id:
                        similarity_matrix[gesture1_id][gesture2_id] = 1.0
                    else:
                        similarity = cosine_similarity(
                            [gesture_features[gesture1_id]], 
                            [gesture_features[gesture2_id]]
                        )[0][0]
                        similarity_matrix[gesture1_id][gesture2_id] = similarity
            
            return similarity_matrix
            
        except Exception as e:
            logger.error(f"Error calculating similarity matrix: {e}")
            return similarity_matrix
