"""
<PERSON><PERSON><PERSON> to retrain existing custom gestures with improved binary classification.
This fixes the recognition issues by retraining models with negative examples.
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.custom_gesture_manager import CustomGestureManager
from src.core.gesture_trainer import GestureTrainer
from src.utils.logger import logger


def retrain_all_custom_gestures():
    """Retrain all existing custom gestures with the new binary classification approach."""
    print("Retraining Custom Gestures with Binary Classification")
    print("=" * 60)
    
    # Initialize managers
    gesture_manager = CustomGestureManager()
    gesture_trainer = GestureTrainer()
    
    # Get all existing gestures
    all_gestures = gesture_manager.get_all_gestures()
    
    if not all_gestures:
        print("No custom gestures found to retrain.")
        return
    
    print(f"Found {len(all_gestures)} custom gestures to retrain:")
    
    retrained_count = 0
    failed_count = 0
    
    for gesture_id, gesture_data in all_gestures.items():
        print(f"\nRetraining: {gesture_data.name}")
        print(f"  ID: {gesture_id}")
        print(f"  Training samples: {len(gesture_data.training_samples)}")
        
        if len(gesture_data.training_samples) < 10:
            print(f"  ⚠ Skipping - insufficient samples (need at least 10)")
            continue
        
        try:
            # Retrain with new binary classification approach
            success, accuracy, model_path = gesture_trainer.train_gesture(
                gesture_data, 
                algorithm='random_forest',
                min_samples=10,
                negative_samples_ratio=1.0  # 1:1 ratio of negative to positive samples
            )
            
            if success:
                # Update gesture with new model information
                gesture_manager.update_gesture_model(gesture_id, model_path, accuracy)
                print(f"  ✓ Retrained successfully!")
                print(f"    New accuracy: {accuracy:.1%}")
                print(f"    Model saved: {model_path}")
                retrained_count += 1
            else:
                print(f"  ✗ Retraining failed")
                failed_count += 1
                
        except Exception as e:
            print(f"  ✗ Error retraining: {e}")
            failed_count += 1
    
    print("\n" + "=" * 60)
    print("Retraining Summary:")
    print(f"  Successfully retrained: {retrained_count}")
    print(f"  Failed: {failed_count}")
    print(f"  Total processed: {retrained_count + failed_count}")
    
    if retrained_count > 0:
        print("\n✓ Custom gestures have been retrained with binary classification!")
        print("  This should significantly improve recognition accuracy and reduce conflicts.")
        print("  Please restart the GestureFlow application to load the new models.")
    else:
        print("\n⚠ No gestures were retrained. Check that you have custom gestures with sufficient training data.")


def main():
    """Main function."""
    try:
        retrain_all_custom_gestures()
        return 0
    except Exception as e:
        print(f"Error: {e}")
        logger.error(f"Retraining script failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
