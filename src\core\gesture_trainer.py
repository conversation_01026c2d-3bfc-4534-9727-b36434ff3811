"""
Gesture Trainer for GestureFlow application.
Implements ML training pipeline for custom static gestures.
Implements GFLOW-7: Implement Training Pipeline for Custom Static Gestures
"""

import os
import pickle
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report
import joblib

from src.core.feature_extractor import HandFeatureExtractor
from src.core.custom_gesture_manager import CustomGestureData
from src.utils.logger import logger


class GestureTrainer:
    """
    Trains machine learning models for custom gesture recognition.
    Supports multiple ML algorithms and provides training pipeline.
    """

    def __init__(self, models_dir: str = "data/models"):
        """
        Initialize gesture trainer.

        Args:
            models_dir: Directory to save trained models
        """
        self.models_dir = models_dir
        self.feature_extractor = HandFeatureExtractor()

        # Ensure models directory exists
        if not os.path.exists(models_dir):
            os.makedirs(models_dir)

        # Available ML algorithms
        self.algorithms = {
            'random_forest': {
                'class': RandomForestClassifier,
                'params': {'n_estimators': 100, 'random_state': 42, 'max_depth': 10}
            },
            'svm': {
                'class': SVC,
                'params': {'kernel': 'rbf', 'random_state': 42, 'probability': True}
            },
            'knn': {
                'class': KNeighborsClassifier,
                'params': {'n_neighbors': 5, 'weights': 'distance'}
            }
        }

        logger.info("GestureTrainer initialized")

    def train_gesture(self, gesture_data: CustomGestureData,
                     algorithm: str = 'random_forest',
                     min_samples: int = 10,
                     negative_samples_ratio: float = 1.0) -> Tuple[bool, float, str]:
        """
        Train a model for a single custom gesture using one-class classification.

        Args:
            gesture_data: Custom gesture data with training samples
            algorithm: ML algorithm to use ('random_forest', 'svm', 'knn')
            min_samples: Minimum number of training samples required
            negative_samples_ratio: Ratio of negative to positive samples to generate

        Returns:
            Tuple of (success, accuracy, model_path)
        """
        try:
            # Check if we have enough training samples
            if len(gesture_data.training_samples) < min_samples:
                logger.warning(f"Insufficient training samples for {gesture_data.name}: "
                             f"{len(gesture_data.training_samples)} < {min_samples}")
                return False, 0.0, ""

            # Extract features from training samples
            logger.info(f"Extracting features for gesture: {gesture_data.name}")
            feature_vectors = self.feature_extractor.extract_features_from_landmarks_list(
                gesture_data.training_samples
            )

            if not feature_vectors:
                logger.error(f"Failed to extract features for gesture: {gesture_data.name}")
                return False, 0.0, ""

            # Prepare positive samples
            X_positive = np.array(feature_vectors)

            # Generate negative samples by adding noise to positive samples
            # This creates "near-miss" examples that help the model learn boundaries
            num_negative = int(len(feature_vectors) * negative_samples_ratio)
            X_negative = self._generate_negative_samples(X_positive, num_negative)

            # Combine positive and negative samples
            X = np.vstack([X_positive, X_negative])
            y = np.hstack([np.ones(len(X_positive)), np.zeros(len(X_negative))])

            # Validate data
            if X.shape[0] < 6:  # Need at least 6 samples for train/test split
                logger.warning(f"Too few samples for training: {X.shape[0]}")
                return False, 0.0, ""

            # Scale features
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            # Split data for validation
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=0.3, random_state=42, stratify=y
            )

            # Train model
            if algorithm not in self.algorithms:
                logger.error(f"Unknown algorithm: {algorithm}")
                return False, 0.0, ""

            model_config = self.algorithms[algorithm]
            model = model_config['class'](**model_config['params'])
            model.fit(X_train, y_train)

            # Calculate accuracy on test set
            predictions = model.predict(X_test)
            accuracy = accuracy_score(y_test, predictions)

            # Calculate precision and recall for positive class
            from sklearn.metrics import precision_recall_fscore_support
            precision, recall, _, _ = precision_recall_fscore_support(y_test, predictions, average='binary')

            logger.info(f"Gesture {gesture_data.name} training results:")
            logger.info(f"  Accuracy: {accuracy:.3f}")
            logger.info(f"  Precision: {precision:.3f}")
            logger.info(f"  Recall: {recall:.3f}")

            # Save model and scaler
            model_filename = f"{gesture_data.id}_{algorithm}.pkl"
            model_path = os.path.join(self.models_dir, model_filename)

            model_data = {
                'model': model,
                'scaler': scaler,
                'algorithm': algorithm,
                'feature_names': self.feature_extractor.get_feature_names(),
                'training_samples': len(gesture_data.training_samples),
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'gesture_id': gesture_data.id,
                'gesture_name': gesture_data.name,
                'model_type': 'binary_classifier'
            }

            with open(model_path, 'wb') as f:
                pickle.dump(model_data, f)

            logger.info(f"Trained binary classifier for {gesture_data.name}: "
                       f"accuracy={accuracy:.3f}, samples={len(feature_vectors)}")

            return True, accuracy, model_path

        except Exception as e:
            logger.error(f"Error training gesture {gesture_data.name}: {e}")
            return False, 0.0, ""

    def _generate_negative_samples(self, X_positive: np.ndarray, num_negative: int) -> np.ndarray:
        """
        Generate negative samples by adding controlled noise to positive samples.

        Args:
            X_positive: Positive training samples
            num_negative: Number of negative samples to generate

        Returns:
            Array of negative samples
        """
        if num_negative == 0:
            return np.array([]).reshape(0, X_positive.shape[1])

        # Calculate feature statistics
        feature_std = np.std(X_positive, axis=0)
        feature_mean = np.mean(X_positive, axis=0)

        negative_samples = []

        for _ in range(num_negative):
            # Start with a random positive sample
            base_sample = X_positive[np.random.randint(0, len(X_positive))]

            # Add noise to create a negative sample
            # Use different noise strategies
            noise_type = np.random.choice(['gaussian', 'uniform', 'feature_swap'])

            if noise_type == 'gaussian':
                # Add Gaussian noise scaled by feature standard deviation
                noise = np.random.normal(0, feature_std * 0.5, size=base_sample.shape)
                negative_sample = base_sample + noise

            elif noise_type == 'uniform':
                # Add uniform noise
                noise_scale = feature_std * 0.3
                noise = np.random.uniform(-noise_scale, noise_scale, size=base_sample.shape)
                negative_sample = base_sample + noise

            else:  # feature_swap
                # Randomly swap some features with values from other samples
                negative_sample = base_sample.copy()
                num_swaps = np.random.randint(1, min(10, len(base_sample) // 4))
                swap_indices = np.random.choice(len(base_sample), num_swaps, replace=False)

                for idx in swap_indices:
                    # Replace with value from random sample
                    random_sample = X_positive[np.random.randint(0, len(X_positive))]
                    negative_sample[idx] = random_sample[idx] + np.random.normal(0, feature_std[idx] * 0.2)

            negative_samples.append(negative_sample)

        return np.array(negative_samples)

    def train_multi_gesture_classifier(self, gestures_data: Dict[str, CustomGestureData],
                                     algorithm: str = 'random_forest') -> Tuple[bool, float, str]:
        """
        Train a multi-class classifier for multiple custom gestures.

        Args:
            gestures_data: Dictionary of gesture ID to CustomGestureData
            algorithm: ML algorithm to use

        Returns:
            Tuple of (success, accuracy, model_path)
        """
        try:
            if len(gestures_data) < 2:
                logger.warning("Need at least 2 gestures for multi-class training")
                return False, 0.0, ""

            # Collect all training data
            all_features = []
            all_labels = []
            gesture_id_to_label = {}
            label_to_gesture_id = {}

            label = 0
            for gesture_id, gesture_data in gestures_data.items():
                if len(gesture_data.training_samples) < 5:  # Minimum samples per class
                    logger.warning(f"Skipping {gesture_data.name}: insufficient samples")
                    continue

                # Extract features
                feature_vectors = self.feature_extractor.extract_features_from_landmarks_list(
                    gesture_data.training_samples
                )

                if feature_vectors:
                    all_features.extend(feature_vectors)
                    all_labels.extend([label] * len(feature_vectors))
                    gesture_id_to_label[gesture_id] = label
                    label_to_gesture_id[label] = gesture_id
                    label += 1

            if len(set(all_labels)) < 2:
                logger.warning("Need at least 2 classes with sufficient samples")
                return False, 0.0, ""

            # Prepare training data
            X = np.array(all_features)
            y = np.array(all_labels)

            # Scale features
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            # Split data for validation
            if len(X_scaled) > 10:
                X_train, X_test, y_train, y_test = train_test_split(
                    X_scaled, y, test_size=0.2, random_state=42, stratify=y
                )
            else:
                X_train, X_test, y_train, y_test = X_scaled, X_scaled, y, y

            # Train model
            model_config = self.algorithms[algorithm]
            model = model_config['class'](**model_config['params'])
            model.fit(X_train, y_train)

            # Calculate accuracy
            if len(X_test) > 0:
                predictions = model.predict(X_test)
                accuracy = accuracy_score(y_test, predictions)

                # Log classification report
                logger.info(f"Classification Report:\n{classification_report(y_test, predictions)}")
            else:
                accuracy = 0.0

            # Save multi-class model
            model_filename = f"multi_gesture_{algorithm}.pkl"
            model_path = os.path.join(self.models_dir, model_filename)

            model_data = {
                'model': model,
                'scaler': scaler,
                'algorithm': algorithm,
                'feature_names': self.feature_extractor.get_feature_names(),
                'gesture_id_to_label': gesture_id_to_label,
                'label_to_gesture_id': label_to_gesture_id,
                'accuracy': accuracy,
                'num_classes': len(set(all_labels)),
                'total_samples': len(all_features)
            }

            with open(model_path, 'wb') as f:
                pickle.dump(model_data, f)

            logger.info(f"Trained multi-class model: accuracy={accuracy:.3f}, "
                       f"classes={len(set(all_labels))}, samples={len(all_features)}")

            return True, accuracy, model_path

        except Exception as e:
            logger.error(f"Error training multi-gesture classifier: {e}")
            return False, 0.0, ""

    def load_model(self, model_path: str) -> Optional[Dict[str, Any]]:
        """
        Load a trained model from disk.

        Args:
            model_path: Path to the model file

        Returns:
            Model data dictionary or None if loading failed
        """
        try:
            if not os.path.exists(model_path):
                logger.error(f"Model file not found: {model_path}")
                return None

            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)

            logger.info(f"Loaded model from {model_path}")
            return model_data

        except Exception as e:
            logger.error(f"Error loading model from {model_path}: {e}")
            return None

    def predict_gesture(self, model_data: Dict[str, Any],
                       landmarks: List[Tuple[float, float, float]]) -> Tuple[Optional[str], float]:
        """
        Predict gesture using a trained model.

        Args:
            model_data: Loaded model data
            landmarks: Hand landmarks

        Returns:
            Tuple of (gesture_id, confidence)
        """
        try:
            # Extract features
            from src.core.hand_tracker import HandLandmarks
            hand_landmarks = HandLandmarks(landmarks=landmarks, handedness="Right", confidence=1.0)
            features = self.feature_extractor.extract_features(hand_landmarks)

            if not features:
                return None, 0.0

            # Scale features
            X = np.array(features).reshape(1, -1)
            X_scaled = model_data['scaler'].transform(X)

            # Predict
            model = model_data['model']

            # Handle different model types
            if model_data.get('model_type') == 'binary_classifier':
                # Binary classifier (positive vs negative)
                if hasattr(model, 'predict_proba'):
                    probabilities = model.predict_proba(X_scaled)[0]
                    # Get probability of positive class (class 1)
                    positive_prob = probabilities[1] if len(probabilities) > 1 else probabilities[0]
                    prediction = model.predict(X_scaled)[0]

                    # Only return gesture if predicted as positive class
                    if prediction == 1:
                        gesture_id = model_data.get('gesture_id')
                        confidence = positive_prob
                    else:
                        gesture_id = None
                        confidence = 0.0
                else:
                    prediction = model.predict(X_scaled)[0]
                    if prediction == 1:
                        gesture_id = model_data.get('gesture_id')
                        confidence = 0.8  # Default confidence for models without probabilities
                    else:
                        gesture_id = None
                        confidence = 0.0

            elif 'label_to_gesture_id' in model_data:
                # Multi-class model
                if hasattr(model, 'predict_proba'):
                    probabilities = model.predict_proba(X_scaled)[0]
                    prediction = model.predict(X_scaled)[0]
                    confidence = np.max(probabilities)
                else:
                    prediction = model.predict(X_scaled)[0]
                    confidence = 0.8

                gesture_id = model_data['label_to_gesture_id'].get(prediction)

            else:
                # Legacy single-class model (deprecated)
                prediction = model.predict(X_scaled)[0]
                gesture_id = model_data.get('gesture_id')
                if prediction == 0:
                    gesture_id = None
                    confidence = 0.0
                else:
                    confidence = 0.8

            return gesture_id, confidence

        except Exception as e:
            logger.error(f"Error predicting gesture: {e}")
            return None, 0.0

    def evaluate_model(self, model_path: str, test_data: Dict[str, List[List[Tuple[float, float, float]]]]) -> Dict[str, float]:
        """
        Evaluate a trained model on test data.

        Args:
            model_path: Path to the trained model
            test_data: Dictionary of gesture_id to list of landmark sequences

        Returns:
            Evaluation metrics
        """
        try:
            model_data = self.load_model(model_path)
            if not model_data:
                return {}

            all_predictions = []
            all_true_labels = []

            for gesture_id, landmark_sequences in test_data.items():
                for landmarks in landmark_sequences:
                    predicted_id, confidence = self.predict_gesture(model_data, landmarks)

                    all_predictions.append(predicted_id)
                    all_true_labels.append(gesture_id)

            # Calculate accuracy
            correct = sum(1 for pred, true in zip(all_predictions, all_true_labels) if pred == true)
            accuracy = correct / len(all_predictions) if all_predictions else 0.0

            return {
                'accuracy': accuracy,
                'total_predictions': len(all_predictions),
                'correct_predictions': correct
            }

        except Exception as e:
            logger.error(f"Error evaluating model: {e}")
            return {}

    def get_available_algorithms(self) -> List[str]:
        """Get list of available ML algorithms."""
        return list(self.algorithms.keys())
