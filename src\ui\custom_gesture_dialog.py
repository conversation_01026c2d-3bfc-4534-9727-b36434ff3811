"""
Custom Gesture Recording Dialog for GestureFlow application.
Provides GUI for recording and labeling custom static gestures.
Implements GFLOW-6: Develop GUI for Custom Static Gesture Recording & Labeling
"""

import time
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QLabel, QLineEdit, QTextEdit, QProgressBar, QGroupBox,
                           QSpinBox, QMessageBox, QGridLayout)
from PyQt5.QtCore import QTimer, pyqtSignal, Qt
from PyQt5.QtGui import QFont, QPixmap, QImage
import cv2
import numpy as np
from typing import Optional, List

from src.core.custom_gesture_manager import CustomGestureManager
from src.core.gesture_trainer import GestureTrainer
from src.core.hand_tracker import HandLandmarks
from src.utils.logger import logger


class CustomGestureRecordingDialog(QDialog):
    """
    Dialog for recording custom gestures.
    Allows users to create, name, and train custom static gestures.
    """

    # Signals
    gesture_created = pyqtSignal(str)  # Emitted when a new gesture is created

    def __init__(self, gesture_manager: CustomGestureManager,
                 gesture_trainer: GestureTrainer, parent=None):
        """
        Initialize custom gesture recording dialog.

        Args:
            gesture_manager: Custom gesture manager instance
            gesture_trainer: Gesture trainer instance
            parent: Parent widget
        """
        super().__init__(parent)
        self.gesture_manager = gesture_manager
        self.gesture_trainer = gesture_trainer

        # Recording state
        self.current_gesture_id = None
        self.is_recording = False
        self.recorded_samples = []
        self.target_samples = 15
        self.recording_countdown = 0
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self.update_countdown)

        # Hand tracking data (will be provided by parent)
        self.current_hand_landmarks: Optional[List[HandLandmarks]] = None

        self.setup_ui()
        self.connect_signals()

        logger.info("CustomGestureRecordingDialog initialized")

    def setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("Record Custom Gesture")
        self.setModal(True)
        self.resize(500, 600)

        layout = QVBoxLayout(self)

        # Title
        title_label = QLabel("Create Custom Gesture")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # Gesture information group
        info_group = QGroupBox("Gesture Information")
        info_layout = QGridLayout(info_group)

        # Gesture name
        info_layout.addWidget(QLabel("Gesture Name:"), 0, 0)
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("Enter a unique name for your gesture")
        info_layout.addWidget(self.name_input, 0, 1)

        # Gesture description
        info_layout.addWidget(QLabel("Description:"), 1, 0)
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("Optional description of the gesture")
        self.description_input.setMaximumHeight(60)
        info_layout.addWidget(self.description_input, 1, 1)

        layout.addWidget(info_group)

        # Recording settings group
        settings_group = QGroupBox("Recording Settings")
        settings_layout = QGridLayout(settings_group)

        # Number of samples
        settings_layout.addWidget(QLabel("Training Samples:"), 0, 0)
        self.samples_spinbox = QSpinBox()
        self.samples_spinbox.setRange(10, 50)
        self.samples_spinbox.setValue(15)
        self.samples_spinbox.valueChanged.connect(self.update_target_samples)
        settings_layout.addWidget(self.samples_spinbox, 0, 1)

        layout.addWidget(settings_group)

        # Recording status group
        status_group = QGroupBox("Recording Status")
        status_layout = QVBoxLayout(status_group)

        # Status label
        self.status_label = QLabel("Ready to record")
        self.status_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.status_label)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, self.target_samples)
        self.progress_bar.setValue(0)
        status_layout.addWidget(self.progress_bar)

        # Sample count
        self.sample_count_label = QLabel("Samples recorded: 0 / 15")
        self.sample_count_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.sample_count_label)

        # Countdown label
        self.countdown_label = QLabel("")
        self.countdown_label.setAlignment(Qt.AlignCenter)
        countdown_font = QFont()
        countdown_font.setPointSize(24)
        countdown_font.setBold(True)
        self.countdown_label.setFont(countdown_font)
        self.countdown_label.setStyleSheet("color: red;")
        status_layout.addWidget(self.countdown_label)

        layout.addWidget(status_group)

        # Instructions
        instructions_group = QGroupBox("Instructions")
        instructions_layout = QVBoxLayout(instructions_group)

        instructions_text = """
1. Enter a unique name for your gesture
2. Optionally add a description
3. Click 'Start Recording' to begin
4. When countdown reaches 0, perform your gesture
5. Hold the gesture steady for about 1 second
6. Repeat until you have enough samples
7. Click 'Train Gesture' to create the model
        """

        instructions_label = QLabel(instructions_text.strip())
        instructions_label.setWordWrap(True)
        instructions_layout.addWidget(instructions_label)

        layout.addWidget(instructions_group)

        # Control buttons
        button_layout = QHBoxLayout()

        self.start_button = QPushButton("Start Recording")
        self.start_button.setEnabled(True)
        button_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("Stop Recording")
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)

        self.train_button = QPushButton("Train Gesture")
        self.train_button.setEnabled(False)
        button_layout.addWidget(self.train_button)

        layout.addLayout(button_layout)

        # Dialog buttons
        dialog_button_layout = QHBoxLayout()

        self.cancel_button = QPushButton("Cancel")
        dialog_button_layout.addWidget(self.cancel_button)

        self.finish_button = QPushButton("Finish")
        self.finish_button.setEnabled(False)
        dialog_button_layout.addWidget(self.finish_button)

        layout.addLayout(dialog_button_layout)

    def connect_signals(self):
        """Connect UI signals to handlers."""
        self.start_button.clicked.connect(self.start_recording)
        self.stop_button.clicked.connect(self.stop_recording)
        self.train_button.clicked.connect(self.train_gesture)
        self.cancel_button.clicked.connect(self.reject)
        self.finish_button.clicked.connect(self.accept)
        self.name_input.textChanged.connect(self.validate_input)

    def validate_input(self):
        """Validate user input and enable/disable buttons."""
        name = self.name_input.text().strip()
        has_name = len(name) > 0

        self.start_button.setEnabled(has_name and not self.is_recording)

    def update_target_samples(self, value):
        """Update target number of samples."""
        self.target_samples = value
        self.progress_bar.setRange(0, value)
        self.update_sample_count_display()

    def update_sample_count_display(self):
        """Update the sample count display."""
        current = len(self.recorded_samples)
        self.sample_count_label.setText(f"Samples recorded: {current} / {self.target_samples}")
        self.progress_bar.setValue(current)

    def start_recording(self):
        """Start the gesture recording process."""
        name = self.name_input.text().strip()
        if not name:
            QMessageBox.warning(self, "Invalid Input", "Please enter a gesture name.")
            return

        # Check if gesture name already exists
        existing_gestures = self.gesture_manager.get_all_gestures()
        for gesture in existing_gestures.values():
            if gesture.name.lower() == name.lower():
                QMessageBox.warning(self, "Duplicate Name",
                                  f"A gesture named '{name}' already exists. Please choose a different name.")
                return

        # Create new gesture
        description = self.description_input.toPlainText().strip()
        self.current_gesture_id = self.gesture_manager.create_new_gesture(name, description)

        # Reset recording state
        self.recorded_samples = []
        self.is_recording = True

        # Update UI
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.name_input.setEnabled(False)
        self.description_input.setEnabled(False)
        self.samples_spinbox.setEnabled(False)

        # Start countdown for first sample
        self.start_sample_countdown()

        logger.info(f"Started recording gesture: {name}")

    def start_sample_countdown(self):
        """Start countdown for recording a sample."""
        self.recording_countdown = 3  # 3 second countdown
        self.status_label.setText("Get ready to perform your gesture...")
        self.countdown_timer.start(1000)  # Update every second
        self.update_countdown()

    def update_countdown(self):
        """Update countdown display."""
        if self.recording_countdown > 0:
            self.countdown_label.setText(str(self.recording_countdown))
            self.recording_countdown -= 1
        else:
            self.countdown_label.setText("RECORD!")
            self.countdown_timer.stop()

            # Record sample after a short delay
            QTimer.singleShot(1000, self.record_sample)

    def record_sample(self):
        """Record a single gesture sample."""
        if not self.is_recording or not self.current_hand_landmarks:
            return

        # Check if we have hand landmarks
        if not self.current_hand_landmarks:
            self.status_label.setText("No hand detected. Please show your hand to the camera.")
            QTimer.singleShot(2000, self.start_sample_countdown)
            return

        # Use the first detected hand
        hand_landmarks = self.current_hand_landmarks[0]

        # Add sample to gesture
        success = self.gesture_manager.add_training_sample(self.current_gesture_id, hand_landmarks)

        if success:
            self.recorded_samples.append(hand_landmarks.landmarks)
            self.update_sample_count_display()

            current_count = len(self.recorded_samples)
            self.status_label.setText(f"Sample {current_count} recorded!")
            self.countdown_label.setText("✓")

            # Check if we have enough samples
            if current_count >= self.target_samples:
                self.status_label.setText("Recording complete! You can now train the gesture.")
                self.stop_recording()
                self.train_button.setEnabled(True)
            else:
                # Continue with next sample
                QTimer.singleShot(2000, self.start_sample_countdown)
        else:
            self.status_label.setText("Failed to record sample. Please try again.")
            QTimer.singleShot(2000, self.start_sample_countdown)

    def stop_recording(self):
        """Stop the recording process."""
        self.is_recording = False
        self.countdown_timer.stop()

        # Update UI
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.name_input.setEnabled(True)
        self.description_input.setEnabled(True)
        self.samples_spinbox.setEnabled(True)

        self.countdown_label.setText("")

        if len(self.recorded_samples) > 0:
            self.status_label.setText(f"Recording stopped. {len(self.recorded_samples)} samples recorded.")
            self.train_button.setEnabled(True)
        else:
            self.status_label.setText("Recording stopped. No samples recorded.")

        logger.info("Stopped recording gesture")

    def train_gesture(self):
        """Train the recorded gesture."""
        if not self.current_gesture_id:
            QMessageBox.warning(self, "Error", "No gesture to train.")
            return

        gesture_data = self.gesture_manager.get_gesture(self.current_gesture_id)
        if not gesture_data:
            QMessageBox.warning(self, "Error", "Gesture data not found.")
            return

        if len(gesture_data.training_samples) < 5:
            QMessageBox.warning(self, "Insufficient Data",
                              "Need at least 5 samples to train a gesture. Please record more samples.")
            return

        # Show training progress
        self.status_label.setText("Training gesture model...")
        self.train_button.setEnabled(False)

        # Train the gesture (this should be done in a separate thread in a real application)
        success, accuracy, model_path = self.gesture_trainer.train_gesture(gesture_data)

        if success:
            # Update gesture with model information
            self.gesture_manager.update_gesture_model(self.current_gesture_id, model_path, accuracy)

            self.status_label.setText(f"Training complete! Accuracy: {accuracy:.1%}")
            self.finish_button.setEnabled(True)

            QMessageBox.information(self, "Training Complete",
                                  f"Gesture '{gesture_data.name}' trained successfully!\n"
                                  f"Accuracy: {accuracy:.1%}")

            # Emit signal that gesture was created
            self.gesture_created.emit(self.current_gesture_id)

        else:
            self.status_label.setText("Training failed. Please try again.")
            self.train_button.setEnabled(True)

            QMessageBox.warning(self, "Training Failed",
                              "Failed to train the gesture model. Please try recording more samples.")

        logger.info(f"Gesture training completed: success={success}, accuracy={accuracy:.3f}")

    def update_hand_landmarks(self, hand_landmarks_list: List[HandLandmarks]):
        """
        Update current hand landmarks from the main application.

        Args:
            hand_landmarks_list: List of detected hand landmarks
        """
        self.current_hand_landmarks = hand_landmarks_list

    def closeEvent(self, event):
        """Handle dialog close event."""
        if self.is_recording:
            reply = QMessageBox.question(self, "Recording in Progress",
                                       "Recording is in progress. Are you sure you want to close?",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.No:
                event.ignore()
                return

        # Clean up if gesture was created but not trained
        if self.current_gesture_id and not self.finish_button.isEnabled():
            gesture_data = self.gesture_manager.get_gesture(self.current_gesture_id)
            if gesture_data and not gesture_data.model_path:
                self.gesture_manager.delete_gesture(self.current_gesture_id)
                logger.info(f"Deleted untrained gesture: {gesture_data.name}")

        event.accept()
