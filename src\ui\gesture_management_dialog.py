"""
Gesture Management Dialog for GestureFlow application.
Provides GUI for viewing, managing, and deleting custom gestures.
Implements GFLOW-9: Develop GUI for Managing Custom Gestures (View, Delete)
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QLabel, QTableWidget, QTableWidgetItem, QGroupBox,
                           QMessageBox, QHeaderView, QAbstractItemView, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from typing import Dict, Optional
from datetime import datetime

from src.core.custom_gesture_manager import CustomGestureManager, CustomGestureData
from src.utils.logger import logger


class GestureManagementDialog(QDialog):
    """
    Dialog for managing custom gestures.
    Allows users to view, enable/disable, and delete custom gestures.
    """

    # Signals
    gesture_deleted = pyqtSignal(str)  # Emitted when a gesture is deleted
    gesture_updated = pyqtSignal(str)  # Emitted when a gesture is updated

    def __init__(self, gesture_manager: CustomGestureManager, parent=None):
        """
        Initialize gesture management dialog.

        Args:
            gesture_manager: Custom gesture manager instance
            parent: Parent widget
        """
        super().__init__(parent)
        self.gesture_manager = gesture_manager
        self.selected_gesture_id: Optional[str] = None

        self.setup_ui()
        self.connect_signals()
        self.refresh_gesture_list()

        logger.info("GestureManagementDialog initialized")

    def setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("Manage Custom Gestures")
        self.setModal(True)
        self.resize(800, 600)

        layout = QVBoxLayout(self)

        # Title
        title_label = QLabel("Custom Gesture Management")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # Gesture list group
        list_group = QGroupBox("Your Custom Gestures")
        list_layout = QVBoxLayout(list_group)

        # Gesture table
        self.gesture_table = QTableWidget()
        self.gesture_table.setColumnCount(6)
        self.gesture_table.setHorizontalHeaderLabels([
            "Name", "Samples", "Accuracy", "Status", "Created", "Modified"
        ])

        # Configure table
        self.gesture_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.gesture_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.gesture_table.setAlternatingRowColors(True)

        # Set column widths
        header = self.gesture_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Name
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Samples
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Accuracy
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Status
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Created
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Modified

        list_layout.addWidget(self.gesture_table)
        layout.addWidget(list_group)

        # Gesture details group
        details_group = QGroupBox("Gesture Details")
        details_layout = QVBoxLayout(details_group)

        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setMaximumHeight(150)
        details_layout.addWidget(self.details_text)

        layout.addWidget(details_group)

        # Statistics group
        stats_group = QGroupBox("Statistics")
        stats_layout = QVBoxLayout(stats_group)

        self.stats_label = QLabel()
        stats_layout.addWidget(self.stats_label)

        layout.addWidget(stats_group)

        # Control buttons
        button_layout = QHBoxLayout()

        self.refresh_button = QPushButton("Refresh")
        button_layout.addWidget(self.refresh_button)

        self.enable_button = QPushButton("Enable")
        self.enable_button.setEnabled(False)
        button_layout.addWidget(self.enable_button)

        self.disable_button = QPushButton("Disable")
        self.disable_button.setEnabled(False)
        button_layout.addWidget(self.disable_button)

        self.delete_button = QPushButton("Delete")
        self.delete_button.setEnabled(False)
        self.delete_button.setStyleSheet("QPushButton { background-color: #ff6b6b; color: white; }")
        button_layout.addWidget(self.delete_button)

        layout.addLayout(button_layout)

        # Dialog buttons
        dialog_button_layout = QHBoxLayout()

        self.close_button = QPushButton("Close")
        dialog_button_layout.addWidget(self.close_button)

        layout.addLayout(dialog_button_layout)

    def connect_signals(self):
        """Connect UI signals to handlers."""
        self.gesture_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.refresh_button.clicked.connect(self.refresh_gesture_list)
        self.enable_button.clicked.connect(self.enable_gesture)
        self.disable_button.clicked.connect(self.disable_gesture)
        self.delete_button.clicked.connect(self.delete_gesture)
        self.close_button.clicked.connect(self.accept)

    def refresh_gesture_list(self):
        """Refresh the gesture list display."""
        try:
            gestures = self.gesture_manager.get_all_gestures()

            # Clear table
            self.gesture_table.setRowCount(0)

            # Populate table
            for row, (gesture_id, gesture_data) in enumerate(gestures.items()):
                self.gesture_table.insertRow(row)

                # Name
                name_item = QTableWidgetItem(gesture_data.name)
                name_item.setData(Qt.UserRole, gesture_id)  # Store gesture ID
                self.gesture_table.setItem(row, 0, name_item)

                # Samples
                samples_item = QTableWidgetItem(str(len(gesture_data.training_samples)))
                samples_item.setTextAlignment(Qt.AlignCenter)
                self.gesture_table.setItem(row, 1, samples_item)

                # Accuracy
                if gesture_data.accuracy is not None:
                    accuracy_text = f"{gesture_data.accuracy:.1%}"
                else:
                    accuracy_text = "Not trained"
                accuracy_item = QTableWidgetItem(accuracy_text)
                accuracy_item.setTextAlignment(Qt.AlignCenter)
                self.gesture_table.setItem(row, 2, accuracy_item)

                # Status
                status_text = "Enabled" if gesture_data.enabled else "Disabled"
                status_item = QTableWidgetItem(status_text)
                status_item.setTextAlignment(Qt.AlignCenter)
                if gesture_data.enabled:
                    status_item.setBackground(Qt.green)
                else:
                    status_item.setBackground(Qt.red)
                self.gesture_table.setItem(row, 3, status_item)

                # Created date
                created_date = self._format_date(gesture_data.created_date)
                created_item = QTableWidgetItem(created_date)
                created_item.setTextAlignment(Qt.AlignCenter)
                self.gesture_table.setItem(row, 4, created_item)

                # Modified date
                modified_date = self._format_date(gesture_data.modified_date)
                modified_item = QTableWidgetItem(modified_date)
                modified_item.setTextAlignment(Qt.AlignCenter)
                self.gesture_table.setItem(row, 5, modified_item)

            # Update statistics
            self.update_statistics()

            # Clear selection
            self.selected_gesture_id = None
            self.update_gesture_details()
            self.update_button_states()

            logger.info(f"Refreshed gesture list: {len(gestures)} gestures")

        except Exception as e:
            logger.error(f"Error refreshing gesture list: {e}")
            QMessageBox.critical(self, "Error", f"Failed to refresh gesture list: {e}")

    def on_selection_changed(self):
        """Handle gesture selection change."""
        selected_items = self.gesture_table.selectedItems()

        if selected_items:
            # Get gesture ID from the first column of selected row
            row = selected_items[0].row()
            name_item = self.gesture_table.item(row, 0)
            self.selected_gesture_id = name_item.data(Qt.UserRole)
        else:
            self.selected_gesture_id = None

        self.update_gesture_details()
        self.update_button_states()

    def update_gesture_details(self):
        """Update the gesture details display."""
        if not self.selected_gesture_id:
            self.details_text.setText("Select a gesture to view details.")
            return

        gesture_data = self.gesture_manager.get_gesture(self.selected_gesture_id)
        if not gesture_data:
            self.details_text.setText("Gesture data not found.")
            return

        # Format details
        accuracy_text = f"{gesture_data.accuracy:.1%}" if gesture_data.accuracy is not None else "N/A"

        details = f"""
<b>Name:</b> {gesture_data.name}<br>
<b>Description:</b> {gesture_data.description or 'No description'}<br>
<b>ID:</b> {gesture_data.id}<br>
<b>Training Samples:</b> {len(gesture_data.training_samples)}<br>
<b>Model Path:</b> {gesture_data.model_path or 'Not trained'}<br>
<b>Accuracy:</b> {(f"{gesture_data.accuracy:.1%}" if gesture_data.accuracy is not None else "N/A")}<br>
<b>Confidence Threshold:</b> {gesture_data.confidence_threshold:.1%}<br>
<b>Status:</b> {'Enabled' if gesture_data.enabled else 'Disabled'}<br>
<b>Created:</b> {self._format_date(gesture_data.created_date)}<br>
<b>Modified:</b> {self._format_date(gesture_data.modified_date)}
        """.strip()

        self.details_text.setHtml(details)

    def update_button_states(self):
        """Update button enabled/disabled states."""
        has_selection = self.selected_gesture_id is not None

        self.enable_button.setEnabled(False)
        self.disable_button.setEnabled(False)
        self.delete_button.setEnabled(has_selection)

        if has_selection:
            gesture_data = self.gesture_manager.get_gesture(self.selected_gesture_id)
            if gesture_data:
                self.enable_button.setEnabled(not gesture_data.enabled)
                self.disable_button.setEnabled(gesture_data.enabled)

    def update_statistics(self):
        """Update the statistics display."""
        try:
            stats = self.gesture_manager.get_gesture_statistics()

            stats_text = f"""
Total Gestures: {stats['total_gestures']}
Enabled Gestures: {stats['enabled_gestures']}
Trained Gestures: {stats['trained_gestures']}
Total Training Samples: {stats['total_training_samples']}
Average Samples per Gesture: {stats['average_samples_per_gesture']:.1f}
            """.strip()

            self.stats_label.setText(stats_text)

        except Exception as e:
            logger.error(f"Error updating statistics: {e}")
            self.stats_label.setText("Error loading statistics")

    def enable_gesture(self):
        """Enable the selected gesture."""
        if not self.selected_gesture_id:
            return

        gesture_data = self.gesture_manager.get_gesture(self.selected_gesture_id)
        if gesture_data:
            gesture_data.enabled = True
            self.gesture_manager.save_gesture(self.selected_gesture_id)
            self.refresh_gesture_list()
            self.gesture_updated.emit(self.selected_gesture_id)
            logger.info(f"Enabled gesture: {gesture_data.name}")

    def disable_gesture(self):
        """Disable the selected gesture."""
        if not self.selected_gesture_id:
            return

        gesture_data = self.gesture_manager.get_gesture(self.selected_gesture_id)
        if gesture_data:
            gesture_data.enabled = False
            self.gesture_manager.save_gesture(self.selected_gesture_id)
            self.refresh_gesture_list()
            self.gesture_updated.emit(self.selected_gesture_id)
            logger.info(f"Disabled gesture: {gesture_data.name}")

    def delete_gesture(self):
        """Delete the selected gesture."""
        if not self.selected_gesture_id:
            return

        gesture_data = self.gesture_manager.get_gesture(self.selected_gesture_id)
        if not gesture_data:
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self, "Confirm Deletion",
            f"Are you sure you want to delete the gesture '{gesture_data.name}'?\n\n"
            f"This will permanently remove:\n"
            f"• {len(gesture_data.training_samples)} training samples\n"
            f"• The trained model (if any)\n"
            f"• All gesture data\n\n"
            f"This action cannot be undone.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            success = self.gesture_manager.delete_gesture(self.selected_gesture_id)

            if success:
                QMessageBox.information(self, "Gesture Deleted",
                                      f"Gesture '{gesture_data.name}' has been deleted.")
                self.gesture_deleted.emit(self.selected_gesture_id)
                self.refresh_gesture_list()
                logger.info(f"Deleted gesture: {gesture_data.name}")
            else:
                QMessageBox.critical(self, "Deletion Failed",
                                   f"Failed to delete gesture '{gesture_data.name}'.")

    def _format_date(self, date_string: str) -> str:
        """Format ISO date string for display."""
        try:
            dt = datetime.fromisoformat(date_string.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M")
        except Exception:
            return date_string
