# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/util/tracking/frame_selection_solution_evaluator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n@mediapipe/util/tracking/frame_selection_solution_evaluator.proto\x12\tmediapipe\"4\n&FrameSelectionSolutionEvaluatorOptions*\n\x08\xa0\x9c\x01\x10\x80\x80\x80\x80\x02\"\x9e\x01\n#FrameSelectionSolutionEvaluatorType\x12\x33\n\nclass_name\x18\x01 \x01(\t:\x1f\x46rameSelectionSolutionEvaluator\x12\x42\n\x07options\x18\x02 \x01(\x0b\x32\x31.mediapipe.FrameSelectionSolutionEvaluatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.util.tracking.frame_selection_solution_evaluator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_FRAMESELECTIONSOLUTIONEVALUATOROPTIONS']._serialized_start=79
  _globals['_FRAMESELECTIONSOLUTIONEVALUATOROPTIONS']._serialized_end=131
  _globals['_FRAMESELECTIONSOLUTIONEVALUATORTYPE']._serialized_start=134
  _globals['_FRAMESELECTIONSOLUTIONEVALUATORTYPE']._serialized_end=292
# @@protoc_insertion_point(module_scope)
