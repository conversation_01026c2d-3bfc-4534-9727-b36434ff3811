# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/stream_handler/sync_set_input_stream_handler.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import mediapipe_options_pb2 as mediapipe_dot_framework_dot_mediapipe__options__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nFmediapipe/framework/stream_handler/sync_set_input_stream_handler.proto\x12\tmediapipe\x1a+mediapipe/framework/mediapipe_options.proto\"\xe1\x01\n SyncSetInputStreamHandlerOptions\x12\x45\n\x08sync_set\x18\x01 \x03(\x0b\x32\x33.mediapipe.SyncSetInputStreamHandlerOptions.SyncSet\x1a\x1c\n\x07SyncSet\x12\x11\n\ttag_index\x18\x01 \x03(\t2X\n\x03\x65xt\x12\x1b.mediapipe.MediaPipeOptions\x18\xd1\xa2\xa6J \x01(\x0b\x32+.mediapipe.SyncSetInputStreamHandlerOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.stream_handler.sync_set_input_stream_handler_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_mediapipe__options__pb2.MediaPipeOptions.RegisterExtension(_SYNCSETINPUTSTREAMHANDLEROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_SYNCSETINPUTSTREAMHANDLEROPTIONS']._serialized_start=131
  _globals['_SYNCSETINPUTSTREAMHANDLEROPTIONS']._serialized_end=356
  _globals['_SYNCSETINPUTSTREAMHANDLEROPTIONS_SYNCSET']._serialized_start=238
  _globals['_SYNCSETINPUTSTREAMHANDLEROPTIONS_SYNCSET']._serialized_end=266
# @@protoc_insertion_point(module_scope)
