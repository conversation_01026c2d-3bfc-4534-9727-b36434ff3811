"""
Custom Gesture Manager for GestureFlow application.
Handles data structure, storage, and management of user-defined gestures.
Implements GFLOW-5: Design Custom Static Gesture Data Structure & Storage
"""

import os
import json
import pickle
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import numpy as np

from src.core.hand_tracker import HandLandmarks
from src.utils.logger import logger


@dataclass
class CustomGestureData:
    """Data structure for a custom gesture."""
    id: str
    name: str
    description: str
    created_date: str
    modified_date: str
    training_samples: List[List[Tuple[float, float, float]]]  # List of landmark sequences
    feature_vectors: List[List[float]]  # Extracted features for training
    model_path: Optional[str] = None
    accuracy: Optional[float] = None
    confidence_threshold: float = 0.7
    enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CustomGestureData':
        """Create instance from dictionary."""
        return cls(**data)


class CustomGestureManager:
    """
    Manages custom gesture data, storage, and operations.
    Provides interface for creating, saving, loading, and managing custom gestures.
    """
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize custom gesture manager.
        
        Args:
            data_dir: Base directory for storing gesture data
        """
        self.data_dir = data_dir
        self.gestures_dir = os.path.join(data_dir, "custom_gestures")
        self.models_dir = os.path.join(data_dir, "models")
        self.metadata_file = os.path.join(data_dir, "custom_gestures_metadata.json")
        
        # Create directories if they don't exist
        self._ensure_directories()
        
        # Load existing gestures
        self.custom_gestures: Dict[str, CustomGestureData] = {}
        self.load_all_gestures()
        
        logger.info(f"CustomGestureManager initialized with {len(self.custom_gestures)} gestures")
    
    def _ensure_directories(self):
        """Create necessary directories for gesture storage."""
        for directory in [self.data_dir, self.gestures_dir, self.models_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"Created directory: {directory}")
    
    def create_new_gesture(self, name: str, description: str = "") -> str:
        """
        Create a new custom gesture.
        
        Args:
            name: Name of the gesture
            description: Optional description
            
        Returns:
            Unique ID of the created gesture
        """
        # Generate unique ID
        gesture_id = str(uuid.uuid4())
        
        # Create gesture data
        gesture_data = CustomGestureData(
            id=gesture_id,
            name=name,
            description=description,
            created_date=datetime.now().isoformat(),
            modified_date=datetime.now().isoformat(),
            training_samples=[],
            feature_vectors=[]
        )
        
        # Store in memory
        self.custom_gestures[gesture_id] = gesture_data
        
        # Save to disk
        self.save_gesture(gesture_id)
        self.save_metadata()
        
        logger.info(f"Created new custom gesture: {name} (ID: {gesture_id})")
        return gesture_id
    
    def add_training_sample(self, gesture_id: str, hand_landmarks: HandLandmarks) -> bool:
        """
        Add a training sample to a custom gesture.
        
        Args:
            gesture_id: ID of the gesture
            hand_landmarks: Hand landmarks data
            
        Returns:
            True if sample was added successfully
        """
        if gesture_id not in self.custom_gestures:
            logger.error(f"Gesture ID {gesture_id} not found")
            return False
        
        gesture = self.custom_gestures[gesture_id]
        
        # Add landmarks as training sample
        gesture.training_samples.append(hand_landmarks.landmarks)
        gesture.modified_date = datetime.now().isoformat()
        
        # Save updated gesture
        self.save_gesture(gesture_id)
        
        logger.info(f"Added training sample to gesture {gesture.name} "
                   f"(total samples: {len(gesture.training_samples)})")
        return True
    
    def get_gesture(self, gesture_id: str) -> Optional[CustomGestureData]:
        """Get gesture data by ID."""
        return self.custom_gestures.get(gesture_id)
    
    def get_all_gestures(self) -> Dict[str, CustomGestureData]:
        """Get all custom gestures."""
        return self.custom_gestures.copy()
    
    def get_enabled_gestures(self) -> Dict[str, CustomGestureData]:
        """Get only enabled custom gestures."""
        return {gid: gesture for gid, gesture in self.custom_gestures.items() 
                if gesture.enabled}
    
    def delete_gesture(self, gesture_id: str) -> bool:
        """
        Delete a custom gesture and its associated files.
        
        Args:
            gesture_id: ID of the gesture to delete
            
        Returns:
            True if deletion was successful
        """
        if gesture_id not in self.custom_gestures:
            logger.error(f"Gesture ID {gesture_id} not found")
            return False
        
        gesture = self.custom_gestures[gesture_id]
        
        try:
            # Delete gesture data file
            gesture_file = os.path.join(self.gestures_dir, f"{gesture_id}.json")
            if os.path.exists(gesture_file):
                os.remove(gesture_file)
            
            # Delete model file if it exists
            if gesture.model_path and os.path.exists(gesture.model_path):
                os.remove(gesture.model_path)
            
            # Remove from memory
            del self.custom_gestures[gesture_id]
            
            # Update metadata
            self.save_metadata()
            
            logger.info(f"Deleted custom gesture: {gesture.name} (ID: {gesture_id})")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting gesture {gesture_id}: {e}")
            return False
    
    def save_gesture(self, gesture_id: str) -> bool:
        """
        Save a specific gesture to disk.
        
        Args:
            gesture_id: ID of the gesture to save
            
        Returns:
            True if save was successful
        """
        if gesture_id not in self.custom_gestures:
            logger.error(f"Gesture ID {gesture_id} not found")
            return False
        
        try:
            gesture = self.custom_gestures[gesture_id]
            gesture_file = os.path.join(self.gestures_dir, f"{gesture_id}.json")
            
            with open(gesture_file, 'w') as f:
                json.dump(gesture.to_dict(), f, indent=2)
            
            return True
            
        except Exception as e:
            logger.error(f"Error saving gesture {gesture_id}: {e}")
            return False
    
    def load_gesture(self, gesture_id: str) -> bool:
        """
        Load a specific gesture from disk.
        
        Args:
            gesture_id: ID of the gesture to load
            
        Returns:
            True if load was successful
        """
        try:
            gesture_file = os.path.join(self.gestures_dir, f"{gesture_id}.json")
            
            if not os.path.exists(gesture_file):
                logger.warning(f"Gesture file not found: {gesture_file}")
                return False
            
            with open(gesture_file, 'r') as f:
                gesture_data = json.load(f)
            
            gesture = CustomGestureData.from_dict(gesture_data)
            self.custom_gestures[gesture_id] = gesture
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading gesture {gesture_id}: {e}")
            return False
    
    def load_all_gestures(self) -> int:
        """
        Load all gestures from disk.
        
        Returns:
            Number of gestures loaded
        """
        loaded_count = 0
        
        try:
            # Load metadata if it exists
            if os.path.exists(self.metadata_file):
                with open(self.metadata_file, 'r') as f:
                    metadata = json.load(f)
                    gesture_ids = metadata.get('gesture_ids', [])
            else:
                # Fallback: scan directory for gesture files
                gesture_ids = []
                if os.path.exists(self.gestures_dir):
                    for filename in os.listdir(self.gestures_dir):
                        if filename.endswith('.json'):
                            gesture_ids.append(filename[:-5])  # Remove .json extension
            
            # Load each gesture
            for gesture_id in gesture_ids:
                if self.load_gesture(gesture_id):
                    loaded_count += 1
            
            logger.info(f"Loaded {loaded_count} custom gestures")
            return loaded_count
            
        except Exception as e:
            logger.error(f"Error loading gestures: {e}")
            return loaded_count
    
    def save_metadata(self) -> bool:
        """Save metadata about all gestures."""
        try:
            metadata = {
                'gesture_ids': list(self.custom_gestures.keys()),
                'total_gestures': len(self.custom_gestures),
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            return True
            
        except Exception as e:
            logger.error(f"Error saving metadata: {e}")
            return False
    
    def update_gesture_model(self, gesture_id: str, model_path: str, accuracy: float) -> bool:
        """
        Update gesture with trained model information.
        
        Args:
            gesture_id: ID of the gesture
            model_path: Path to the trained model file
            accuracy: Training accuracy
            
        Returns:
            True if update was successful
        """
        if gesture_id not in self.custom_gestures:
            logger.error(f"Gesture ID {gesture_id} not found")
            return False
        
        gesture = self.custom_gestures[gesture_id]
        gesture.model_path = model_path
        gesture.accuracy = accuracy
        gesture.modified_date = datetime.now().isoformat()
        
        # Save updated gesture
        return self.save_gesture(gesture_id)
    
    def get_gesture_statistics(self) -> Dict[str, Any]:
        """Get statistics about custom gestures."""
        total_gestures = len(self.custom_gestures)
        enabled_gestures = len(self.get_enabled_gestures())
        trained_gestures = len([g for g in self.custom_gestures.values() if g.model_path])
        
        total_samples = sum(len(g.training_samples) for g in self.custom_gestures.values())
        avg_samples = total_samples / total_gestures if total_gestures > 0 else 0
        
        return {
            'total_gestures': total_gestures,
            'enabled_gestures': enabled_gestures,
            'trained_gestures': trained_gestures,
            'total_training_samples': total_samples,
            'average_samples_per_gesture': avg_samples
        }
