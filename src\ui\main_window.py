"""
Main Application Window for GestureFlow.
Provides the primary user interface for gesture recognition control.
"""

import sys
import cv2
import numpy as np
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                           QPushButton, QLabel, QTextEdit, QGroupBox, QGridLayout,
                           QApplication, QMessageBox, QStatusBar)
from PyQt5.QtCore import QTimer, QThread, pyqtSignal, Qt
from PyQt5.QtGui import QImage, QPixmap, QFont
from typing import Optional

from src.core.webcam_manager import WebcamManager
from src.core.gesture_recognizer import StaticGestureRecog<PERSON><PERSON>, GestureResult, GestureType
from src.core.performance_monitor import PerformanceMonitor
from src.core.custom_gesture_manager import CustomGestureManager
from src.core.gesture_trainer import GestureTrainer
from src.utils.logger import logger

# Try to import MediaPipe-based hand tracker, fall back to OpenCV-based version
try:
    from src.core.hand_tracker import HandTracker, HandLandmarks
    MEDIAPIPE_AVAILABLE = True
    logger.info("Using MediaPipe-based hand tracking")
except ImportError as e:
    logger.warning(f"MediaPipe not available: {e}")
    logger.info("Falling back to OpenCV-based hand tracking")
    from src.core.hand_tracker_fallback import FallbackHandTracker as HandTracker, HandLandmarks
    MEDIAPIPE_AVAILABLE = False


class GestureRecognitionThread(QThread):
    """Thread for running gesture recognition to avoid blocking UI."""

    frame_processed = pyqtSignal(np.ndarray, list, object)  # frame, hand_landmarks, gesture_result
    error_occurred = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.webcam_manager = WebcamManager()
        self.hand_tracker = HandTracker()
        self.gesture_recognizer = StaticGestureRecognizer()
        self.performance_monitor = PerformanceMonitor()

        # Custom gesture support
        self.custom_gesture_manager = CustomGestureManager()
        self.gesture_trainer = GestureTrainer()

        # Initialize custom gesture support in recognizer
        self.gesture_recognizer.set_custom_gesture_support(
            self.custom_gesture_manager, self.gesture_trainer
        )

        self.is_running = False
        self.show_landmarks = True
        self.current_hand_landmarks = []  # Store current hand landmarks for custom gesture recording

    def start_recognition(self):
        """Start gesture recognition."""
        if not self.webcam_manager.is_camera_available():
            self.error_occurred.emit("No camera detected. Please connect a webcam.")
            return False

        if not self.webcam_manager.start_capture():
            self.error_occurred.emit("Failed to start camera capture.")
            return False

        self.performance_monitor.start_monitoring()
        self.is_running = True
        self.start()
        return True

    def stop_recognition(self):
        """Stop gesture recognition."""
        self.is_running = False
        self.webcam_manager.stop_capture()
        self.performance_monitor.stop_monitoring()
        self.wait()

    def toggle_landmarks(self, show: bool):
        """Toggle landmark display."""
        self.show_landmarks = show

    def run(self):
        """Main recognition loop."""
        logger.info("Gesture recognition thread started")

        while self.is_running:
            try:
                self.performance_monitor.start_frame_processing()

                # Get frame from webcam
                success, frame = self.webcam_manager.get_frame()
                if not success or frame is None:
                    continue

                # Process hand tracking
                processed_frame, hand_landmarks_list = self.hand_tracker.process_frame(frame)

                # Store current hand landmarks for custom gesture recording
                self.current_hand_landmarks = hand_landmarks_list

                # Recognize gestures
                gesture_result = None
                if hand_landmarks_list:
                    self.performance_monitor.start_gesture_recognition()

                    # For now, just process the first detected hand
                    hand_landmarks = hand_landmarks_list[0]
                    gesture_result = self.gesture_recognizer.recognize_gesture(hand_landmarks)

                    self.performance_monitor.end_gesture_recognition(
                        gesture_result.gesture_type.value,
                        gesture_result.confidence
                    )

                # Draw landmarks if enabled
                if self.show_landmarks and hand_landmarks_list:
                    processed_frame = self.hand_tracker.draw_landmarks(processed_frame, hand_landmarks_list)

                # Emit processed frame and results
                self.frame_processed.emit(processed_frame, hand_landmarks_list, gesture_result)

                self.performance_monitor.end_frame_processing()

            except Exception as e:
                logger.error(f"Error in recognition thread: {e}")
                self.error_occurred.emit(f"Recognition error: {str(e)}")

        logger.info("Gesture recognition thread stopped")


class MainWindow(QMainWindow):
    """Main application window for GestureFlow."""

    def __init__(self):
        super().__init__()
        self.recognition_thread = GestureRecognitionThread()
        self.setup_ui()
        self.connect_signals()

        # Update timer for performance metrics
        self.metrics_timer = QTimer()
        self.metrics_timer.timeout.connect(self.update_metrics_display)
        self.metrics_timer.start(1000)  # Update every second

        logger.info("MainWindow initialized")

    def setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("GestureFlow - Hand Gesture Recognition")
        self.setGeometry(100, 100, 1200, 800)

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QHBoxLayout(central_widget)

        # Left panel - Video feed
        video_group = QGroupBox("Camera Feed")
        video_layout = QVBoxLayout(video_group)

        self.video_label = QLabel("Camera feed will appear here")
        self.video_label.setMinimumSize(640, 480)
        self.video_label.setStyleSheet("border: 1px solid gray; background-color: black;")
        self.video_label.setAlignment(Qt.AlignCenter)
        video_layout.addWidget(self.video_label)

        # Video controls
        video_controls = QHBoxLayout()
        self.start_button = QPushButton("Start Recognition")
        self.stop_button = QPushButton("Stop Recognition")
        self.landmarks_button = QPushButton("Toggle Landmarks")

        self.stop_button.setEnabled(False)

        video_controls.addWidget(self.start_button)
        video_controls.addWidget(self.stop_button)
        video_controls.addWidget(self.landmarks_button)
        video_layout.addLayout(video_controls)

        # Custom gesture controls
        custom_controls = QHBoxLayout()
        self.new_gesture_button = QPushButton("New Custom Gesture")
        self.manage_gestures_button = QPushButton("Manage Gestures")

        custom_controls.addWidget(self.new_gesture_button)
        custom_controls.addWidget(self.manage_gestures_button)
        video_layout.addLayout(custom_controls)

        main_layout.addWidget(video_group, 2)

        # Right panel - Information and controls
        info_layout = QVBoxLayout()

        # Gesture recognition status
        status_group = QGroupBox("Recognition Status")
        status_layout = QGridLayout(status_group)

        self.status_label = QLabel("Status: Stopped")
        self.gesture_label = QLabel("Gesture: None")
        self.confidence_label = QLabel("Confidence: 0%")
        self.hands_label = QLabel("Hands Detected: 0")

        # Add tracking method indicator
        tracking_method = "MediaPipe" if MEDIAPIPE_AVAILABLE else "OpenCV (Fallback)"
        self.tracking_label = QLabel(f"Tracking: {tracking_method}")
        if not MEDIAPIPE_AVAILABLE:
            self.tracking_label.setStyleSheet("color: orange;")

        status_layout.addWidget(QLabel("Status:"), 0, 0)
        status_layout.addWidget(self.status_label, 0, 1)
        status_layout.addWidget(QLabel("Tracking:"), 1, 0)
        status_layout.addWidget(self.tracking_label, 1, 1)
        status_layout.addWidget(QLabel("Current Gesture:"), 2, 0)
        status_layout.addWidget(self.gesture_label, 2, 1)
        status_layout.addWidget(QLabel("Confidence:"), 3, 0)
        status_layout.addWidget(self.confidence_label, 3, 1)
        status_layout.addWidget(QLabel("Hands:"), 4, 0)
        status_layout.addWidget(self.hands_label, 4, 1)

        info_layout.addWidget(status_group)

        # Performance metrics
        metrics_group = QGroupBox("Performance Metrics")
        metrics_layout = QGridLayout(metrics_group)

        self.fps_label = QLabel("FPS: 0")
        self.cpu_label = QLabel("CPU: 0%")
        self.memory_label = QLabel("Memory: 0%")
        self.latency_label = QLabel("Latency: 0ms")

        metrics_layout.addWidget(QLabel("FPS:"), 0, 0)
        metrics_layout.addWidget(self.fps_label, 0, 1)
        metrics_layout.addWidget(QLabel("CPU Usage:"), 1, 0)
        metrics_layout.addWidget(self.cpu_label, 1, 1)
        metrics_layout.addWidget(QLabel("Memory:"), 2, 0)
        metrics_layout.addWidget(self.memory_label, 2, 1)
        metrics_layout.addWidget(QLabel("Latency:"), 3, 0)
        metrics_layout.addWidget(self.latency_label, 3, 1)

        info_layout.addWidget(metrics_group)

        # Supported gestures info
        gestures_group = QGroupBox("Supported Gestures")
        gestures_layout = QVBoxLayout(gestures_group)

        gesture_list = QTextEdit()
        gesture_list.setReadOnly(True)
        gesture_list.setMaximumHeight(150)
        gesture_text = """Supported Static Gestures:
• Open Palm - All fingers extended
• Fist - All fingers curled
• Peace Sign - Index and middle fingers extended
• Thumbs Up - Thumb extended upward
• Pointing - Index finger extended"""
        gesture_list.setPlainText(gesture_text)
        gestures_layout.addWidget(gesture_list)

        info_layout.addWidget(gestures_group)

        # Add stretch to push everything to top
        info_layout.addStretch()

        # Create widget for right panel
        info_widget = QWidget()
        info_widget.setLayout(info_layout)
        info_widget.setMaximumWidth(350)

        main_layout.addWidget(info_widget, 1)

        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

    def connect_signals(self):
        """Connect UI signals to handlers."""
        self.start_button.clicked.connect(self.start_recognition)
        self.stop_button.clicked.connect(self.stop_recognition)
        self.landmarks_button.clicked.connect(self.toggle_landmarks)
        self.new_gesture_button.clicked.connect(self.open_new_gesture_dialog)
        self.manage_gestures_button.clicked.connect(self.open_manage_gestures_dialog)

        self.recognition_thread.frame_processed.connect(self.update_video_display)
        self.recognition_thread.error_occurred.connect(self.show_error)

    def start_recognition(self):
        """Start gesture recognition."""
        if self.recognition_thread.start_recognition():
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("Running")
            self.status_bar.showMessage("Gesture recognition started")
            logger.info("Gesture recognition started from UI")

    def stop_recognition(self):
        """Stop gesture recognition."""
        self.recognition_thread.stop_recognition()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("Stopped")
        self.video_label.setText("Camera feed will appear here")
        self.status_bar.showMessage("Gesture recognition stopped")
        logger.info("Gesture recognition stopped from UI")

    def toggle_landmarks(self):
        """Toggle landmark display."""
        current_state = self.recognition_thread.show_landmarks
        self.recognition_thread.toggle_landmarks(not current_state)
        status = "enabled" if not current_state else "disabled"
        self.status_bar.showMessage(f"Landmark display {status}")

    def update_video_display(self, frame: np.ndarray, hand_landmarks_list: list, gesture_result: Optional[object]):
        """Update video display with processed frame."""
        try:
            # Convert frame to Qt format
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_frame.shape
            bytes_per_line = ch * w
            qt_image = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format_RGB888)

            # Scale image to fit label
            pixmap = QPixmap.fromImage(qt_image)
            scaled_pixmap = pixmap.scaled(self.video_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.video_label.setPixmap(scaled_pixmap)

            # Update gesture information
            self.hands_label.setText(f"{len(hand_landmarks_list)}")

            if gesture_result and gesture_result.gesture_type != GestureType.UNKNOWN:
                if gesture_result.gesture_type == GestureType.CUSTOM and gesture_result.gesture_name:
                    self.gesture_label.setText(gesture_result.gesture_name)
                else:
                    self.gesture_label.setText(gesture_result.gesture_type.value.replace('_', ' ').title())
                self.confidence_label.setText(f"{gesture_result.confidence:.1%}")
            else:
                self.gesture_label.setText("None")
                self.confidence_label.setText("0%")

        except Exception as e:
            logger.error(f"Error updating video display: {e}")

    def update_metrics_display(self):
        """Update performance metrics display."""
        if not self.recognition_thread.is_running:
            return

        try:
            metrics = self.recognition_thread.performance_monitor.get_current_metrics()

            self.fps_label.setText(f"{metrics.fps:.1f}")
            self.cpu_label.setText(f"{metrics.cpu_usage:.1f}%")
            self.memory_label.setText(f"{metrics.memory_usage:.1f}%")
            self.latency_label.setText(f"{metrics.recognition_latency*1000:.1f}ms")

        except Exception as e:
            logger.error(f"Error updating metrics display: {e}")

    def open_new_gesture_dialog(self):
        """Open dialog for creating new custom gesture."""
        try:
            from src.ui.custom_gesture_dialog import CustomGestureRecordingDialog

            dialog = CustomGestureRecordingDialog(
                self.recognition_thread.custom_gesture_manager,
                self.recognition_thread.gesture_trainer,
                self
            )

            # Connect to receive hand landmarks updates
            if self.recognition_thread.is_running:
                # Pass current hand landmarks to dialog
                def update_landmarks():
                    if hasattr(self.recognition_thread, 'current_hand_landmarks'):
                        dialog.update_hand_landmarks(self.recognition_thread.current_hand_landmarks or [])

                # Update landmarks periodically
                timer = QTimer()
                timer.timeout.connect(update_landmarks)
                timer.start(100)  # Update every 100ms

                # Connect gesture creation signal
                dialog.gesture_created.connect(self.on_gesture_created)

                result = dialog.exec_()
                timer.stop()

                if result == QDialog.Accepted:
                    self.status_bar.showMessage("Custom gesture created successfully!")
            else:
                QMessageBox.information(self, "Start Recognition",
                                      "Please start gesture recognition first to record custom gestures.")

        except Exception as e:
            logger.error(f"Error opening new gesture dialog: {e}")
            QMessageBox.critical(self, "Error", f"Failed to open gesture recording dialog: {e}")

    def open_manage_gestures_dialog(self):
        """Open dialog for managing custom gestures."""
        try:
            from src.ui.gesture_management_dialog import GestureManagementDialog

            dialog = GestureManagementDialog(
                self.recognition_thread.custom_gesture_manager,
                self
            )

            # Connect signals
            dialog.gesture_deleted.connect(self.on_gesture_deleted)
            dialog.gesture_updated.connect(self.on_gesture_updated)

            dialog.exec_()

        except Exception as e:
            logger.error(f"Error opening manage gestures dialog: {e}")
            QMessageBox.critical(self, "Error", f"Failed to open gesture management dialog: {e}")

    def on_gesture_created(self, gesture_id: str):
        """Handle new gesture creation."""
        # Reload custom gestures in recognizer
        self.recognition_thread.gesture_recognizer.reload_custom_gestures()
        logger.info(f"New gesture created: {gesture_id}")

    def on_gesture_deleted(self, gesture_id: str):
        """Handle gesture deletion."""
        # Reload custom gestures in recognizer
        self.recognition_thread.gesture_recognizer.reload_custom_gestures()
        logger.info(f"Gesture deleted: {gesture_id}")

    def on_gesture_updated(self, gesture_id: str):
        """Handle gesture update (enable/disable)."""
        # Reload custom gestures in recognizer
        self.recognition_thread.gesture_recognizer.reload_custom_gestures()
        logger.info(f"Gesture updated: {gesture_id}")

    def show_error(self, error_message: str):
        """Show error message to user."""
        QMessageBox.critical(self, "Error", error_message)
        logger.error(f"UI Error: {error_message}")

    def closeEvent(self, event):
        """Handle application close event."""
        if self.recognition_thread.is_running:
            self.stop_recognition()
        event.accept()
        logger.info("Application closed")
