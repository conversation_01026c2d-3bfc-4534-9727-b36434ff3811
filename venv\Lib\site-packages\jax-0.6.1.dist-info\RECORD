jax-0.6.1.dist-info/AUTHORS,sha256=OOBQygrI1Zc94AYJycH1r_BSCwP38Hyv3sludltJ7hY,313
jax-0.6.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
jax-0.6.1.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
jax-0.6.1.dist-info/METADATA,sha256=FFBnvx2Otp_pUlFMIj-xuMdncuDc1ooPFc6wvO0tHaU,13101
jax-0.6.1.dist-info/RECORD,,
jax-0.6.1.dist-info/WHEEL,sha256=52BFRY2Up02UkjOa29eZOS2VxUrpPORXg1pkohGGUS8,91
jax-0.6.1.dist-info/top_level.txt,sha256=DabAh8MXQ-HM0EtjKD-Wx_D6tTIW-_de73pdG_f_KTE,4
jax/__init__.py,sha256=LD82Acv1D4o9YeBUcnz7unFDFzjLRVkDToNoifV2bvQ,9291
jax/__pycache__/__init__.cpython-312.pyc,,
jax/__pycache__/ad_checkpoint.cpython-312.pyc,,
jax/__pycache__/api_util.cpython-312.pyc,,
jax/__pycache__/cloud_tpu_init.cpython-312.pyc,,
jax/__pycache__/collect_profile.cpython-312.pyc,,
jax/__pycache__/core.cpython-312.pyc,,
jax/__pycache__/custom_batching.cpython-312.pyc,,
jax/__pycache__/custom_derivatives.cpython-312.pyc,,
jax/__pycache__/custom_transpose.cpython-312.pyc,,
jax/__pycache__/debug.cpython-312.pyc,,
jax/__pycache__/distributed.cpython-312.pyc,,
jax/__pycache__/dlpack.cpython-312.pyc,,
jax/__pycache__/dtypes.cpython-312.pyc,,
jax/__pycache__/errors.cpython-312.pyc,,
jax/__pycache__/export.cpython-312.pyc,,
jax/__pycache__/ffi.cpython-312.pyc,,
jax/__pycache__/flatten_util.cpython-312.pyc,,
jax/__pycache__/monitoring.cpython-312.pyc,,
jax/__pycache__/profiler.cpython-312.pyc,,
jax/__pycache__/random.cpython-312.pyc,,
jax/__pycache__/sharding.cpython-312.pyc,,
jax/__pycache__/stages.cpython-312.pyc,,
jax/__pycache__/test_util.cpython-312.pyc,,
jax/__pycache__/tree.cpython-312.pyc,,
jax/__pycache__/tree_util.cpython-312.pyc,,
jax/__pycache__/typing.cpython-312.pyc,,
jax/__pycache__/util.cpython-312.pyc,,
jax/__pycache__/version.cpython-312.pyc,,
jax/_src/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/__pycache__/__init__.cpython-312.pyc,,
jax/_src/__pycache__/abstract_arrays.cpython-312.pyc,,
jax/_src/__pycache__/ad_checkpoint.cpython-312.pyc,,
jax/_src/__pycache__/ad_util.cpython-312.pyc,,
jax/_src/__pycache__/api.cpython-312.pyc,,
jax/_src/__pycache__/api_util.cpython-312.pyc,,
jax/_src/__pycache__/array.cpython-312.pyc,,
jax/_src/__pycache__/attrs.cpython-312.pyc,,
jax/_src/__pycache__/basearray.cpython-312.pyc,,
jax/_src/__pycache__/blocked_sampler.cpython-312.pyc,,
jax/_src/__pycache__/buffer_callback.cpython-312.pyc,,
jax/_src/__pycache__/cache_key.cpython-312.pyc,,
jax/_src/__pycache__/callback.cpython-312.pyc,,
jax/_src/__pycache__/checkify.cpython-312.pyc,,
jax/_src/__pycache__/cloud_tpu_init.cpython-312.pyc,,
jax/_src/__pycache__/compilation_cache.cpython-312.pyc,,
jax/_src/__pycache__/compilation_cache_interface.cpython-312.pyc,,
jax/_src/__pycache__/compiler.cpython-312.pyc,,
jax/_src/__pycache__/compute_on.cpython-312.pyc,,
jax/_src/__pycache__/config.cpython-312.pyc,,
jax/_src/__pycache__/core.cpython-312.pyc,,
jax/_src/__pycache__/custom_api_util.cpython-312.pyc,,
jax/_src/__pycache__/custom_batching.cpython-312.pyc,,
jax/_src/__pycache__/custom_dce.cpython-312.pyc,,
jax/_src/__pycache__/custom_derivatives.cpython-312.pyc,,
jax/_src/__pycache__/custom_partitioning.cpython-312.pyc,,
jax/_src/__pycache__/custom_partitioning_sharding_rule.cpython-312.pyc,,
jax/_src/__pycache__/custom_transpose.cpython-312.pyc,,
jax/_src/__pycache__/debugging.cpython-312.pyc,,
jax/_src/__pycache__/deprecations.cpython-312.pyc,,
jax/_src/__pycache__/dispatch.cpython-312.pyc,,
jax/_src/__pycache__/distributed.cpython-312.pyc,,
jax/_src/__pycache__/dlpack.cpython-312.pyc,,
jax/_src/__pycache__/dtypes.cpython-312.pyc,,
jax/_src/__pycache__/earray.cpython-312.pyc,,
jax/_src/__pycache__/effects.cpython-312.pyc,,
jax/_src/__pycache__/environment_info.cpython-312.pyc,,
jax/_src/__pycache__/error_check.cpython-312.pyc,,
jax/_src/__pycache__/errors.cpython-312.pyc,,
jax/_src/__pycache__/ffi.cpython-312.pyc,,
jax/_src/__pycache__/flatten_util.cpython-312.pyc,,
jax/_src/__pycache__/hardware_utils.cpython-312.pyc,,
jax/_src/__pycache__/jaxpr_util.cpython-312.pyc,,
jax/_src/__pycache__/lax_reference.cpython-312.pyc,,
jax/_src/__pycache__/layout.cpython-312.pyc,,
jax/_src/__pycache__/lazy_loader.cpython-312.pyc,,
jax/_src/__pycache__/linear_util.cpython-312.pyc,,
jax/_src/__pycache__/logging_config.cpython-312.pyc,,
jax/_src/__pycache__/lru_cache.cpython-312.pyc,,
jax/_src/__pycache__/mesh.cpython-312.pyc,,
jax/_src/__pycache__/mesh_utils.cpython-312.pyc,,
jax/_src/__pycache__/monitoring.cpython-312.pyc,,
jax/_src/__pycache__/named_sharding.cpython-312.pyc,,
jax/_src/__pycache__/op_shardings.cpython-312.pyc,,
jax/_src/__pycache__/partition_spec.cpython-312.pyc,,
jax/_src/__pycache__/path.cpython-312.pyc,,
jax/_src/__pycache__/pickle_util.cpython-312.pyc,,
jax/_src/__pycache__/pjit.cpython-312.pyc,,
jax/_src/__pycache__/pretty_printer.cpython-312.pyc,,
jax/_src/__pycache__/prng.cpython-312.pyc,,
jax/_src/__pycache__/profiler.cpython-312.pyc,,
jax/_src/__pycache__/public_test_util.cpython-312.pyc,,
jax/_src/__pycache__/random.cpython-312.pyc,,
jax/_src/__pycache__/shard_alike.cpython-312.pyc,,
jax/_src/__pycache__/shard_map.cpython-312.pyc,,
jax/_src/__pycache__/sharding.cpython-312.pyc,,
jax/_src/__pycache__/sharding_impls.cpython-312.pyc,,
jax/_src/__pycache__/sharding_specs.cpython-312.pyc,,
jax/_src/__pycache__/source_info_util.cpython-312.pyc,,
jax/_src/__pycache__/sourcemap.cpython-312.pyc,,
jax/_src/__pycache__/stages.cpython-312.pyc,,
jax/_src/__pycache__/test_loader.cpython-312.pyc,,
jax/_src/__pycache__/test_util.cpython-312.pyc,,
jax/_src/__pycache__/test_warning_util.cpython-312.pyc,,
jax/_src/__pycache__/tpu_custom_call.cpython-312.pyc,,
jax/_src/__pycache__/traceback_util.cpython-312.pyc,,
jax/_src/__pycache__/tree.cpython-312.pyc,,
jax/_src/__pycache__/tree_util.cpython-312.pyc,,
jax/_src/__pycache__/typing.cpython-312.pyc,,
jax/_src/__pycache__/util.cpython-312.pyc,,
jax/_src/__pycache__/xla_bridge.cpython-312.pyc,,
jax/_src/__pycache__/xla_metadata.cpython-312.pyc,,
jax/_src/abstract_arrays.py,sha256=1kxQA1qnwmri9_GeZHerQmghGZ-zHOy-etSyGHIhTIE,2889
jax/_src/ad_checkpoint.py,sha256=0FTckQVK1x36ZhrEIqE8oYz7tVud6XROGH4oBg87Gdo,37180
jax/_src/ad_util.py,sha256=i1Oj_yGWZXcPb33GVukSYkLskVN3L4Kt0AUZ2F-i8wc,4446
jax/_src/api.py,sha256=f92OBh8V1GJGzTCflfHTJFT_SNMitcpT319QRB0APZs,137010
jax/_src/api_util.py,sha256=kflDDV8L1QUCeHyNhUqXGOxMrQ8QUeCgPV25Jv3bz_w,29191
jax/_src/array.py,sha256=aRRA17sApdApSN0FPyQVuWIDwfInSFquL7dEue0rVXU,49375
jax/_src/attrs.py,sha256=fZwSxnEpoYvexdv7tOzYJlr59aaettnVNDwDVhwCvl4,15353
jax/_src/basearray.py,sha256=FQOcRwUhh1w4byHijEzCZAVqa8Tukr3bSr4fEqZz1q4,6904
jax/_src/basearray.pyi,sha256=e0CykmZ_wlCr0w9FDk4vLHFBI-akpzeJ5QLSdQPWhyc,13463
jax/_src/blocked_sampler.py,sha256=OtV8uaYBfdGsWOpYD6LG9JPK1yknKkrJuLPAJ_LJPeM,5915
jax/_src/buffer_callback.py,sha256=1Lutj9tRiNwEtOm3l-d6odSsfhtO4Es7ESHn59RyXLM,10629
jax/_src/cache_key.py,sha256=5PjcNKTsM50djKfpw29HHU4kBa5XntSWTCtQV7TTkBU,12922
jax/_src/callback.py,sha256=btWkqLec5IvklST1EJ8TrUwTmNq8FyD4WCQGtFuqQOA,33089
jax/_src/checkify.py,sha256=hZHK1ixcQroaaKq9By7QhMn5NWVENscCNe8pVsepu48,57782
jax/_src/cloud_tpu_init.py,sha256=nHNo6ZZV6r3mvv8vCDp6Dzva6bhirmN-MXoAh8u8Dns,4807
jax/_src/clusters/__init__.py,sha256=blIgNT92mYE9Bst7obP5_xhZ2fE2hKqwxH6R0cAQZ94,1283
jax/_src/clusters/__pycache__/__init__.cpython-312.pyc,,
jax/_src/clusters/__pycache__/cloud_tpu_cluster.cpython-312.pyc,,
jax/_src/clusters/__pycache__/cluster.cpython-312.pyc,,
jax/_src/clusters/__pycache__/k8s_cluster.cpython-312.pyc,,
jax/_src/clusters/__pycache__/mpi4py_cluster.cpython-312.pyc,,
jax/_src/clusters/__pycache__/ompi_cluster.cpython-312.pyc,,
jax/_src/clusters/__pycache__/slurm_cluster.cpython-312.pyc,,
jax/_src/clusters/cloud_tpu_cluster.py,sha256=SuB7FuOWChVC2dpLeKmNOYYpd49Ke1c0UgLoMJe4c3E,8522
jax/_src/clusters/cluster.py,sha256=4_heeOPzwH7NHumJKZspEfCReABCJC9SDfW8z3ORZp0,5856
jax/_src/clusters/k8s_cluster.py,sha256=hliNJl5-YaQQSnIRhAKdCBIuMddv75nI91rY4iMpicI,9278
jax/_src/clusters/mpi4py_cluster.py,sha256=ugDhirp00Hh3knxX3qVb5yxSz1d0_O1DuQUcVfyO8rg,2608
jax/_src/clusters/ompi_cluster.py,sha256=Vfk3AimGa2jtDEwjpJJXuYP9p5uv1tBwEKtpGn1f4IQ,2317
jax/_src/clusters/slurm_cluster.py,sha256=hsZQJXznkCXGDTfKgMDgjVf4bmbAtSne6pa0wW4iJT0,2321
jax/_src/compilation_cache.py,sha256=IliI3Yvc67Mk8eXEFnapUOpOKdAjqdkPMF31usa5_L4,12187
jax/_src/compilation_cache_interface.py,sha256=ONHvsIC3Kzoda0TcQWnhhjxtOWYixjkO9Or__BAW3_0,865
jax/_src/compiler.py,sha256=Q2ej1sjd4TbVhggR_Qf9CJdPguXmXOMGQq1KKBap4f0,31505
jax/_src/compute_on.py,sha256=0b74TSV1R31kJTR53thafdtB0sYiFGqmRoAbZwAFcps,1911
jax/_src/config.py,sha256=Ymhw0OxYK_HgsEL39XZTJl_JDsKKjf9ksqgeEpgyYqg,69997
jax/_src/core.py,sha256=wuQyH6Q-osGAzqST5AiunmuBJ5HEDuS3JGHk1qDPT_k,125014
jax/_src/cudnn/__init__.py,sha256=03MR0G6UMqHXi-JBeyj6tipV6ioP9a-iSN1mW-8Ws5A,631
jax/_src/cudnn/__pycache__/__init__.cpython-312.pyc,,
jax/_src/cudnn/__pycache__/fused_attention_stablehlo.cpython-312.pyc,,
jax/_src/cudnn/__pycache__/fusion.cpython-312.pyc,,
jax/_src/cudnn/__pycache__/scaled_matmul_stablehlo.cpython-312.pyc,,
jax/_src/cudnn/fused_attention_stablehlo.py,sha256=XJNXoDVJ_9YyMKBsI253_HcwR_uXbpBosYvFbaFiEx0,72586
jax/_src/cudnn/fusion.py,sha256=KO7fo_thf5-sy1i_9HUrbTpt5cyOlS6zbu6k2RPonk8,2809
jax/_src/cudnn/scaled_matmul_stablehlo.py,sha256=AKIxjX6NvXFp4YPQhenBjUTDpt0L5IbYBYdqW3xICTs,23901
jax/_src/custom_api_util.py,sha256=uv6uyrXU1BHGzKuAe498W2PD9bcWt1PEa-8UAqr9Zaw,876
jax/_src/custom_batching.py,sha256=wK4iEi0Fk8SL_Le3oenJwzBUO6vTmfJRwqiX4z0gTA0,15177
jax/_src/custom_dce.py,sha256=XkVPIEBiiZ05Z4BIC8Ajbt6nmkE8qIAccDTLQ24sU2s,17478
jax/_src/custom_derivatives.py,sha256=5FFS1sw_6K-uYcfSvLTANl5E9So914i_BTvoAeewfs4,77945
jax/_src/custom_partitioning.py,sha256=a0nIRBhLSXOPVgnmWDwPLiDG6SH0ezCppsHMmSw5dcg,26120
jax/_src/custom_partitioning_sharding_rule.py,sha256=vzsRj4tSmoo81WnFHHCz9Euj9Dn2Ld_UUyLpPRk7DEE,18029
jax/_src/custom_transpose.py,sha256=_qJcMtVIJKxYq1iijy7X9xyuqd9ClgcnzWLC_eWW1iE,8850
jax/_src/debugger/__init__.py,sha256=iZU1X5nkpSBK5pwLSuWhZsPZgtLUovBF3tyWPmQoh1Q,899
jax/_src/debugger/__pycache__/__init__.cpython-312.pyc,,
jax/_src/debugger/__pycache__/cli_debugger.cpython-312.pyc,,
jax/_src/debugger/__pycache__/colab_debugger.cpython-312.pyc,,
jax/_src/debugger/__pycache__/colab_lib.cpython-312.pyc,,
jax/_src/debugger/__pycache__/core.cpython-312.pyc,,
jax/_src/debugger/__pycache__/web_debugger.cpython-312.pyc,,
jax/_src/debugger/cli_debugger.py,sha256=C9FQoMwTZOesOTG0X5dVDT_3nGTExqKZixdPPzTDkew,4780
jax/_src/debugger/colab_debugger.py,sha256=PHmiL-QES4t97iICJt34a5FXSNTm-AaSUERPGCZ3QHA,7823
jax/_src/debugger/colab_lib.py,sha256=jCbwj2Q4f8SEo7CBr6AeiUtzlsIv4BfKa5CVbq4Bn2U,4298
jax/_src/debugger/core.py,sha256=WilcRMqpzssKa5YL_P7SFtKaUVUaXqDutxO_N-ZRnp0,8593
jax/_src/debugger/web_debugger.py,sha256=Or-gJL40oBUfClu8mg1YUBoKA9cQytZgJAeGGrPPK_w,3433
jax/_src/debugging.py,sha256=zR1K9c1xwrjHn7ENZ-XsPl1p5vou8oSRqCW3ZlYL_cA,28686
jax/_src/deprecations.py,sha256=vBQeRe1h_4Okm3lJV5Xpu5yFSb9Z-nLX8Ieh5c7Yypg,4795
jax/_src/dispatch.py,sha256=3cjgfxg2RfDHg7eqRBDHDv7Uhbt_SqPphLTeTWI9N9E,26132
jax/_src/distributed.py,sha256=8rAhhjxfe-1G4ZW0oKEqkt2RZYS2o1RaqUZeV4Q9c0E,13693
jax/_src/dlpack.py,sha256=JlwF2sVmVNJ_RiitDCslaDUF7GGqw_6pnUwr8duuKig,11411
jax/_src/dtypes.py,sha256=wbWt7ITfUh_Em2HISTlI_yYrM0bXmN_KbuP-vf1t7V0,35700
jax/_src/earray.py,sha256=6KsYjNLzWaT1S0IKDwmZMAsa94E8yg9TU2Yb3-2QN70,4412
jax/_src/effects.py,sha256=5JdfjPGYSqChGNKcTGRDPxz1Cxq3rLogLLvD9_dbNSk,4676
jax/_src/environment_info.py,sha256=IoRky2nPXfdS_VPKvpPhzW0mTShyfUJiAl6SBleErh4,1986
jax/_src/error_check.py,sha256=X-FFObANtgQB_hZqKrjVCDvJ1hjZZDgSuT9fThPXtwQ,12727
jax/_src/errors.py,sha256=XlpnaaOLJUqWv5bOamlIL0PVqeQfRD1YNUYZcT1zYls,24710
jax/_src/export/__init__.py,sha256=mS3YSYUZFIG68T5Zo9CFGkT7wCyWRycnWV4L36CnHYU,581
jax/_src/export/__pycache__/__init__.cpython-312.pyc,,
jax/_src/export/__pycache__/_export.cpython-312.pyc,,
jax/_src/export/__pycache__/serialization.cpython-312.pyc,,
jax/_src/export/__pycache__/serialization_generated.cpython-312.pyc,,
jax/_src/export/__pycache__/shape_poly.cpython-312.pyc,,
jax/_src/export/__pycache__/shape_poly_decision.cpython-312.pyc,,
jax/_src/export/_export.py,sha256=oaR8zV_qMF4lmzz1kYdlYOTOjWdKQSmxer82e7bpFx4,70089
jax/_src/export/serialization.py,sha256=QFaaGFsS9Z7YwmgVDZ1KcI_FGDmO8po_JIg2lxtWYi8,19573
jax/_src/export/serialization_generated.py,sha256=pCU6x5XxV-2IUyRkA036MrhMY1RvbKWdefXwOySy7Cw,27869
jax/_src/export/shape_poly.py,sha256=yyTSk91uR8y8V7AgopklSpuXwvD3PG7J4sAqHcgCRR8,85898
jax/_src/export/shape_poly_decision.py,sha256=x24hHa1CGTskS83TMeo7xGILKsrgufYsGReiKLQAJtc,20476
jax/_src/extend/__init__.py,sha256=t5wRc_z8eIDwV0votvqn8WtOUEd53ZNf_J4VRYLJepc,581
jax/_src/extend/__pycache__/__init__.cpython-312.pyc,,
jax/_src/extend/__pycache__/random.cpython-312.pyc,,
jax/_src/extend/random.py,sha256=1KmQCCYxFMJO8KB0qJz9ZVQhnK-05Sv4Jn0jXLPpWQw,1258
jax/_src/ffi.py,sha256=rhL9B4OatDmTCSjqNOT4XLkNakncFqZ518ur-haRj_E,29021
jax/_src/flatten_util.py,sha256=WSVHRWlDiJm4gdnEIK3csN9aOxLuvWdLe8HXJfuQPMA,3550
jax/_src/hardware_utils.py,sha256=0-8OyFvMYu5l-C0e3VObpQMkLGg-BBtzP56CAdFEIxE,2295
jax/_src/image/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/image/__pycache__/__init__.cpython-312.pyc,,
jax/_src/image/__pycache__/scale.cpython-312.pyc,,
jax/_src/image/scale.py,sha256=XL1VSkCM4gtOOFurIElk8nXPfUB802nw3P8wlqquP4Q,13774
jax/_src/interpreters/__init__.py,sha256=hnuhN7h1jY5__Kt2rBvTrk0o8nX4fQl244fDCIMh0qk,690
jax/_src/interpreters/__pycache__/__init__.cpython-312.pyc,,
jax/_src/interpreters/__pycache__/ad.cpython-312.pyc,,
jax/_src/interpreters/__pycache__/batching.cpython-312.pyc,,
jax/_src/interpreters/__pycache__/mlir.cpython-312.pyc,,
jax/_src/interpreters/__pycache__/partial_eval.cpython-312.pyc,,
jax/_src/interpreters/__pycache__/pxla.cpython-312.pyc,,
jax/_src/interpreters/__pycache__/xla.cpython-312.pyc,,
jax/_src/interpreters/ad.py,sha256=-EBAdZ1HLz8wTnitLcUu03Aaxw0f9zQKHQF9vIbv7OA,57685
jax/_src/interpreters/batching.py,sha256=oW5M2ZXS0egtn_TErlmA-b2vyoxT1Sr9qJu9m-7l3xY,50849
jax/_src/interpreters/mlir.py,sha256=OL7CqgEcrNxs9hBLqHPvLP2jEKk7bNbgmv0gxHYJX1g,128921
jax/_src/interpreters/partial_eval.py,sha256=TmkKk5iHnHqOpO_fWp2mAb5kbw8oj6ufhBMCOF8A71o,119028
jax/_src/interpreters/pxla.py,sha256=2hLYLVUusfvxcZcR4-2qedx566lbmsjU-xnfy5-EO3o,137136
jax/_src/interpreters/xla.py,sha256=Me2RiFbqW_hF-RUOBNxQEGWkPhqYfYjH-12eAQbJHb0,4981
jax/_src/jaxpr_util.py,sha256=XYL-dYXHas26onHF8tKfUN3C7jGNg2TDpzWcFsOEiks,8127
jax/_src/lax/__init__.py,sha256=jRL3qQoUco8BVUAh8lV-KuY38h_4GxXcb41asljW2yI,690
jax/_src/lax/__pycache__/__init__.cpython-312.pyc,,
jax/_src/lax/__pycache__/ann.cpython-312.pyc,,
jax/_src/lax/__pycache__/convolution.cpython-312.pyc,,
jax/_src/lax/__pycache__/eigh.cpython-312.pyc,,
jax/_src/lax/__pycache__/fft.cpython-312.pyc,,
jax/_src/lax/__pycache__/lax.cpython-312.pyc,,
jax/_src/lax/__pycache__/linalg.cpython-312.pyc,,
jax/_src/lax/__pycache__/other.cpython-312.pyc,,
jax/_src/lax/__pycache__/parallel.cpython-312.pyc,,
jax/_src/lax/__pycache__/qdwh.cpython-312.pyc,,
jax/_src/lax/__pycache__/slicing.cpython-312.pyc,,
jax/_src/lax/__pycache__/special.cpython-312.pyc,,
jax/_src/lax/__pycache__/stack.cpython-312.pyc,,
jax/_src/lax/__pycache__/svd.cpython-312.pyc,,
jax/_src/lax/__pycache__/utils.cpython-312.pyc,,
jax/_src/lax/__pycache__/windowed_reductions.cpython-312.pyc,,
jax/_src/lax/ann.py,sha256=W1Bq5Asf1czGwMTMUtF5-14cPoTk31Rx4wf0eNshDes,17177
jax/_src/lax/control_flow/__init__.py,sha256=WRnqzy4ec591tqipY4GcyXFzniF0njoIVkP0HhKGbiI,2244
jax/_src/lax/control_flow/__pycache__/__init__.cpython-312.pyc,,
jax/_src/lax/control_flow/__pycache__/common.cpython-312.pyc,,
jax/_src/lax/control_flow/__pycache__/conditionals.cpython-312.pyc,,
jax/_src/lax/control_flow/__pycache__/for_loop.cpython-312.pyc,,
jax/_src/lax/control_flow/__pycache__/loops.cpython-312.pyc,,
jax/_src/lax/control_flow/__pycache__/solves.cpython-312.pyc,,
jax/_src/lax/control_flow/common.py,sha256=75qJrTdI-QLmKGcjkC1T8cECWKgHYQDtzX1UOHXMRe0,11565
jax/_src/lax/control_flow/conditionals.py,sha256=R6wPIQitoSlN23FtEezXp7hW_2FIWGdJ8px5F6Ilx48,50769
jax/_src/lax/control_flow/for_loop.py,sha256=9L4Zv3LTKKCW3mlce9cFZRrK6zYb5UIiBWNdgyUy9-A,36370
jax/_src/lax/control_flow/loops.py,sha256=yxcmpV_DN5GLdSI6WHVpnsDhJ6kU79sW_wbyHkXFjpI,127360
jax/_src/lax/control_flow/solves.py,sha256=9C-rBd_2PaVdefP7FHeGRmIEZ8e7hlf5JJg-rlaWpv0,20140
jax/_src/lax/convolution.py,sha256=rL-mM5hd7g7ounRZLItM2zZqE4BlOOJDSMhSpMmqooc,45958
jax/_src/lax/eigh.py,sha256=MAWftHqilChJ0yd2-aJxSpiu6b9RYnY_y_SUI5OfrnU,22418
jax/_src/lax/fft.py,sha256=j3UOtxwQfrhR5qO8krthS4pxG0vmhBC9yb0gyLFL_QE,7269
jax/_src/lax/lax.py,sha256=KJUOOzF4LgBpaH56BCgyS1kMl6LtvdUCJpAZG4nL3-8,359682
jax/_src/lax/linalg.py,sha256=O-RmPhBMJsvE10-9JNjMHvV8GnsmKKzdKAvOQkN8n0A,106482
jax/_src/lax/other.py,sha256=k5QaTGae3ps89MM2UzOG1_foHg_W3C5Ysq3BMKJWdyc,12497
jax/_src/lax/parallel.py,sha256=xGevpoM6O6CuZ5XKxNCv260wvZTczISN1No3zYnevCc,87493
jax/_src/lax/qdwh.py,sha256=dmslOVnzg-DhxjSIHV9jHCOKopVosYD-xe1FPmSgnCo,9391
jax/_src/lax/slicing.py,sha256=WtFn0N0wnGDmNZ1-p7p1NbWP_rcnjTxYgRApCHpZEjs,134758
jax/_src/lax/special.py,sha256=yIjzWwwZ6IetGqkpQbEvyLtbcKj09HBI0VUS19yi7fc,28856
jax/_src/lax/stack.py,sha256=7hxK1zlVa_Q9gO48Ymj8S1nA8DN6keYgcOY0DAtbjOY,2560
jax/_src/lax/svd.py,sha256=3ol5rMgm_uy40yZzKs-9BoDwFn4oUqbcDWbKcvvVok0,8826
jax/_src/lax/utils.py,sha256=6g-yPv2f8m04wOWxqYrct2zT-S3wwLNUD0W4XfZJgYg,8184
jax/_src/lax/windowed_reductions.py,sha256=aFTwUoLpcKVdeGCjfguECsFoSfIQB7n5wcQq2aOlmIA,45232
jax/_src/lax_reference.py,sha256=YPZTCI52PssuXAEnMn4r44ZZ2jDHB0wYj8d_fJDrCnw,19282
jax/_src/layout.py,sha256=gv8fiQZs_Qq-UemSinCiz4M5PCxOkYFiDjD0zsdOsAo,5406
jax/_src/lazy_loader.py,sha256=G6czW2Uq6WRC1EjDiX6rslMu02cZUGRkKNrvIhXksxw,1842
jax/_src/lib/__init__.py,sha256=EauTS-Vmc5VKfjZXD6JAr1LxFr1LDQ2CLCbIRZ4QNzU,7803
jax/_src/lib/__pycache__/__init__.cpython-312.pyc,,
jax/_src/lib/__pycache__/mosaic_gpu.cpython-312.pyc,,
jax/_src/lib/__pycache__/triton.cpython-312.pyc,,
jax/_src/lib/mlir/__init__.py,sha256=UoirBDcNhqLa8Gb1Qrxd4wYxScz_p-1tb-ewbTsK1jM,733
jax/_src/lib/mlir/__pycache__/__init__.cpython-312.pyc,,
jax/_src/lib/mlir/dialects/__init__.py,sha256=a9echsyt7bw1MRyFgHLFDt3YJUaRH5jsbfA7V_40Ie8,2020
jax/_src/lib/mlir/dialects/__pycache__/__init__.cpython-312.pyc,,
jax/_src/lib/mosaic_gpu.py,sha256=lHnM852MFg7K5UUwry1zN_r5nrPSWl6ZVwOxVnsWAz4,898
jax/_src/lib/triton.py,sha256=lMwAguUTQxYs9HkdLqeracg8sz5RL7gm1W6PpzSrzdk,2202
jax/_src/linear_util.py,sha256=B-gJVzOGn0HELEKyY_hGZcLf5Fftsu9F7AKPqyHfqxw,19035
jax/_src/logging_config.py,sha256=zKijn3gjyw21ljvWjTsBX1bE6AyIa3zHRQ15YvaDDmk,4452
jax/_src/lru_cache.py,sha256=NejEgo8BmO7xy8uORQrAHsN0HpCnY8qsEUnuEhDCkPs,6741
jax/_src/mesh.py,sha256=p673588iDm6ppVjTLowb3pIBqq5ZO1JTf4h6Uvd513U,18553
jax/_src/mesh_utils.py,sha256=JJWHor6Gjp4PL3FHeAybDrQmVLuKolK7Sja2tjMWT8M,32401
jax/_src/monitoring.py,sha256=PSOb1js-21Hb4INsIdiJpm0s483xeljnoRr0Lha4nzM,6241
jax/_src/named_sharding.py,sha256=B8nRIhW2fLr4m2GPiTvu5e6G5yt8Pt2ep9KLnrERmWs,20708
jax/_src/nn/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/nn/__pycache__/__init__.cpython-312.pyc,,
jax/_src/nn/__pycache__/functions.cpython-312.pyc,,
jax/_src/nn/__pycache__/initializers.cpython-312.pyc,,
jax/_src/nn/functions.py,sha256=HA3ECKzZ5TbJCr0wk33CVjn6B6IGc8poC1WB1HhmbC8,45776
jax/_src/nn/initializers.py,sha256=ja9Xln7x_CosDe2lfXS9eTBYGjDaACx05pFqJ4gWEoo,25383
jax/_src/numpy/__init__.py,sha256=-QhmzWj2qFbKVIT9TDAIvQWaKQibiRXy3r1zQkI3X4M,581
jax/_src/numpy/__pycache__/__init__.cpython-312.pyc,,
jax/_src/numpy/__pycache__/array_api_metadata.cpython-312.pyc,,
jax/_src/numpy/__pycache__/array_creation.cpython-312.pyc,,
jax/_src/numpy/__pycache__/array_methods.cpython-312.pyc,,
jax/_src/numpy/__pycache__/einsum.cpython-312.pyc,,
jax/_src/numpy/__pycache__/error.cpython-312.pyc,,
jax/_src/numpy/__pycache__/fft.cpython-312.pyc,,
jax/_src/numpy/__pycache__/index_tricks.cpython-312.pyc,,
jax/_src/numpy/__pycache__/indexing.cpython-312.pyc,,
jax/_src/numpy/__pycache__/lax_numpy.cpython-312.pyc,,
jax/_src/numpy/__pycache__/linalg.cpython-312.pyc,,
jax/_src/numpy/__pycache__/polynomial.cpython-312.pyc,,
jax/_src/numpy/__pycache__/reductions.cpython-312.pyc,,
jax/_src/numpy/__pycache__/scalar_types.cpython-312.pyc,,
jax/_src/numpy/__pycache__/setops.cpython-312.pyc,,
jax/_src/numpy/__pycache__/sorting.cpython-312.pyc,,
jax/_src/numpy/__pycache__/tensor_contractions.cpython-312.pyc,,
jax/_src/numpy/__pycache__/ufunc_api.cpython-312.pyc,,
jax/_src/numpy/__pycache__/ufuncs.cpython-312.pyc,,
jax/_src/numpy/__pycache__/util.cpython-312.pyc,,
jax/_src/numpy/__pycache__/vectorize.cpython-312.pyc,,
jax/_src/numpy/__pycache__/window_functions.cpython-312.pyc,,
jax/_src/numpy/array_api_metadata.py,sha256=YIvEv5NDLKb3I4xURDTf9A_xwbiIeYKrCeW53o1Ec7A,3616
jax/_src/numpy/array_creation.py,sha256=wIMdQOTWZFOwsYu92Yuhje08qFIcqVR23Y_YeU_9jD4,28448
jax/_src/numpy/array_methods.py,sha256=j-Vws0vcJ3unfB7J7XGl8ow2H_elphBEpEa6fTX3Pn0,47553
jax/_src/numpy/einsum.py,sha256=ksfv6qK803IS4MSJpMcBfMQfWc8XltFRIt9RAOYTftQ,23595
jax/_src/numpy/error.py,sha256=CKxSg5NZL5s3VCmr0i_UVnMXpa4HdaNy-DESzoMyEow,6639
jax/_src/numpy/fft.py,sha256=RkqyGXxNSoc_P1IxI6jgrH0mziJc7O3zAqtc3pqu-xE,52082
jax/_src/numpy/index_tricks.py,sha256=aRFc9So43wmgzk-eVGBeN16BI4Ny7eXYFr7yEtrgDqM,9992
jax/_src/numpy/indexing.py,sha256=c2pcOObqP9p5ZG-S6ScoHpoo55JXMNv0j_FdDnaK3zg,51485
jax/_src/numpy/lax_numpy.py,sha256=7L01CsBRavfk7wd3RLyqIWPsEv5BxITbNcVqWj3m_xE,348289
jax/_src/numpy/linalg.py,sha256=wPy_uowgilzDbSwiaC3Ni-CqTbLFuHEL8tF8N0hc47Q,79099
jax/_src/numpy/polynomial.py,sha256=QUq8G4N_xvTSv5C0_munafFPm6HQL0tkSaBgCFnU2O0,28420
jax/_src/numpy/reductions.py,sha256=KF3Naef4z3weXD3orGMteru2CC1YnD6F-19HuruUdBc,109272
jax/_src/numpy/scalar_types.py,sha256=KNsCK6TAqS7jDG9sulZADAw-zxaM1fUsQFHioVSG65g,3802
jax/_src/numpy/setops.py,sha256=QUbc1j-EMKEcK79Ron__r7zA0P87hoSr1SmMrcWB1m4,47867
jax/_src/numpy/sorting.py,sha256=DvYC8Te38zxcBImc0egun35xGcD2zNpj2KP1c2SvY54,16296
jax/_src/numpy/tensor_contractions.py,sha256=SxSh_wrnCaMSFDSQqYV6U6qfSo79yciIJi75s4V-GDk,24318
jax/_src/numpy/ufunc_api.py,sha256=kRvpLgvTjIwNrV7DBtDpDJ1EkmFu1twcDT8TtOfc5h8,24284
jax/_src/numpy/ufuncs.py,sha256=_zoscMFEHN_IoQlFceopVrCgAUNUCbZ1TIdY97fNsRg,118717
jax/_src/numpy/util.py,sha256=DazxM1zHR_fRTvdH3S0kUt-xKho8P8KNXzEATGG5HKM,17394
jax/_src/numpy/vectorize.py,sha256=EgOSGTnPvJD-V1YYYu4fJS1u44oxQUMop9Ma87X8wWk,14068
jax/_src/numpy/window_functions.py,sha256=_xI_pbsvC8Ai_8hVdchPx8gQNWYgKGPED8TR2GRsqXY,5524
jax/_src/op_shardings.py,sha256=nRnGgfaKz7mIB7sHxh_QkiCa1oPMA9VsDpPyQkivtlU,3857
jax/_src/ops/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/ops/__pycache__/__init__.cpython-312.pyc,,
jax/_src/ops/__pycache__/scatter.cpython-312.pyc,,
jax/_src/ops/__pycache__/special.cpython-312.pyc,,
jax/_src/ops/scatter.py,sha256=8QUaAIS9mgB28RfAL-L_qocl6WW8I6pit1rPQJqe2ac,18181
jax/_src/ops/special.py,sha256=p1hRiOonzxVdhfcDGjVxLkL2nU0iU3EtgtmnchLIFgo,4212
jax/_src/pallas/__init__.py,sha256=t5wRc_z8eIDwV0votvqn8WtOUEd53ZNf_J4VRYLJepc,581
jax/_src/pallas/__pycache__/__init__.cpython-312.pyc,,
jax/_src/pallas/__pycache__/core.cpython-312.pyc,,
jax/_src/pallas/__pycache__/cost_estimate.cpython-312.pyc,,
jax/_src/pallas/__pycache__/helpers.cpython-312.pyc,,
jax/_src/pallas/__pycache__/hlo_interpreter.cpython-312.pyc,,
jax/_src/pallas/__pycache__/pallas_call.cpython-312.pyc,,
jax/_src/pallas/__pycache__/primitives.cpython-312.pyc,,
jax/_src/pallas/__pycache__/utils.cpython-312.pyc,,
jax/_src/pallas/core.py,sha256=oWFjZJn0xFKa63pWWOcYYGDAZONSR1qQsfoG_2GNcR0,46641
jax/_src/pallas/cost_estimate.py,sha256=inTDMJ3N7qBsRFf7MIBnkgK72wN5qyaTDp5Qg5jt7WE,8316
jax/_src/pallas/fuser/__init__.py,sha256=cHEWioEa-43e-714tP3fFN9hdyZg_ebBFbFJur7bXwg,1182
jax/_src/pallas/fuser/__pycache__/__init__.cpython-312.pyc,,
jax/_src/pallas/fuser/__pycache__/block_spec.cpython-312.pyc,,
jax/_src/pallas/fuser/__pycache__/custom_evaluate.cpython-312.pyc,,
jax/_src/pallas/fuser/__pycache__/fuser_utils.cpython-312.pyc,,
jax/_src/pallas/fuser/__pycache__/fusible.cpython-312.pyc,,
jax/_src/pallas/fuser/__pycache__/fusible_dtype.cpython-312.pyc,,
jax/_src/pallas/fuser/__pycache__/fusion.cpython-312.pyc,,
jax/_src/pallas/fuser/__pycache__/jaxpr_fusion.cpython-312.pyc,,
jax/_src/pallas/fuser/block_spec.py,sha256=YyEFEBQMEVbOv0uWRaGy3QLnjqDUZahwJretF3yROmw,61198
jax/_src/pallas/fuser/custom_evaluate.py,sha256=jv5JjL5BlUUK89ZKx9sufff0bVyqJA3s5ag6Bpus5ys,2823
jax/_src/pallas/fuser/fuser_utils.py,sha256=UY_9Ly9Z863mHX3R_EdLw4H1jLsTCeaeTDMV1Dg9l-s,1273
jax/_src/pallas/fuser/fusible.py,sha256=afsksW8n7JyKqC3Ggolu4GLWYU1ontw-czS0xkwPmjU,2692
jax/_src/pallas/fuser/fusible_dtype.py,sha256=tNF9lWP1QY89-aBT4FJw7SksQ17p-nuG9VXTBAjtgNI,14358
jax/_src/pallas/fuser/fusion.py,sha256=W1NBIzSqOkBk8ADty9HfjnmePZXKBsPxFUiM_k3qtC8,1488
jax/_src/pallas/fuser/jaxpr_fusion.py,sha256=bNt0JrU5Jsq9AFYoP1qhe9b_4uQ0KLQfXIfSaC5KxMs,10806
jax/_src/pallas/helpers.py,sha256=yl7nNvCgpCe_kdHUxHB8j93OremndvK7wS6Oz6xUEA8,1786
jax/_src/pallas/hlo_interpreter.py,sha256=W2ZajXb4PAAntth9umf4wFSCLtYvjx8S5YGJTiWF0C4,19082
jax/_src/pallas/mosaic/__init__.py,sha256=t5wRc_z8eIDwV0votvqn8WtOUEd53ZNf_J4VRYLJepc,581
jax/_src/pallas/mosaic/__pycache__/__init__.cpython-312.pyc,,
jax/_src/pallas/mosaic/__pycache__/core.cpython-312.pyc,,
jax/_src/pallas/mosaic/__pycache__/error_handling.cpython-312.pyc,,
jax/_src/pallas/mosaic/__pycache__/helpers.cpython-312.pyc,,
jax/_src/pallas/mosaic/__pycache__/interpret.cpython-312.pyc,,
jax/_src/pallas/mosaic/__pycache__/lowering.cpython-312.pyc,,
jax/_src/pallas/mosaic/__pycache__/pallas_call_registration.cpython-312.pyc,,
jax/_src/pallas/mosaic/__pycache__/pipeline.cpython-312.pyc,,
jax/_src/pallas/mosaic/__pycache__/primitives.cpython-312.pyc,,
jax/_src/pallas/mosaic/__pycache__/random.cpython-312.pyc,,
jax/_src/pallas/mosaic/__pycache__/verification.cpython-312.pyc,,
jax/_src/pallas/mosaic/core.py,sha256=_THi0QHImVRNZBc55IMlkPjXP7h-G2rUtSD4kSTV6q0,8071
jax/_src/pallas/mosaic/error_handling.py,sha256=FCbB-b5Dl75erMmr-FMvc4QJwCXqn2z7ioNC2Eh0vQc,5542
jax/_src/pallas/mosaic/helpers.py,sha256=DFBL7RuFtfBQc-aUaC4bILa5Xzsbb91Wu3VCn7b3v4M,2767
jax/_src/pallas/mosaic/interpret.py,sha256=9DhkR9k9o6rbDUPLYkfPbH9blqVGJrBaoxZ2hXtcqyE,71774
jax/_src/pallas/mosaic/lowering.py,sha256=3wA4kPH7hgHlu2Zm6R7Is24rlJYwsvjdHjog9VQy5vE,136330
jax/_src/pallas/mosaic/pallas_call_registration.py,sha256=O6cZNBUOlw3TRvkdgLkFqWl4agPzUFPC3M7upb_1FIM,9369
jax/_src/pallas/mosaic/pipeline.py,sha256=C4LbCSj6rauUxalx-2lBK-D9CHsn0W2-MozH-UqJlzw,51976
jax/_src/pallas/mosaic/primitives.py,sha256=-WEzbXx6KcS8vlQ6jFyCQHYCRnnWdi71YLqVHkXYits,25416
jax/_src/pallas/mosaic/random.py,sha256=BTxNgeHRgKv8GkLMhqBNchFHkIB-rUprAcLM5p3m9S0,7844
jax/_src/pallas/mosaic/verification.py,sha256=1QWYOwAAOvuCgB8gXWCJUlP1mPLvegzeUoFgJ_xC4X4,24485
jax/_src/pallas/mosaic_gpu/__init__.py,sha256=mS3YSYUZFIG68T5Zo9CFGkT7wCyWRycnWV4L36CnHYU,581
jax/_src/pallas/mosaic_gpu/__pycache__/__init__.cpython-312.pyc,,
jax/_src/pallas/mosaic_gpu/__pycache__/core.cpython-312.pyc,,
jax/_src/pallas/mosaic_gpu/__pycache__/helpers.cpython-312.pyc,,
jax/_src/pallas/mosaic_gpu/__pycache__/lowering.cpython-312.pyc,,
jax/_src/pallas/mosaic_gpu/__pycache__/pallas_call_registration.cpython-312.pyc,,
jax/_src/pallas/mosaic_gpu/__pycache__/pipeline.cpython-312.pyc,,
jax/_src/pallas/mosaic_gpu/__pycache__/primitives.cpython-312.pyc,,
jax/_src/pallas/mosaic_gpu/core.py,sha256=oBx8vHCTlRbrZoZaXUR0C47fzMCNFQVh8AD5TPNB4cU,35998
jax/_src/pallas/mosaic_gpu/helpers.py,sha256=CWFbFe75GpTPDYsn7IUyMBBq6WWHjIPxuEDVT81vSSk,2802
jax/_src/pallas/mosaic_gpu/lowering.py,sha256=mK0x84PXPbV01HlkCQg7KQGWrh8KxrzbuPOT77qHhT4,112092
jax/_src/pallas/mosaic_gpu/pallas_call_registration.py,sha256=NoMWkJy8jmecc_bUoF-PgjuqkJLY3SjI6wK573SfsIM,5165
jax/_src/pallas/mosaic_gpu/pipeline.py,sha256=HgeLhg9HczACP_qBOp5l5KzdLS4eU4t1TMl0AF7HrkE,28164
jax/_src/pallas/mosaic_gpu/primitives.py,sha256=ZwkVA-f1RwQa1sQWS9zsQYFwSqSXEEpxzhI7vJJKaqo,65048
jax/_src/pallas/pallas_call.py,sha256=cqr3goUkA4fVRK4aQgJB4xAeVk8zhYawR6MKhOulAwM,68452
jax/_src/pallas/primitives.py,sha256=fiaG95pFM3WAYgUsKinoPCR7Dj61QPs5NYeTNAvFoDg,42993
jax/_src/pallas/triton/__init__.py,sha256=t5wRc_z8eIDwV0votvqn8WtOUEd53ZNf_J4VRYLJepc,581
jax/_src/pallas/triton/__pycache__/__init__.cpython-312.pyc,,
jax/_src/pallas/triton/__pycache__/core.cpython-312.pyc,,
jax/_src/pallas/triton/__pycache__/lowering.cpython-312.pyc,,
jax/_src/pallas/triton/__pycache__/pallas_call_registration.cpython-312.pyc,,
jax/_src/pallas/triton/__pycache__/primitives.cpython-312.pyc,,
jax/_src/pallas/triton/core.py,sha256=_SQpKmmpgPnqUll_A4QgcB16SqcZbyy4h9pYdmw35hs,1389
jax/_src/pallas/triton/lowering.py,sha256=H47cy_Sf5DpqkNeFmA2F-zHb94aiD8TJeYhjTRCMCZM,99131
jax/_src/pallas/triton/pallas_call_registration.py,sha256=fxM8lkAWR3pPwBuDA5_1n0mhYaAq12b3ixlwJLUqsIs,6711
jax/_src/pallas/triton/primitives.py,sha256=_UjvgpOsEaRzul3xizwzPdP1blZ1-NzChzZ4bsVmSaA,4228
jax/_src/pallas/utils.py,sha256=7Tor_59JAGvLDRbFMMLCqIcDQ-8y8UOrSjVlwND5kKw,13887
jax/_src/partition_spec.py,sha256=LbRqpUurneNtAOCE-fQ1BHZHy7XZN_GpXCQpqwsz9oI,5753
jax/_src/path.py,sha256=VkrwcBMWagZZQBBQYhdSFr7pQzoxwBnNUEfKfEbN-nU,1596
jax/_src/pickle_util.py,sha256=gE8IrNWtg4hfu84T5rQ2DVAdpozh5xwSzAlRi7UIWcQ,2312
jax/_src/pjit.py,sha256=et_iSyfrz0Bj2-XcS00j9OLdbXhMiXlutOlD7GPqvuE,148458
jax/_src/pretty_printer.py,sha256=EO3Unk2tnWzi6eMEsdlKuMUI6KdZJCcrrOE7HDT6hdM,15125
jax/_src/prng.py,sha256=_ouMZwQveJ5ZHw9iPm10m5bTpYCRqkwCObSQA0VLh-8,45778
jax/_src/profiler.py,sha256=llJ3Y0YNC5vtfArkAqKBweAd2F5iL3X0ZZ5FGQiCiOI,17265
jax/_src/public_test_util.py,sha256=wXB1H7lgFuDvZVRyDOs-yPMm9C18VKVPKUfKlIdF7gI,12676
jax/_src/random.py,sha256=XMkzy4IqI97_sz4p8Aid4mYrbvfoXiMfHPcs6Pqqcog,106765
jax/_src/scipy/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/scipy/__pycache__/__init__.cpython-312.pyc,,
jax/_src/scipy/__pycache__/fft.cpython-312.pyc,,
jax/_src/scipy/__pycache__/integrate.cpython-312.pyc,,
jax/_src/scipy/__pycache__/linalg.cpython-312.pyc,,
jax/_src/scipy/__pycache__/ndimage.cpython-312.pyc,,
jax/_src/scipy/__pycache__/signal.cpython-312.pyc,,
jax/_src/scipy/__pycache__/special.cpython-312.pyc,,
jax/_src/scipy/cluster/__init__.py,sha256=S-o0CP72882Xv3l6k2PlrEx6eNvXBTk5Oaed5KF4iL8,581
jax/_src/scipy/cluster/__pycache__/__init__.cpython-312.pyc,,
jax/_src/scipy/cluster/__pycache__/vq.cpython-312.pyc,,
jax/_src/scipy/cluster/vq.py,sha256=3Xfr7esTCzSawrDGZ37hu_MXJ-EqnZtff6hYmXhz0Fc,3043
jax/_src/scipy/fft.py,sha256=NFy_bDIzx44Lpkw1-74fBm2jxeXmzIUp8LTVh3-6Z5s,15281
jax/_src/scipy/integrate.py,sha256=4VtR2wYVO037Z0yiOdQbniKtpxL4boZQNIppy4J9qy0,2346
jax/_src/scipy/interpolate/__init__.py,sha256=S-o0CP72882Xv3l6k2PlrEx6eNvXBTk5Oaed5KF4iL8,581
jax/_src/scipy/interpolate/__pycache__/__init__.cpython-312.pyc,,
jax/_src/scipy/linalg.py,sha256=Qh9xlsOj3DYr_3qUa7ZNtiYGUS_gxeWSd5skuARc724,79364
jax/_src/scipy/ndimage.py,sha256=IuE4PMzY5HuxST9D0G0On1SVFRpNF367Y2cIQv8duTc,7092
jax/_src/scipy/optimize/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/scipy/optimize/__pycache__/__init__.cpython-312.pyc,,
jax/_src/scipy/optimize/__pycache__/_lbfgs.cpython-312.pyc,,
jax/_src/scipy/optimize/__pycache__/bfgs.cpython-312.pyc,,
jax/_src/scipy/optimize/__pycache__/line_search.cpython-312.pyc,,
jax/_src/scipy/optimize/__pycache__/minimize.cpython-312.pyc,,
jax/_src/scipy/optimize/_lbfgs.py,sha256=kqKkdDeRYiH5prawdntOXfCs-g311NYwnY57jV8DDbw,7769
jax/_src/scipy/optimize/bfgs.py,sha256=JRdDBxnfNrVxK5UDyehFGF3eQTJenWfdZVmKOMheenE,5609
jax/_src/scipy/optimize/line_search.py,sha256=E51ROBUJYmanL1byuPpIa28HmN1vCf_BAaBC_dQZgoc,13235
jax/_src/scipy/optimize/minimize.py,sha256=mCojZkL0TXQhix6w0-GroM7ITU2rodYdDwmvynLsqWs,4979
jax/_src/scipy/signal.py,sha256=oiGpJvutJsQodboOqXHJzEnkfEbVqTJXUjvhGC1ujH8,47397
jax/_src/scipy/sparse/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/scipy/sparse/__pycache__/__init__.cpython-312.pyc,,
jax/_src/scipy/sparse/__pycache__/linalg.cpython-312.pyc,,
jax/_src/scipy/sparse/linalg.py,sha256=IGX8lZ2il77S3WA50Lz7MdBiw7lqdD8mHgWRaCUHX6k,27245
jax/_src/scipy/spatial/__init__.py,sha256=t5wRc_z8eIDwV0votvqn8WtOUEd53ZNf_J4VRYLJepc,581
jax/_src/scipy/spatial/__pycache__/__init__.cpython-312.pyc,,
jax/_src/scipy/spatial/__pycache__/transform.cpython-312.pyc,,
jax/_src/scipy/spatial/transform.py,sha256=yD9HOfgQpQO3-eACTh5zW8qzV-WLcrTHY_5ZlsnBoSk,17525
jax/_src/scipy/special.py,sha256=NG7CoHK7_s4TqvK3nWRNpc_vxYzZd3mR9wLnYy9fXlU,89932
jax/_src/scipy/stats/__init__.py,sha256=9AYvmtjvwOmouHXvS-uU2Id1YBE7CUbYVE__dR1koWg,581
jax/_src/scipy/stats/__pycache__/__init__.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/_core.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/bernoulli.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/beta.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/betabinom.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/binom.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/cauchy.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/chi2.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/dirichlet.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/expon.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/gamma.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/gennorm.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/geom.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/kde.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/laplace.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/logistic.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/multinomial.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/multivariate_normal.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/nbinom.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/norm.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/pareto.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/poisson.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/t.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/truncnorm.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/uniform.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/vonmises.cpython-312.pyc,,
jax/_src/scipy/stats/__pycache__/wrapcauchy.cpython-312.pyc,,
jax/_src/scipy/stats/_core.py,sha256=Gq8H86uI_wWodhyvdEy5GiNulBfW3kJZvm8Z0Qw6Vdc,10653
jax/_src/scipy/stats/bernoulli.py,sha256=zOvo5ZLr8y3O0byR0cp3JRPKPPHoz8oVyG3-C_5-NJo,4541
jax/_src/scipy/stats/beta.py,sha256=h2LF8lIveFCa-qNZ_XLD1hQIK9_dLSmlCjrqeSdz_ME,7988
jax/_src/scipy/stats/betabinom.py,sha256=dmhOAUk6py5Fnu-jR95KnwJ6suvvfwNF9CXNHz73CSc,3447
jax/_src/scipy/stats/binom.py,sha256=E7a3go8bCM5bSiC72l1pDQEuuzSpXLmgfG40iPF90wA,2825
jax/_src/scipy/stats/cauchy.py,sha256=26eHaNIfxwh1og3kTqnPYgivH0GTIuWVwElai4VJwv8,8884
jax/_src/scipy/stats/chi2.py,sha256=vPFrhpbLRPDheYenmHQ7QvZrRt8cAyRtOpU9VmemNIY,8008
jax/_src/scipy/stats/dirichlet.py,sha256=_wWyx07z62_EH25i3J4jS5q53oj0UH5A3-XgQzapwbY,3198
jax/_src/scipy/stats/expon.py,sha256=W0tt3HtC5jB5mEM-bQbAC4U9FKVJFspsqnPpOAN6_FE,7813
jax/_src/scipy/stats/gamma.py,sha256=Nf0qgFD7K-2H4R581g1Kb0RQ_WHZrl81A1vgyS_PdxM,7038
jax/_src/scipy/stats/gennorm.py,sha256=kp22L5H-rR5n7sn9rpeWsWta-F76yZaXkO0Ovvf9zc8,2989
jax/_src/scipy/stats/geom.py,sha256=2hQ4LoaqNHFBahdRTGS9oyWMYdFQufJb2ByqOw9709s,2243
jax/_src/scipy/stats/kde.py,sha256=qY2ZLH8HVOU50DhQBDrhH2XTLT1eUHAFx7qGuvhgSmc,10139
jax/_src/scipy/stats/laplace.py,sha256=9PlK08r9wRHelqSIuKkv8KGO0oPV2AArI0Fs6rICFj0,3269
jax/_src/scipy/stats/logistic.py,sha256=HB3Brjwwl-yyGB_9K1lpdiQlmitsHwNLfgl0g4owFSM,6271
jax/_src/scipy/stats/multinomial.py,sha256=CVgpOPCVcr_wTNuF1vXcUjgsENe2mVO7gYBYTSdCOwI,2469
jax/_src/scipy/stats/multivariate_normal.py,sha256=Dei7nhj3j5gB1lBi9q_ZpdMHi1UY7XCr3GTzNuaWF1E,3433
jax/_src/scipy/stats/nbinom.py,sha256=kr6FLGYROqMMtfLfWLI1fumHBl_tSX1_Ys2icp59Hdw,2603
jax/_src/scipy/stats/norm.py,sha256=pUS-JhrjrFtdbfO178IeOJizZBU_WjXU6aUr8W67tQg,8401
jax/_src/scipy/stats/pareto.py,sha256=WcRAchdj198RF_W3egR8Bteok1dXE0PHv9ygw-pQYtM,2596
jax/_src/scipy/stats/poisson.py,sha256=Y43e2hY6L89go0Vc1HJCDrf61-vDZcQVIC6zVJdaYyQ,3348
jax/_src/scipy/stats/t.py,sha256=I32sQ9TVaes4keK5AIK6I-vX--nvl4qtZctuc0zIhG0,3221
jax/_src/scipy/stats/truncnorm.py,sha256=V4_Y2OEMYPepPyWVqMZKO0hRV8HwwnI6_djK9xYwdDA,9239
jax/_src/scipy/stats/uniform.py,sha256=pR3NYr6ZHTs6xhw-LECsRJqp5l_iBbsw689QHxr9Puc,4361
jax/_src/scipy/stats/vonmises.py,sha256=yxvmgBI_HWQn8U3u7wtLv9PjqOk7hRX8I4gWKfqRZKg,2451
jax/_src/scipy/stats/wrapcauchy.py,sha256=SpHIjzS9j3TqgAMTHXxtDz73aBfezqYUaNSsGYCky5I,2371
jax/_src/shard_alike.py,sha256=wG_kUvyH6aNRjgViwVffEAz1tbfhBF66kf3vw0XexIg,3989
jax/_src/shard_map.py,sha256=nZQd_TnmpNCGpZX-MbcGYiEMzj3uoCBEYNsOIQnv1b8,81676
jax/_src/sharding.py,sha256=pkrmy_r6huTjqDmNsQaFmWIFdxZukjp5NyeWhMbC-OE,7704
jax/_src/sharding_impls.py,sha256=zFqo6429xkvc0V15J9DVq2O5mF8vgEz7C9NigNH6gBo,53078
jax/_src/sharding_specs.py,sha256=UCYUIcQfS6lcA993W33zZI5tfAg5zcefP9cZUyW9NoI,8617
jax/_src/source_info_util.py,sha256=O8Ff0QonDyYKecTH8a2KdqBe7HuQHrIDTkb-OShHnpk,10852
jax/_src/sourcemap.py,sha256=5s4zTThhX6PZwgQ4v4UbYL4norg4uHipqgUGdHBn76Y,6640
jax/_src/stages.py,sha256=NYpyZ2MeKMEo6JtJ2kOoxwYIjEwbTGnkXTqW5AeRImw,27695
jax/_src/state/__init__.py,sha256=TU0qQETOXKzVAd1oVaIEtOJTC2OMj1PdFhsExMHFx2g,988
jax/_src/state/__pycache__/__init__.cpython-312.pyc,,
jax/_src/state/__pycache__/discharge.cpython-312.pyc,,
jax/_src/state/__pycache__/indexing.cpython-312.pyc,,
jax/_src/state/__pycache__/primitives.cpython-312.pyc,,
jax/_src/state/__pycache__/types.cpython-312.pyc,,
jax/_src/state/__pycache__/utils.cpython-312.pyc,,
jax/_src/state/discharge.py,sha256=LWYbTx5Yg9iqiQOSa20ivSgYQLXNNduPqW7rnl5MmMU,49382
jax/_src/state/indexing.py,sha256=_ZVN84Jo5Z39SVAJR8WJvXlnDMnGKKMNncdUc1azI9A,12013
jax/_src/state/primitives.py,sha256=RcUYtZ2UJb2IBrZybMQNUhiSEwy7BIab-ZsdtYniZ_8,30494
jax/_src/state/types.py,sha256=BmNGzlUp1lgkLJwcVjR3kTqGBb16XwAq6407NKPwLV4,14870
jax/_src/state/utils.py,sha256=BBCCdjdCyaZmnB1Y6FSfSZ9JyWiAbTX8c1ZaXfR-lt4,4107
jax/_src/test_loader.py,sha256=l7LDHdnqD5g44nUo4Hdwtc8IplXjCcn-L9-RO4Q_qhs,7260
jax/_src/test_util.py,sha256=CPOKUt8zmNPPzNt5UtdKGmzCnJG5uzpVIQbBvs7gR3o,81582
jax/_src/test_warning_util.py,sha256=9CvuFdeyGcmTH5xfz3Wqs0qOUGpOVcOwh4SnJsp6FbE,4074
jax/_src/third_party/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
jax/_src/third_party/__pycache__/__init__.cpython-312.pyc,,
jax/_src/third_party/scipy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
jax/_src/third_party/scipy/__pycache__/__init__.cpython-312.pyc,,
jax/_src/third_party/scipy/__pycache__/betaln.cpython-312.pyc,,
jax/_src/third_party/scipy/__pycache__/interpolate.cpython-312.pyc,,
jax/_src/third_party/scipy/__pycache__/linalg.cpython-312.pyc,,
jax/_src/third_party/scipy/__pycache__/signal_helper.cpython-312.pyc,,
jax/_src/third_party/scipy/__pycache__/special.cpython-312.pyc,,
jax/_src/third_party/scipy/betaln.py,sha256=TT77MW5HcPt-C6m-jHT43mGPLuAKO9ANcoEsXjl8JDE,2373
jax/_src/third_party/scipy/interpolate.py,sha256=5C9-SwIUrwlpBUgAKYskaSLgTbOtpwESJRPv-6cjj0Y,6307
jax/_src/third_party/scipy/linalg.py,sha256=SzmpCoi4kSnLTflDsRa5QdoBcijKHuwmtN9xac2TWlw,3786
jax/_src/third_party/scipy/signal_helper.py,sha256=3IF3mEJ09uSuPeDVM1ntW5dYSoOi-LQDZJkXr38Rv-Q,3508
jax/_src/third_party/scipy/special.py,sha256=weGxx01LOfJDklgdX8vKJVUpTy1_FV_9PWEX2hgNtB0,9136
jax/_src/tpu_custom_call.py,sha256=eN9uT-71pX-X0kHmAyyxj9EB6Zfh3e3otDgzwE9Sa8A,24088
jax/_src/traceback_util.py,sha256=QZinVxddgYKdO3p8OFo2JITpZMJO-wttlxe4L7GcTqM,9141
jax/_src/tree.py,sha256=SIwfh6UL_xEuEMq-ky4bmOmoq1LllYa0ZUhmh4l4-gM,12592
jax/_src/tree_util.py,sha256=0EuW7UwwxIB9ZTQqfKezNzWcleYOM6dJnU0fIwN0C3I,46040
jax/_src/typing.py,sha256=Ed5ujNHsKktTk0Z2nhZojS_77K1INnsPvj4S7o0KQ5Q,3529
jax/_src/util.py,sha256=xL6pNKduybQzWwXDYAIiNAdZFYdCewrJmo2kajo2Ohw,20914
jax/_src/xla_bridge.py,sha256=id_csEcb60m96P3I8wza90epGRXMgm9t6JIxYg2RItE,41310
jax/_src/xla_metadata.py,sha256=ZQUQ4c8Sn11_3LW1WcOl8umxV3NerwhE5NLYKaUUh9I,2103
jax/ad_checkpoint.py,sha256=jnNhXa-FiezkCFP9BlCul194Pz11olQPdZEeoCJQi-U,932
jax/api_util.py,sha256=avYenJ6674XMZHYtWzplyMPO0j2NXOk13BYb1o3vnCc,979
jax/cloud_tpu_init.py,sha256=r_UB06-qStzlo7BWtH7eZ7OMl3ayrG_CQXWQJJwWsYs,651
jax/collect_profile.py,sha256=4dwj_shsaRML-ZSyiUXePBKzIuAp9FKz2y1G5pfM40k,4729
jax/core.py,sha256=ArEQGXJE2KwLLZKXaLAkDM13beCiDP4XdiNAxCKHhxk,8619
jax/custom_batching.py,sha256=MEoypZfGgYqvZrBKtnvL5sQjl8YrjVm11hiFWoTCtV8,691
jax/custom_derivatives.py,sha256=CVhRiRgnWk5hBsc3ZX7mFD0Yst2S9VDAirtwF8rSZcY,1967
jax/custom_transpose.py,sha256=vGj3GjRJQpxl-MhvYztIedauNBiWMj9nD_aJHQiNaYc,664
jax/debug.py,sha256=HXdNZlaM5LxtEYS6fsH3J4LRgiDVL-5W6AXcimbFS5o,1083
jax/distributed.py,sha256=CP5IDXwA4QzxXilcTCM1F6435U3dYsdWkDzO4hr_evE,710
jax/dlpack.py,sha256=UBWbObzJ-N-nFAPvoSTamtXH2uaqSoG2-NnUDYuZuEo,1411
jax/dtypes.py,sha256=iaMlUUaiUH8zFsy5IJEff4s5QTu0EzD5msV-JYAyD6I,1188
jax/errors.py,sha256=MaZbeSxeRyWRMfq4cj6P0GA9UFykLgX3aURPAKhqbts,1363
jax/example_libraries/__init__.py,sha256=9jtOmvvDMLWHLUuqHkvlxcTzvN1yoUxeFmRAxYu7lMw,581
jax/example_libraries/__pycache__/__init__.cpython-312.pyc,,
jax/example_libraries/__pycache__/optimizers.cpython-312.pyc,,
jax/example_libraries/__pycache__/stax.cpython-312.pyc,,
jax/example_libraries/optimizers.py,sha256=T2D3qDPWZ50kwTyDe2IXPPLbOUu04cQTdf51oHdXVvU,20426
jax/example_libraries/stax.py,sha256=jNCoL5RlFv9JnLssUHthGRaNcA9xY57BKPxF76Nz7u8,13862
jax/experimental/__init__.py,sha256=yoE5_c5xRagzLRgNBgo2jRWg31h8Sm-dpKW58vhgjh0,1198
jax/experimental/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/__pycache__/attrs.cpython-312.pyc,,
jax/experimental/__pycache__/buffer_callback.cpython-312.pyc,,
jax/experimental/__pycache__/checkify.cpython-312.pyc,,
jax/experimental/__pycache__/compute_on.cpython-312.pyc,,
jax/experimental/__pycache__/custom_dce.cpython-312.pyc,,
jax/experimental/__pycache__/custom_partitioning.cpython-312.pyc,,
jax/experimental/__pycache__/host_callback.cpython-312.pyc,,
jax/experimental/__pycache__/jet.cpython-312.pyc,,
jax/experimental/__pycache__/layout.cpython-312.pyc,,
jax/experimental/__pycache__/mesh_utils.cpython-312.pyc,,
jax/experimental/__pycache__/multihost_utils.cpython-312.pyc,,
jax/experimental/__pycache__/ode.cpython-312.pyc,,
jax/experimental/__pycache__/pjit.cpython-312.pyc,,
jax/experimental/__pycache__/profiler.cpython-312.pyc,,
jax/experimental/__pycache__/rnn.cpython-312.pyc,,
jax/experimental/__pycache__/serialize_executable.cpython-312.pyc,,
jax/experimental/__pycache__/shard.cpython-312.pyc,,
jax/experimental/__pycache__/shard_alike.cpython-312.pyc,,
jax/experimental/__pycache__/shard_map.cpython-312.pyc,,
jax/experimental/__pycache__/topologies.cpython-312.pyc,,
jax/experimental/__pycache__/transfer.cpython-312.pyc,,
jax/experimental/__pycache__/x64_context.cpython-312.pyc,,
jax/experimental/__pycache__/xla_metadata.cpython-312.pyc,,
jax/experimental/array_serialization/__init__.py,sha256=9jtOmvvDMLWHLUuqHkvlxcTzvN1yoUxeFmRAxYu7lMw,581
jax/experimental/array_serialization/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/array_serialization/__pycache__/serialization.cpython-312.pyc,,
jax/experimental/array_serialization/__pycache__/tensorstore_impl.cpython-312.pyc,,
jax/experimental/array_serialization/serialization.py,sha256=1mAmIFPAPKr2n3nVvyYcaQROo1iuhoBegXic0tuCTuQ,13434
jax/experimental/array_serialization/tensorstore_impl.py,sha256=AdMgKuZM2F6AqEN3zZIhoZ5JAdR4UzImHPb7MQqY07w,21384
jax/experimental/attrs.py,sha256=FRU-Wm4BsARc07DAZ_GyL765Rhuusz4OLtKHmwRPDL0,749
jax/experimental/buffer_callback.py,sha256=Vs6w20qUHDMdhyLsK8XXdXwgt5FaOwQ4gYAnDpiVYiE,998
jax/experimental/checkify.py,sha256=V84hTTNrwnckaRzgvWDmpcKKA95kAakjZwwlfnRaXt0,1213
jax/experimental/colocated_python/__init__.py,sha256=Cp1CgJT_Sn5qLkiO7jW5S0HPWieBmOdpEE-AaYpKs9E,980
jax/experimental/colocated_python/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/colocated_python/__pycache__/api.cpython-312.pyc,,
jax/experimental/colocated_python/__pycache__/func.cpython-312.pyc,,
jax/experimental/colocated_python/__pycache__/func_backend.cpython-312.pyc,,
jax/experimental/colocated_python/__pycache__/obj.cpython-312.pyc,,
jax/experimental/colocated_python/__pycache__/obj_backend.cpython-312.pyc,,
jax/experimental/colocated_python/__pycache__/serialization.cpython-312.pyc,,
jax/experimental/colocated_python/api.py,sha256=yPze9ndZnkUXCj8IDJw14iQnrWenJy1BH21wmGduvlM,2586
jax/experimental/colocated_python/func.py,sha256=axdHSq7-NkcCnyXWadXLJT6u1bXpL4tn3lZdqqgnTYs,17442
jax/experimental/colocated_python/func_backend.py,sha256=lXN-FRbWIWezsICyIc3QYwjpF9-tYgZV1Ut7TBN_dpE,1373
jax/experimental/colocated_python/obj.py,sha256=N_KEQff3d9vzuX5EGkM4Z8rPn6lGEDPNFrTIqcesQ70,5636
jax/experimental/colocated_python/obj_backend.py,sha256=50I4Ttb17qRk7IUrtufYMNlXqyl4KY6bBc61lGQBWfM,2283
jax/experimental/colocated_python/serialization.py,sha256=Z_hm9Jdj4hMLkoaPeWdBbMiFnEfAldfUsrTph67xen0,8343
jax/experimental/compilation_cache/__init__.py,sha256=9jtOmvvDMLWHLUuqHkvlxcTzvN1yoUxeFmRAxYu7lMw,581
jax/experimental/compilation_cache/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/compilation_cache/__pycache__/compilation_cache.cpython-312.pyc,,
jax/experimental/compilation_cache/compilation_cache.py,sha256=OIZLvS1c3Yw_EbbqlV28pC40g5dPdBB-XDrXnHcT6As,820
jax/experimental/compute_on.py,sha256=Mdg2n8JtAsrq1ap_PwUPwoqpoCNGO5rjf7T47e_iaAE,644
jax/experimental/custom_dce.py,sha256=ORRk_kBjbseQ06T_LmLPxYCO7-Rby5U8Erro5U5Gi4A,682
jax/experimental/custom_partitioning.py,sha256=RlaIvfdM8VqlEJC3FajqCmB_EOHyk6iyfGUgaUyR61g,1054
jax/experimental/host_callback.py,sha256=Msk8gUwv_UtJckKwsGU0lYTYGpPG2isWCJrxJ8mzb6g,1201
jax/experimental/jax2tf/__init__.py,sha256=ZqsPlkalGAkzdTwRIutfW1k8NmOHzJn6kfIB1A98pdw,944
jax/experimental/jax2tf/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/jax2tf/__pycache__/call_tf.cpython-312.pyc,,
jax/experimental/jax2tf/__pycache__/impl_no_xla.cpython-312.pyc,,
jax/experimental/jax2tf/__pycache__/jax2tf.cpython-312.pyc,,
jax/experimental/jax2tf/call_tf.py,sha256=pE4kpi_9JhPO64j3HwVVSIDp1HJLtzKlDdTEb43xV8w,28939
jax/experimental/jax2tf/impl_no_xla.py,sha256=7ztEeEhJycNm_yA263efIAa-FsuYeOI2cvvgrqmmgNY,54478
jax/experimental/jax2tf/jax2tf.py,sha256=j-qKSx7u2V3DwkuhQ9jTbwb9fzHUQpAdjqFyXK6nBvo,145449
jax/experimental/jet.py,sha256=qCQiv4k2XoRKOoLWgRGZYgwskBAWuTPqMSGx8zKhgXI,27322
jax/experimental/key_reuse/__init__.py,sha256=5CjqLFSLnAhm_YyrowsWA48ENwxcvcD5KQrNQJTgLpQ,1688
jax/experimental/key_reuse/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/key_reuse/__pycache__/_core.cpython-312.pyc,,
jax/experimental/key_reuse/_core.py,sha256=S-9lqcZXzzk30BW7Eh8FO0yht5c7-CgQ1Qp5CNQPH94,23017
jax/experimental/layout.py,sha256=n5uHNEgJhlMajaLHFx4lD5mHCI9O9Mby3xUGYNschPQ,760
jax/experimental/mesh_utils.py,sha256=Sf5ivjPuZ_u0ufQ38MLmhbEFnlIMs4SHd6lGXWQJgQY,923
jax/experimental/mosaic/__init__.py,sha256=-MnUIUUmkSaNkO5bOpPdXrcTnKVWt9ModYX4cnlq9LY,883
jax/experimental/mosaic/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/mosaic/__pycache__/dialects.cpython-312.pyc,,
jax/experimental/mosaic/dialects.py,sha256=eYxSlw_cu_8usVXcEA3f-cqmlxDUwcTiqLiqNAgWWfs,769
jax/experimental/mosaic/gpu/__init__.py,sha256=x2GTKFmJ6-Iety082LmCbTlgH7qwUeCRwpFwtVW7-sg,3325
jax/experimental/mosaic/gpu/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/mosaic/gpu/__pycache__/core.cpython-312.pyc,,
jax/experimental/mosaic/gpu/__pycache__/dialect_lowering.cpython-312.pyc,,
jax/experimental/mosaic/gpu/__pycache__/fragmented_array.cpython-312.pyc,,
jax/experimental/mosaic/gpu/__pycache__/inference_utils.cpython-312.pyc,,
jax/experimental/mosaic/gpu/__pycache__/launch_context.cpython-312.pyc,,
jax/experimental/mosaic/gpu/__pycache__/layout_inference.cpython-312.pyc,,
jax/experimental/mosaic/gpu/__pycache__/layouts.cpython-312.pyc,,
jax/experimental/mosaic/gpu/__pycache__/mma_utils.cpython-312.pyc,,
jax/experimental/mosaic/gpu/__pycache__/profiler.cpython-312.pyc,,
jax/experimental/mosaic/gpu/__pycache__/tcgen05.cpython-312.pyc,,
jax/experimental/mosaic/gpu/__pycache__/transform_inference.cpython-312.pyc,,
jax/experimental/mosaic/gpu/__pycache__/utils.cpython-312.pyc,,
jax/experimental/mosaic/gpu/__pycache__/wgmma.cpython-312.pyc,,
jax/experimental/mosaic/gpu/core.py,sha256=5JVI3jh_cqj_CG5YA7DPIoKzrbeEhrlF3bChK8Gd-Yc,28975
jax/experimental/mosaic/gpu/dialect_lowering.py,sha256=-8oW9hdHT9QKuXZfx6OXwPsK9z80vWqaCKPkUO-yUNc,48975
jax/experimental/mosaic/gpu/fragmented_array.py,sha256=XcEaFWP6mamY1F7LKUvH2p33gqLHUi_eHTTPvQxRMSE,105823
jax/experimental/mosaic/gpu/inference_utils.py,sha256=3dCnRF7oqi3zb4bInmU7gFQ_ilh9tEoplISP8q-C7xw,7457
jax/experimental/mosaic/gpu/launch_context.py,sha256=NNKk0-zuk0_6ux_LDD6217r_8tLOcSHco0RLl3GQh-M,35588
jax/experimental/mosaic/gpu/layout_inference.py,sha256=jLktgaPC0gjCT2O74qKSWPnarN_V5ggRDXnW3W6GzDY,27589
jax/experimental/mosaic/gpu/layouts.py,sha256=FAojzJO91tQ_CP_f_WQmmTiozQOMJUKxb_ND7Vb-PCA,6820
jax/experimental/mosaic/gpu/mma_utils.py,sha256=_Aoyrzdo3FWifCBUsSrlKMGyJ5NfxhLL01o3jlF1WGc,8664
jax/experimental/mosaic/gpu/profiler.py,sha256=Y5I7eaONisW4DmRGOIlD-nZP6ebtPGmzDOq_SqYnRvw,15532
jax/experimental/mosaic/gpu/tcgen05.py,sha256=-ZCJowVNiu19uK933kjZQZxutiP6A55PRB5VUjLh9mM,38660
jax/experimental/mosaic/gpu/transform_inference.py,sha256=gv5z7uVWjbtC-JU6Aj3ALQDlMkWukZRO5dRIxLegZYk,15163
jax/experimental/mosaic/gpu/utils.py,sha256=6FDBlI__fQ_sou6MIWFaxNJlywkEFO0fI3OUeKL3mBM,47872
jax/experimental/mosaic/gpu/wgmma.py,sha256=Egg8RQ8uCa2VuDZNb5IoAShDNSDzNORdDOoY4nSjNL0,16826
jax/experimental/multihost_utils.py,sha256=AeuNxLlUeBoi9q4tb2t1ZFNR8bInLDZvmccThjWfCmk,21864
jax/experimental/ode.py,sha256=VmaWgwXaD1N9WspkCJ7pvdbUtYRuGtp4cpQaqJkf__8,10859
jax/experimental/pallas/__init__.py,sha256=M_Hi-aBCjU2TEJlg6nJxBa-G0Z7w6iJ394TnSNf0_4Y,4208
jax/experimental/pallas/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/pallas/__pycache__/mosaic_gpu.cpython-312.pyc,,
jax/experimental/pallas/__pycache__/tpu.cpython-312.pyc,,
jax/experimental/pallas/__pycache__/triton.cpython-312.pyc,,
jax/experimental/pallas/mosaic_gpu.py,sha256=68Fki4SX6Ozqj_7XMnUeJk-UhZqKSEOHR0c7iTGaUW4,4408
jax/experimental/pallas/ops/__init__.py,sha256=vC8qlznuwgbYeFDJqP8pDUIf2gEKFrCHg3EZYdTkiwM,764
jax/experimental/pallas/ops/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/pallas/ops/gpu/__init__.py,sha256=mS3YSYUZFIG68T5Zo9CFGkT7wCyWRycnWV4L36CnHYU,581
jax/experimental/pallas/ops/gpu/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/pallas/ops/gpu/__pycache__/attention.cpython-312.pyc,,
jax/experimental/pallas/ops/gpu/__pycache__/attention_mgpu.cpython-312.pyc,,
jax/experimental/pallas/ops/gpu/__pycache__/decode_attention.cpython-312.pyc,,
jax/experimental/pallas/ops/gpu/__pycache__/layer_norm.cpython-312.pyc,,
jax/experimental/pallas/ops/gpu/__pycache__/paged_attention.cpython-312.pyc,,
jax/experimental/pallas/ops/gpu/__pycache__/rms_norm.cpython-312.pyc,,
jax/experimental/pallas/ops/gpu/__pycache__/softmax.cpython-312.pyc,,
jax/experimental/pallas/ops/gpu/attention.py,sha256=3RC1NexI4oLsYmCPHtaQhDT1WVW6dzXFsm5SDw6JAoQ,22429
jax/experimental/pallas/ops/gpu/attention_mgpu.py,sha256=s9CpPX-e6KRg4B6lDgRf6eE8_RSUJSQ83K0MJZnmOBM,35693
jax/experimental/pallas/ops/gpu/decode_attention.py,sha256=cuzBkk0M6p6hrKOUjtXOAAKH5Lu6PLWpE5pFyl-f2sI,17086
jax/experimental/pallas/ops/gpu/layer_norm.py,sha256=GcRX-fdAvl2t_EsJCRuomIkNBdY2Ugdln3khhXiD_v4,11151
jax/experimental/pallas/ops/gpu/paged_attention.py,sha256=VXmKKeLEObv-XZg9iweUWaA2BLGfCDYOaUCMFxg-oXE,14058
jax/experimental/pallas/ops/gpu/rms_norm.py,sha256=QIDM6wevDVH1TWesh4pwGVKMsBNh6j0YGykrngHtpb4,10051
jax/experimental/pallas/ops/gpu/softmax.py,sha256=ydajdpaW_oAOwwdGJUx-t2TmIx9QfHhnXX_lNdtmVL4,2774
jax/experimental/pallas/ops/tpu/__init__.py,sha256=t5wRc_z8eIDwV0votvqn8WtOUEd53ZNf_J4VRYLJepc,581
jax/experimental/pallas/ops/tpu/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/__pycache__/all_gather.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/__pycache__/example_kernel.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/__pycache__/flash_attention.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/__pycache__/matmul.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/all_gather.py,sha256=GpKrnX5GiDvr4LOaR3lmwP9ANfnEc3zNUayqpdVPGj8,5579
jax/experimental/pallas/ops/tpu/example_kernel.py,sha256=jzsyvU-Lk0PIorE9XCw2NDdqdp8Cn4bqsSe76DBxeEM,802
jax/experimental/pallas/ops/tpu/flash_attention.py,sha256=dQzsMyjx9xdAib4Xxp3cF6SBItXDzEX3xgtg5f3mUVA,49939
jax/experimental/pallas/ops/tpu/matmul.py,sha256=yoH1Wi0b3-AUzPNWrrKIxT37rh5HnfGZGzpTOyo1bSc,2720
jax/experimental/pallas/ops/tpu/megablox/__init__.py,sha256=HRWx7FtM9_17OLitbxwIZp-A9RM61uSL6QAcBGJyw_w,650
jax/experimental/pallas/ops/tpu/megablox/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/megablox/__pycache__/common.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/megablox/__pycache__/gmm.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/megablox/__pycache__/ops.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/megablox/common.py,sha256=9zg98VzHJh5l4Sf_SKly1nrXFWeWeyKoe7ZSPn6Ht38,1969
jax/experimental/pallas/ops/tpu/megablox/gmm.py,sha256=vNNgQIirDK7pyDViP2foec7GC8EhJuxNjR1mp3kHwjU,27249
jax/experimental/pallas/ops/tpu/megablox/ops.py,sha256=4Xg3O0bnP5HTWsawS0hAm8mF9HTvRHVK65RlopkA1rw,2903
jax/experimental/pallas/ops/tpu/paged_attention/__init__.py,sha256=Q6gFLakYVRJDRxxvqq6vmtDYup-edUHZy54j9194JfY,700
jax/experimental/pallas/ops/tpu/paged_attention/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/paged_attention/__pycache__/paged_attention_kernel.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/paged_attention/__pycache__/quantization_utils.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/paged_attention/__pycache__/util.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/paged_attention/paged_attention_kernel.py,sha256=qkP-ur48ATrtBoW7F7udqF6gXhLKwy0GeqFMZYWK9wM,21783
jax/experimental/pallas/ops/tpu/paged_attention/quantization_utils.py,sha256=5y0QHJo2yDsafF_lgeSO_izChspLHpVOvbFWZ-2d0ss,2556
jax/experimental/pallas/ops/tpu/paged_attention/util.py,sha256=bTC1ls00kzUm_K5NOgDQF003z1G_fGZDMXkYeb0Bw2s,3253
jax/experimental/pallas/ops/tpu/ragged_paged_attention/__init__.py,sha256=XuQq_I5Pn_5EDon__0hWN9NOrOJ3grU3y0qqc8qKSsg,1055
jax/experimental/pallas/ops/tpu/ragged_paged_attention/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/ragged_paged_attention/__pycache__/kernel.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/ragged_paged_attention/__pycache__/tuned_block_sizes.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/ragged_paged_attention/kernel.py,sha256=Zqzh9hUZWwPqrQx3oulBW7hqg_M5_XYYIn6453OaT-I,30090
jax/experimental/pallas/ops/tpu/ragged_paged_attention/tuned_block_sizes.py,sha256=_QZBjMU41ln_i9Fo6QSHjclzxCd0f4xAdiTTy-39iXU,25959
jax/experimental/pallas/ops/tpu/random/__init__.py,sha256=gEMCMS8LKTvoFm0t7khiAnQg78EDUuMI-fvTvw250eo,682
jax/experimental/pallas/ops/tpu/random/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/random/__pycache__/philox.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/random/__pycache__/prng_utils.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/random/__pycache__/threefry.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/random/philox.py,sha256=ySUyKx2lromSM1Wez6nRYEuzy2srOzAGb8KbiMoeuUo,7485
jax/experimental/pallas/ops/tpu/random/prng_utils.py,sha256=CH1W4SuTJtdKNVA-7G64qf_RwtJM7zteTuQsfsRieMk,1771
jax/experimental/pallas/ops/tpu/random/threefry.py,sha256=0HyU9Y_PUpAe_4ypcaqmGgDHeZRmEwsPVOnFhNho18I,4330
jax/experimental/pallas/ops/tpu/splash_attention/__init__.py,sha256=8y556G1Vpmglvw8FbNzeB4x6QK6PLkKUBnMsL4vleNs,2738
jax/experimental/pallas/ops/tpu/splash_attention/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/splash_attention/__pycache__/splash_attention_kernel.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/splash_attention/__pycache__/splash_attention_mask.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/splash_attention/__pycache__/splash_attention_mask_info.cpython-312.pyc,,
jax/experimental/pallas/ops/tpu/splash_attention/splash_attention_kernel.py,sha256=SnrFrpUIAmRscvG0SRwNYSMhjA8wP-Cvl6c3aSYd29E,75029
jax/experimental/pallas/ops/tpu/splash_attention/splash_attention_mask.py,sha256=PMWvDtlCtVcLzhfZlhJLOdNwh-YJJAVe16QZLB3HcGY,16307
jax/experimental/pallas/ops/tpu/splash_attention/splash_attention_mask_info.py,sha256=igQKMeasDhyWYlA9GXaj-4cnGJgZUnefvR_UQP0aw5I,40287
jax/experimental/pallas/tpu.py,sha256=yIMjXG8bFuVI3JLZLLTHRs7clAJBk5SZpB7hCCrvFFg,4319
jax/experimental/pallas/triton.py,sha256=8FEMurTT9d-X8tipH-706ETuSLjFkT0LGWiOHjp8zfM,948
jax/experimental/pjit.py,sha256=2Qi4A4tKn1p6pjxysiCY5UAsfQq_KVZ7QpMX3gRJw30,749
jax/experimental/profiler.py,sha256=Rxlgbb4TPJsfeWXwLYG58gW0PCqBjDVWswlCCLr7fBg,1413
jax/experimental/rnn.py,sha256=q__NeQnA8uZspzC5zFU5a-593i3VOEAbrRmsiJwYEfU,20203
jax/experimental/roofline/__init__.py,sha256=ftyNH95ANQHFYSq9hsweKfJNP47sfipMm-3XFsr32yw,1260
jax/experimental/roofline/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/roofline/__pycache__/roofline.cpython-312.pyc,,
jax/experimental/roofline/__pycache__/rooflines.cpython-312.pyc,,
jax/experimental/roofline/roofline.py,sha256=TfojGX6xEDcD0wUN2MjwpoFlmHH_DffC8Nl7hiKeO_A,10755
jax/experimental/roofline/rooflines.py,sha256=aTQzp55zF-mpfB682LsrpbN0a88Y-35NIRKzNNcv8eI,20404
jax/experimental/serialize_executable.py,sha256=zwFcP0UtWftyQAygXYXHsXzm7DXzx5cHAoPS8YB5fCI,4378
jax/experimental/shard.py,sha256=xTXar7eZIDvVAXyXUdgCPaqHEUmbl8zPh6iF5oLt380,696
jax/experimental/shard_alike.py,sha256=dbrhVCnWEX39TsQYRyGoClFByDmqtmDlKGtOmlT-m2M,646
jax/experimental/shard_map.py,sha256=u2u1c74hpEv5Qy1GJCKYxeFq9jVOTXIeTomnAgX4luI,4426
jax/experimental/source_mapper/__init__.py,sha256=g5b_71QFD84RscRTmCDm7l_TfUYkcW6gZuRd8rgFg9Q,1698
jax/experimental/source_mapper/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/source_mapper/__pycache__/common.cpython-312.pyc,,
jax/experimental/source_mapper/__pycache__/generate_map.cpython-312.pyc,,
jax/experimental/source_mapper/__pycache__/hlo.cpython-312.pyc,,
jax/experimental/source_mapper/__pycache__/jaxpr.cpython-312.pyc,,
jax/experimental/source_mapper/__pycache__/mlir.cpython-312.pyc,,
jax/experimental/source_mapper/common.py,sha256=2wh5nZMvCdhjDBQKw539R5WTnmFJWNzMBRgaZxqI__s,2481
jax/experimental/source_mapper/generate_map.py,sha256=rCr8YPEZI1OAjw_GZFUndUkhgVVS7gcv6dDVszTey3w,2102
jax/experimental/source_mapper/hlo.py,sha256=ItS2ytK1D4cBQOZsap0UzyWA-saoI_T_fWH2DVoMSFA,4112
jax/experimental/source_mapper/jaxpr.py,sha256=PXa9zMzP2_fD_0vxo8Za-mw21pXp2Aao6IQkQIqmoJU,2595
jax/experimental/source_mapper/mlir.py,sha256=U2YSWC9tp_DlnsrRzuzsAU7W2PY6ZwtWedHiuMSTQ5M,4686
jax/experimental/sparse/__init__.py,sha256=iYfDK0sdq3ErijcE7Va7cRjEHjzuJ3ykCwnHSYxNMPs,11186
jax/experimental/sparse/__pycache__/__init__.cpython-312.pyc,,
jax/experimental/sparse/__pycache__/_base.cpython-312.pyc,,
jax/experimental/sparse/__pycache__/_lowerings.cpython-312.pyc,,
jax/experimental/sparse/__pycache__/ad.cpython-312.pyc,,
jax/experimental/sparse/__pycache__/api.cpython-312.pyc,,
jax/experimental/sparse/__pycache__/bcoo.cpython-312.pyc,,
jax/experimental/sparse/__pycache__/bcsr.cpython-312.pyc,,
jax/experimental/sparse/__pycache__/coo.cpython-312.pyc,,
jax/experimental/sparse/__pycache__/csr.cpython-312.pyc,,
jax/experimental/sparse/__pycache__/linalg.cpython-312.pyc,,
jax/experimental/sparse/__pycache__/nm.cpython-312.pyc,,
jax/experimental/sparse/__pycache__/random.cpython-312.pyc,,
jax/experimental/sparse/__pycache__/test_util.cpython-312.pyc,,
jax/experimental/sparse/__pycache__/transform.cpython-312.pyc,,
jax/experimental/sparse/__pycache__/util.cpython-312.pyc,,
jax/experimental/sparse/_base.py,sha256=qyiBgPa12uwS1nLpSDw8dTD1VEq0v9P2_CGTNyPCA_g,3239
jax/experimental/sparse/_lowerings.py,sha256=nNviA0Ed0Dq-J_mRF1us4DQ4GUP69Gu3WBFzuSbLlWI,13334
jax/experimental/sparse/ad.py,sha256=zJLXiDufoAVcLTHdFitEXdbQUM_XtDiJoIktwDVuNZU,7789
jax/experimental/sparse/api.py,sha256=DrqEZmlIr6n6kpJjNli9J_rqMX3UO3sPPW7E8_t-u4g,6596
jax/experimental/sparse/bcoo.py,sha256=D2n0P_IQOqOhyF0f2jWIpZeDA9EgFpK3G-V7YICHm-g,129450
jax/experimental/sparse/bcsr.py,sha256=qSvCId7ZjWHNZNDVpEPdc_dTnB9446V5xsMiIFdpAxs,41314
jax/experimental/sparse/coo.py,sha256=_R22aml359eVXXBA_3BP4mQfrJAfmdCtD_BzCDLy2vw,24165
jax/experimental/sparse/csr.py,sha256=dhOQNlU57XwMZom4n8VVfhc8t4TP1OxPcwtDurK0il0,24332
jax/experimental/sparse/linalg.py,sha256=Zk0pMWAwHMExcC2VaAu8Pelr1rRcVW7K2G1io_XKh0Q,23411
jax/experimental/sparse/nm.py,sha256=ypXA8WaigylNbaRqJRlfXyurJC00f41VF1jxqUAiI3I,8568
jax/experimental/sparse/random.py,sha256=o_g5_F8_EN4vmncmIkFKChU5uX4L3MVM6z-_qdoYUKI,3887
jax/experimental/sparse/test_util.py,sha256=hBw1SDgjVL2KkadjSdvIs5DcHqmdrtQHTHUCwj-MBfw,9433
jax/experimental/sparse/transform.py,sha256=HzxzEfAwUqwrpFXPx8ahQlARBZLOxWR4XBf8_9L_cuk,37484
jax/experimental/sparse/util.py,sha256=zb8BrK9aG5DC9ubS1ZmjSKfLhnnGVKA5ydy9i0hKzA8,4337
jax/experimental/topologies.py,sha256=YnSqioiXZqwsX-WzVhHVUO44lAib22-TkfIzC0KRfLA,2083
jax/experimental/transfer.py,sha256=E8dFkOjF0jnpEYk6--VEdnozMEcC-ntdSG-GkKln9eI,2327
jax/experimental/x64_context.py,sha256=lUrmtrYhu0ZGwd_j8vrzFqQnwfDptgpHxZbqV94Hb30,1710
jax/experimental/xla_metadata.py,sha256=YoNAzPbdARYfGzGTUVoxTfHG9dDrobUvWaPI4uvHRJk,658
jax/export.py,sha256=OzLiZq2zwjC4gDUs-3VcHVFF5IUZtIhMaaTdnV89qIA,1537
jax/extend/__init__.py,sha256=tFbMTUdo-HwWjrmedD-df0hndkPkqGWPuZT_Tb-Fspc,1600
jax/extend/__pycache__/__init__.cpython-312.pyc,,
jax/extend/__pycache__/backend.cpython-312.pyc,,
jax/extend/__pycache__/ffi.cpython-312.pyc,,
jax/extend/__pycache__/ifrt_programs.cpython-312.pyc,,
jax/extend/__pycache__/linear_util.cpython-312.pyc,,
jax/extend/__pycache__/random.cpython-312.pyc,,
jax/extend/__pycache__/source_info_util.cpython-312.pyc,,
jax/extend/backend.py,sha256=ODjEVf5_5U37PZk5WVp8wI8thqOZO8XAdRV6Rortr8E,1054
jax/extend/core/__init__.py,sha256=o_q4S8jW_Tc0986SMqpjcifigZq-Ez2OhFZo6aBLwzM,1036
jax/extend/core/__pycache__/__init__.cpython-312.pyc,,
jax/extend/core/__pycache__/primitives.cpython-312.pyc,,
jax/extend/core/primitives.py,sha256=sqGt3gY8h_icP_JFainCpc5aOKROC2YGQ2YoFimr8mA,6268
jax/extend/ffi.py,sha256=31OhFUi5t8Msov6VnqpTj8U8bpDe9V746tMb1enGh9w,1783
jax/extend/ifrt_programs.py,sha256=TCWvR-K8nHQ9PuUNoMWVAfEuYsutTNrefKwLg4AIRao,787
jax/extend/linear_util.py,sha256=xag9NUqPndR7mh3NDQ4TluFpc6hFFd4r7Q5LarAFqvY,1592
jax/extend/mlir/__init__.py,sha256=t5wRc_z8eIDwV0votvqn8WtOUEd53ZNf_J4VRYLJepc,581
jax/extend/mlir/__pycache__/__init__.cpython-312.pyc,,
jax/extend/mlir/__pycache__/ir.cpython-312.pyc,,
jax/extend/mlir/__pycache__/passmanager.cpython-312.pyc,,
jax/extend/mlir/dialects/__init__.py,sha256=t5wRc_z8eIDwV0votvqn8WtOUEd53ZNf_J4VRYLJepc,581
jax/extend/mlir/dialects/__pycache__/__init__.cpython-312.pyc,,
jax/extend/mlir/dialects/__pycache__/arith.cpython-312.pyc,,
jax/extend/mlir/dialects/__pycache__/builtin.cpython-312.pyc,,
jax/extend/mlir/dialects/__pycache__/chlo.cpython-312.pyc,,
jax/extend/mlir/dialects/__pycache__/func.cpython-312.pyc,,
jax/extend/mlir/dialects/__pycache__/math.cpython-312.pyc,,
jax/extend/mlir/dialects/__pycache__/memref.cpython-312.pyc,,
jax/extend/mlir/dialects/__pycache__/scf.cpython-312.pyc,,
jax/extend/mlir/dialects/__pycache__/sdy.cpython-312.pyc,,
jax/extend/mlir/dialects/__pycache__/sparse_tensor.cpython-312.pyc,,
jax/extend/mlir/dialects/__pycache__/stablehlo.cpython-312.pyc,,
jax/extend/mlir/dialects/__pycache__/vector.cpython-312.pyc,,
jax/extend/mlir/dialects/arith.py,sha256=Au6mT9C4OrdpaGGwJ4_bauTFjGFQKF7wilv62mxzP1E,643
jax/extend/mlir/dialects/builtin.py,sha256=Y1QDRes-Arh7uBLJpySSqygBwUovalHyBq71m3uPbiQ,645
jax/extend/mlir/dialects/chlo.py,sha256=sfxvYEEBgGVd8fDXOlyfGtOLCCAW0xquLrSlTJFA81A,642
jax/extend/mlir/dialects/func.py,sha256=sCnRWz5gKtZ8Cf5NpeCIQ0zVh-nM6H95ttjzksTRrXk,642
jax/extend/mlir/dialects/math.py,sha256=G2gFkarEChMUEjv-vjDc4O09e-1orPhh_zHJhjbPPRQ,642
jax/extend/mlir/dialects/memref.py,sha256=nPR3oOrsENZ-mHjHKFSD48PpQMZHsVLfUVCwomBhv_w,644
jax/extend/mlir/dialects/scf.py,sha256=hmcJIF7RUqzeeP_NIOampnvCB5Jb6T8Xex4T03IeKrI,641
jax/extend/mlir/dialects/sdy.py,sha256=4dpcOKEHHkDh6cXdyEm2VGE9W-8701MD9egZmWDlkRM,641
jax/extend/mlir/dialects/sparse_tensor.py,sha256=TaFY58y273rRITr8WxSM7LB1-sTAa03dCAjH4F8ZWaE,651
jax/extend/mlir/dialects/stablehlo.py,sha256=AbJQc7zghgrJh-BgRB31L7iHrNLVaEk4AELejcq7QiE,647
jax/extend/mlir/dialects/vector.py,sha256=xP03nScJg-t7olD8_P8SiFGVnV6Qi0Fwn9uGK0MHOXI,644
jax/extend/mlir/ir.py,sha256=1IirtLROpBL2nVDNSlP_3c6u09xXaKLqBkzF1JtmGz4,631
jax/extend/mlir/passmanager.py,sha256=1rfuCocMdFlx6It95ynBCP-8JC5xEAEG7rBdLJ7SpFI,640
jax/extend/random.py,sha256=w1OM6RCcGwmzIr9bdGJJTNKIcMO4ypXbzmeEORlBHjQ,1083
jax/extend/source_info_util.py,sha256=IxZYjLDlwOH1uE3A_vc25dy25QZ6FABMb4RqrkiA5hU,1215
jax/ffi.py,sha256=S4EjUxeohrl-Kmmnu8rykPCTxMl2LAxGdaerWLhd5ac,1052
jax/flatten_util.py,sha256=jGwQUB80s2jr78XuEaT1_OP2OUyVm8Pk-yAQq-C1SsY,645
jax/image/__init__.py,sha256=cthOdx-HsZ6SgvW19tdNCDHmC4Proc8da1hY1hRztTI,1027
jax/image/__pycache__/__init__.cpython-312.pyc,,
jax/interpreters/__init__.py,sha256=HNgKpgKpz4svFyTlHE9wMrDp5iQtg6XeeUshYBOnRko,690
jax/interpreters/__pycache__/__init__.cpython-312.pyc,,
jax/interpreters/__pycache__/ad.cpython-312.pyc,,
jax/interpreters/__pycache__/batching.cpython-312.pyc,,
jax/interpreters/__pycache__/mlir.cpython-312.pyc,,
jax/interpreters/__pycache__/partial_eval.cpython-312.pyc,,
jax/interpreters/__pycache__/pxla.cpython-312.pyc,,
jax/interpreters/__pycache__/xla.cpython-312.pyc,,
jax/interpreters/ad.py,sha256=p0gJFj9VvrsZbZavI-xg00Y-PzEu5Rw_6d3upq5u92Y,2759
jax/interpreters/batching.py,sha256=QqKOP4uX4F1PSf96ZrXi6x3DOIrU8wyWqXGTlt1nC2g,2574
jax/interpreters/mlir.py,sha256=H_cKoKAmfXBH7FEmMRthJ7M2Hjs5HeD4c-FkLFHLb1o,3411
jax/interpreters/partial_eval.py,sha256=DJBwBbcrQ7Wrj9EA_TyFNsiq6ZSymxqYpGgPZscdvvI,3924
jax/interpreters/pxla.py,sha256=VN0Mlb4IreJ9Tqez8i-QWn1EXVcrDWTLPeioTkeL-qE,1801
jax/interpreters/xla.py,sha256=JD6CBHHS9gTmO83age6Xwe9CxuoHLlYYBoD3RxyL41U,1584
jax/lax/__init__.py,sha256=ACj0m4Hr7d-auCOp9R7xznUllFua9xpAepf_HNeNr2g,12401
jax/lax/__pycache__/__init__.cpython-312.pyc,,
jax/lax/__pycache__/linalg.cpython-312.pyc,,
jax/lax/linalg.py,sha256=lpjXQ-7jLL0pTG5IzCz5Z0Usf2l4B5yeuNgieGHjrW8,1624
jax/lib/__init__.py,sha256=wy3yxhlsb9k2th9Dfsr6eS59L2qLIxH_IoTM4cPfx3M,774
jax/lib/__pycache__/__init__.cpython-312.pyc,,
jax/lib/__pycache__/xla_bridge.cpython-312.pyc,,
jax/lib/__pycache__/xla_client.cpython-312.pyc,,
jax/lib/__pycache__/xla_extension.cpython-312.pyc,,
jax/lib/xla_bridge.py,sha256=NtW-LD_Dh_TkdCT-19CZ91nvJPWy27aUzCon8ij3CWU,1246
jax/lib/xla_client.py,sha256=UEViLrEA2wi-MUO6ZYNF22r4yboy9TKekN645jEBkrM,5094
jax/lib/xla_extension.py,sha256=J5ldZNiB9BxU8wGkl1rWNUVTAGZP4jHSkZiMuVuOXf8,4428
jax/monitoring.py,sha256=xWZF_CyFg7Vv7MYmIyb3JWNA-AwsvIm9oc-MbGX4Bbw,1415
jax/nn/__init__.py,sha256=vC3v2Eg_WOW8kJWDzbpKS35jDaabCP9iwnOyV1HQEJ8,1730
jax/nn/__pycache__/__init__.cpython-312.pyc,,
jax/nn/__pycache__/initializers.cpython-312.pyc,,
jax/nn/initializers.py,sha256=finfAJmOEZdPIzY2roSIBTOmC4rKEW-rchpH9d6Dc2g,1469
jax/numpy/__init__.py,sha256=thBier3T208VbUA_cyWKJyerGeh_M35l8YmANlAPeTE,12190
jax/numpy/__init__.pyi,sha256=_qp-rWm2Gd9OyT0HwFshWVAtZFAoO3Djs7AZC4ANf6g,44282
jax/numpy/__pycache__/__init__.cpython-312.pyc,,
jax/numpy/__pycache__/fft.cpython-312.pyc,,
jax/numpy/__pycache__/linalg.cpython-312.pyc,,
jax/numpy/fft.py,sha256=iNatBIYRSwpXquPSgtNRMe6-dhbKkv2yoH6UJ6PFoGM,1084
jax/numpy/linalg.py,sha256=bnzLmLpLYSDRIICkjCEsH1WmA7wNOdSKhmO25gPvTqI,1419
jax/ops/__init__.py,sha256=VIAczIKYAFWawHdNmLmptUYnFQiBwHwlsa4LpgGEbIc,870
jax/ops/__pycache__/__init__.cpython-312.pyc,,
jax/profiler.py,sha256=QIqsPMUyx3B4WMFXs3WR7nL4yMxWnbDNwJFEw9vpx0s,1177
jax/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
jax/random.py,sha256=-1oFQ0v1HFzwvFTipdl1JQVqsk8dXRJe2ZH4xTyU51g,9434
jax/scipy/__init__.py,sha256=DXszF0t3ZbfDeoFEP_GyBb0OOr6F-4yqsZzmen7pY58,1474
jax/scipy/__pycache__/__init__.cpython-312.pyc,,
jax/scipy/__pycache__/fft.cpython-312.pyc,,
jax/scipy/__pycache__/integrate.cpython-312.pyc,,
jax/scipy/__pycache__/linalg.cpython-312.pyc,,
jax/scipy/__pycache__/ndimage.cpython-312.pyc,,
jax/scipy/__pycache__/signal.cpython-312.pyc,,
jax/scipy/__pycache__/special.cpython-312.pyc,,
jax/scipy/cluster/__init__.py,sha256=sgIbEEx62OubzDO36E3EkqjbYY_3qsErPOTQE6lsj5E,768
jax/scipy/cluster/__pycache__/__init__.cpython-312.pyc,,
jax/scipy/cluster/__pycache__/vq.cpython-312.pyc,,
jax/scipy/cluster/vq.py,sha256=lkLSlkWFi6TLjwuzTX7hVSiC1IkCYd3OWgn8YtIAJd0,776
jax/scipy/fft.py,sha256=PVKQ4r-vl4J9Ct6LrhZWZ7jMF7frIXggiO98Bh0oVIE,809
jax/scipy/integrate.py,sha256=H9iq7YKgsZQ1d0SUvGcgPnEgD1TBdWJXHYjfUOGzT8Y,777
jax/scipy/interpolate/__init__.py,sha256=RI5oUXPC-8tKmYURnnrnA-e1LDbnNVOVVqkGSpagEBM,760
jax/scipy/interpolate/__pycache__/__init__.cpython-312.pyc,,
jax/scipy/linalg.py,sha256=GXC1BgLMztvwRMxddJhvJS3obG81HLQ0OJFVt_Iv4V8,1370
jax/scipy/ndimage.py,sha256=ei-ZfRiDXrlY4nYC1SFL7nnGHFrC_emD_NAicGhH4vU,788
jax/scipy/optimize/__init__.py,sha256=cAw1rT1e0Mu-22vBUuJb6Q-CQF8fbIUBlKYpynB7e2s,822
jax/scipy/optimize/__pycache__/__init__.cpython-312.pyc,,
jax/scipy/signal.py,sha256=nW4h017jShZBLL3J2NMhLwlOv46wK1MhEPeKlPTj710,975
jax/scipy/sparse/__init__.py,sha256=ugltlihEjwgorQxgfziHC-03UeYy4JzdQmTxrt1cnTo,757
jax/scipy/sparse/__pycache__/__init__.cpython-312.pyc,,
jax/scipy/sparse/__pycache__/linalg.cpython-312.pyc,,
jax/scipy/sparse/linalg.py,sha256=wpjjS1G1UNISyhZVFoJMMgIkqs-L8ASdvtQAbvEBsuk,810
jax/scipy/spatial/__init__.py,sha256=t5wRc_z8eIDwV0votvqn8WtOUEd53ZNf_J4VRYLJepc,581
jax/scipy/spatial/__pycache__/__init__.cpython-312.pyc,,
jax/scipy/spatial/__pycache__/transform.cpython-312.pyc,,
jax/scipy/spatial/transform.py,sha256=cm7lKi8aUepI3cOwTnjrldhFh7hBv89CR_m3pFj3r2o,802
jax/scipy/special.py,sha256=s-0P9YKfZmmTze9BVkrr125COCf6H17G-GFvsmzvCOc,2406
jax/scipy/stats/__init__.py,sha256=ssiwAm2AdZ81MPMM11015yMNogj4DGsNfWfq507yhlo,1995
jax/scipy/stats/__pycache__/__init__.cpython-312.pyc,,
jax/scipy/stats/__pycache__/bernoulli.cpython-312.pyc,,
jax/scipy/stats/__pycache__/beta.cpython-312.pyc,,
jax/scipy/stats/__pycache__/betabinom.cpython-312.pyc,,
jax/scipy/stats/__pycache__/binom.cpython-312.pyc,,
jax/scipy/stats/__pycache__/cauchy.cpython-312.pyc,,
jax/scipy/stats/__pycache__/chi2.cpython-312.pyc,,
jax/scipy/stats/__pycache__/dirichlet.cpython-312.pyc,,
jax/scipy/stats/__pycache__/expon.cpython-312.pyc,,
jax/scipy/stats/__pycache__/gamma.cpython-312.pyc,,
jax/scipy/stats/__pycache__/gennorm.cpython-312.pyc,,
jax/scipy/stats/__pycache__/geom.cpython-312.pyc,,
jax/scipy/stats/__pycache__/laplace.cpython-312.pyc,,
jax/scipy/stats/__pycache__/logistic.cpython-312.pyc,,
jax/scipy/stats/__pycache__/multinomial.cpython-312.pyc,,
jax/scipy/stats/__pycache__/multivariate_normal.cpython-312.pyc,,
jax/scipy/stats/__pycache__/nbinom.cpython-312.pyc,,
jax/scipy/stats/__pycache__/norm.cpython-312.pyc,,
jax/scipy/stats/__pycache__/pareto.cpython-312.pyc,,
jax/scipy/stats/__pycache__/poisson.cpython-312.pyc,,
jax/scipy/stats/__pycache__/t.cpython-312.pyc,,
jax/scipy/stats/__pycache__/truncnorm.cpython-312.pyc,,
jax/scipy/stats/__pycache__/uniform.cpython-312.pyc,,
jax/scipy/stats/__pycache__/vonmises.cpython-312.pyc,,
jax/scipy/stats/__pycache__/wrapcauchy.cpython-312.pyc,,
jax/scipy/stats/bernoulli.py,sha256=llkwAlvFH0qNU1BHdxn9CXXOBAPg0ffnbW_WQQ4LKVg,819
jax/scipy/stats/beta.py,sha256=5UIf6_JtqVZfAl1dDc2N3K8SkG0OV1bBkC3vWpY8-Hs,851
jax/scipy/stats/betabinom.py,sha256=5T6965_jHQxG4oR2YocPQRKcDo5AGxkhIyrk2iz-Kk0,792
jax/scipy/stats/binom.py,sha256=XGO5jDk3nRRgGwHQU6KEl9_kQ5X2qF-2TLgvm3ZUvvs,659
jax/scipy/stats/cauchy.py,sha256=MfAMnK9eG2wcutA5FVAyhU6iVYSSNgzI1XFT1xtNIfg,881
jax/scipy/stats/chi2.py,sha256=f-6i-0twBiOrak26tc7bEnWgOODA3iFwEFgi_An9udo,851
jax/scipy/stats/dirichlet.py,sha256=FUHSEmXKmVUneXuxgaIZM28s6GxlusbymdRYEPnDiUE,792
jax/scipy/stats/expon.py,sha256=YClw5PcqAXgq3VD4ukZ19HfTmRCMtrGk6E2BpXb6U9E,866
jax/scipy/stats/gamma.py,sha256=TvP4BE8MROMxQL6YNDQIHSaZCfxumHkjGbCXw9O2iUo,852
jax/scipy/stats/gennorm.py,sha256=hqwz7ueAgdtzs1v0LHTTro2PLKRDjWxTFuu9jXwXOyI,804
jax/scipy/stats/geom.py,sha256=pdp2tCdXOa3CUWyKJCyrFrMR7TBX0La5tVI4ALAETI4,787
jax/scipy/stats/laplace.py,sha256=7Lap3E3_neHYpG5mUFlYxZnwAT1J8mTrNPnn8BSEGrA,804
jax/scipy/stats/logistic.py,sha256=IoHe_TMB0CH5EMAXjmqRMP8LjR4lTS_x9Zf4K7K5e2g,845
jax/scipy/stats/multinomial.py,sha256=hfRYQL_GeszF4sJXx5Y416JgXoXVw5vxtQIt1NmkCDU,794
jax/scipy/stats/multivariate_normal.py,sha256=YYqtLLZmjsLWeCIoDpxUk-rHde-g9unfo6ofg44nSBQ,802
jax/scipy/stats/nbinom.py,sha256=hWMMt7D_rLRtiGjlpHcbOXJF03BfA3v9Cu972twYKCY,660
jax/scipy/stats/norm.py,sha256=qNMz53OSiWOZGt6TZkJZvWAFocdUMUu-DsysM9kg2K4,879
jax/scipy/stats/pareto.py,sha256=xOqP9JMLAYdVXMFpCfCoRqxJNXVeWfJXkEqpOsrUjHc,789
jax/scipy/stats/poisson.py,sha256=Guu0WOaGAwtujTTWVlBZiBg2fLu4JOjx0fumX4m4Ems,804
jax/scipy/stats/t.py,sha256=kPmOWYVodYyD6cTxzX98qNxiFeWpDHBM1WwrdK5Ztoc,784
jax/scipy/stats/truncnorm.py,sha256=q5aSLcidwhZqFi9sNjqvFF9RxT_YLcYBMW2DofDoE5M,855
jax/scipy/stats/uniform.py,sha256=KCmUJttnFPbr-_ECG7O5xd-fy8CqvysIhepltRj_6SU,818
jax/scipy/stats/vonmises.py,sha256=NYGQbIKES93vjVumCkF6IihydEhrhH7JMaJFmGDfCwM,791
jax/scipy/stats/wrapcauchy.py,sha256=pcs6O0cdJTyhsN6LGTOxBm4TyuKkm3HRIdxfZVoz8r4,793
jax/sharding.py,sha256=PHXvo9WjCtsXIzX5wAdpPCX8031W91yoiZjMto4VJs4,2072
jax/stages.py,sha256=M5yM7y1ubMHqBm6KJor_kJGnJDH8UXVoudgZ15cvaJI,1308
jax/test_util.py,sha256=DVJAPxzZMKdM01Nhx7s2kPe5mcdv8ZL8a_37sgyZPUs,835
jax/tools/__init__.py,sha256=BTyMF0yh0M75AsNwF_0NSd5gHnIVpxhsLznsWkxu3XQ,581
jax/tools/__pycache__/__init__.cpython-312.pyc,,
jax/tools/__pycache__/colab_tpu.cpython-312.pyc,,
jax/tools/__pycache__/jax_to_ir.cpython-312.pyc,,
jax/tools/__pycache__/pgo_nsys_converter.cpython-312.pyc,,
jax/tools/colab_tpu.py,sha256=IPEQ1HZB4xW0MRSPGDeFAw9GoMFaphNQ0kLRIwGypmo,890
jax/tools/jax_to_ir.py,sha256=fFvvhbWBsHJel1cBJJOBhLeacpk-OJVJzRE-hZHxK90,8711
jax/tools/pgo_nsys_converter.py,sha256=BCxXmHnvcorM62adEbOFNYcsnTyv-Hkqnf4K7lo_wt4,3413
jax/tree.py,sha256=tI9yJ2PIEhAFiSBbVKnxV1Wfly6mb3b3mJXECzugJs0,1078
jax/tree_util.py,sha256=6SWQZx5f47UMoNGhG6dkrgoYdzQIwes97vwhpJju43A,3638
jax/typing.py,sha256=zijMsLWzFOqn-u8__DbGxxI7MyjePxpUwU5nFE_P78Y,3319
jax/util.py,sha256=Ny5dah5vtOF0NJtr2UMJlxSJKl9AvdIX_vsbxo-zbho,4486
jax/version.py,sha256=Gai2vtWe_-A9Pk4DjPCdKNcYZXKFu6bdPIl9CEvrAFQ,6725
