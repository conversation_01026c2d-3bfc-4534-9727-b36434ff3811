# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/formats/annotation/rasterization.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n:mediapipe/framework/formats/annotation/rasterization.proto\x12\tmediapipe\"|\n\rRasterization\x12\x33\n\x08interval\x18\x01 \x03(\x0b\x32!.mediapipe.Rasterization.Interval\x1a\x36\n\x08Interval\x12\t\n\x01y\x18\x01 \x02(\x05\x12\x0e\n\x06left_x\x18\x02 \x02(\x05\x12\x0f\n\x07right_x\x18\x03 \x02(\x05\x42\x46\n-com.google.mediapipe.formats.annotation.protoB\x12RasterizationProto\xf8\x01\x01')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.formats.annotation.rasterization_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n-com.google.mediapipe.formats.annotation.protoB\022RasterizationProto\370\001\001'
  _globals['_RASTERIZATION']._serialized_start=73
  _globals['_RASTERIZATION']._serialized_end=197
  _globals['_RASTERIZATION_INTERVAL']._serialized_start=143
  _globals['_RASTERIZATION_INTERVAL']._serialized_end=197
# @@protoc_insertion_point(module_scope)
