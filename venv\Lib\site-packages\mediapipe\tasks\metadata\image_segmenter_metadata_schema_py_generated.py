import flatbuffers

# automatically generated by the FlatBuffers compiler, do not modify

# namespace: tasks

from flatbuffers.compat import import_numpy
np = import_numpy()

class Activation(object):
    NONE = 0
    SIGMOID = 1
    SOFTMAX = 2


class ImageSegmenterOptions(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = ImageSegmenterOptions()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsImageSegmenterOptions(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def ImageSegmenterOptionsBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x56\x30\x30\x31", size_prefixed=size_prefixed)

    # ImageSegmenterOptions
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # ImageSegmenterOptions
    def Activation(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int8Flags, o + self._tab.Pos)
        return 0

    # ImageSegmenterOptions
    def MinParserVersion(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

def ImageSegmenterOptionsStart(builder):
    builder.StartObject(2)

def ImageSegmenterOptionsAddActivation(builder, activation):
    builder.PrependInt8Slot(0, activation, 0)

def ImageSegmenterOptionsAddMinParserVersion(builder, minParserVersion):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(minParserVersion), 0)

def ImageSegmenterOptionsEnd(builder):
    return builder.EndObject()



class ImageSegmenterOptionsT(object):

    # ImageSegmenterOptionsT
    def __init__(self):
        self.activation = 0  # type: int
        self.minParserVersion = None  # type: str

    @classmethod
    def InitFromBuf(cls, buf, pos):
        imageSegmenterOptions = ImageSegmenterOptions()
        imageSegmenterOptions.Init(buf, pos)
        return cls.InitFromObj(imageSegmenterOptions)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, imageSegmenterOptions):
        x = ImageSegmenterOptionsT()
        x._UnPack(imageSegmenterOptions)
        return x

    # ImageSegmenterOptionsT
    def _UnPack(self, imageSegmenterOptions):
        if imageSegmenterOptions is None:
            return
        self.activation = imageSegmenterOptions.Activation()
        self.minParserVersion = imageSegmenterOptions.MinParserVersion()

    # ImageSegmenterOptionsT
    def Pack(self, builder):
        if self.minParserVersion is not None:
            minParserVersion = builder.CreateString(self.minParserVersion)
        ImageSegmenterOptionsStart(builder)
        ImageSegmenterOptionsAddActivation(builder, self.activation)
        if self.minParserVersion is not None:
            ImageSegmenterOptionsAddMinParserVersion(builder, minParserVersion)
        imageSegmenterOptions = ImageSegmenterOptionsEnd(builder)
        return imageSegmenterOptions


