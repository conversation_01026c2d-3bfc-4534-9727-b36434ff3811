# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/genai/inference/calculators/detokenizer_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nKmediapipe/tasks/cc/genai/inference/calculators/detokenizer_calculator.proto\x12\x10odml.infra.proto\"\x8b\x01\n\x1c\x44\x65tokenizerCalculatorOptions\x12\x16\n\x0espm_model_file\x18\x01 \x01(\t\x12\x13\n\x0bstop_tokens\x18\x04 \x03(\t\x12\x18\n\x10num_output_heads\x18\x05 \x01(\x05\x12\x18\n\x10\x66\x61ke_weight_mode\x18\x06 \x01(\x08J\x04\x08\x02\x10\x03J\x04\x08\x03\x10\x04\x42@\n\x1b\x63om.google.odml.infra.protoB!DetokenizerCalculatorOptionsProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.genai.inference.calculators.detokenizer_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\033com.google.odml.infra.protoB!DetokenizerCalculatorOptionsProto'
  _globals['_DETOKENIZERCALCULATOROPTIONS']._serialized_start=98
  _globals['_DETOKENIZERCALCULATOROPTIONS']._serialized_end=237
# @@protoc_insertion_point(module_scope)
