# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/internal/callback_packet_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n?mediapipe/calculators/internal/callback_packet_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\x99\x02\n\x1f\x43\x61llbackPacketCalculatorOptions\x12\x44\n\x04type\x18\x01 \x01(\x0e\x32\x36.mediapipe.CallbackPacketCalculatorOptions.PointerType\x12\x0f\n\x07pointer\x18\x02 \x01(\x0c\"E\n\x0bPointerType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x11\n\rVECTOR_PACKET\x10\x01\x12\x16\n\x12POST_STREAM_PACKET\x10\x02\x32X\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xeb\xc7\xa4u \x01(\x0b\x32*.mediapipe.CallbackPacketCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.internal.callback_packet_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_CALLBACKPACKETCALCULATOROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_CALLBACKPACKETCALCULATOROPTIONS']._serialized_start=117
  _globals['_CALLBACKPACKETCALCULATOROPTIONS']._serialized_end=398
  _globals['_CALLBACKPACKETCALCULATOROPTIONS_POINTERTYPE']._serialized_start=239
  _globals['_CALLBACKPACKETCALCULATOROPTIONS_POINTERTYPE']._serialized_end=308
# @@protoc_insertion_point(module_scope)
