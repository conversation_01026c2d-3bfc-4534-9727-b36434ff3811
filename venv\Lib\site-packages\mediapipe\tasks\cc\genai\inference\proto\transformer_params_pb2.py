# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/genai/inference/proto/transformer_params.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nAmediapipe/tasks/cc/genai/inference/proto/transformer_params.proto\x12\x10odml.infra.proto\"\xcd\r\n\x15TransformerParameters\x12\x12\n\nbatch_size\x18\x01 \x01(\x05\x12\x16\n\x0emax_seq_length\x18\x02 \x01(\x05\x12\x15\n\rembedding_dim\x18\x03 \x01(\x05\x12\x18\n\x10hidden_dimension\x18\x04 \x01(\x05\x12\x16\n\x0ehead_dimension\x18\x05 \x01(\x05\x12\x11\n\tnum_heads\x18\x06 \x01(\x05\x12\x12\n\nnum_stacks\x18\x07 \x01(\x05\x12\x14\n\x0cnum_kv_heads\x18\t \x01(\x05\x12^\n\x17\x66\x65\x65\x64_forward_parameters\x18\x0b \x01(\x0b\x32=.odml.infra.proto.TransformerParameters.FeedForwardParameters\x12`\n\x18\x66inal_project_parameters\x18\x0c \x01(\x0b\x32>.odml.infra.proto.TransformerParameters.FinalProjectParameters\x12>\n\x08pre_norm\x18\r \x01(\x0e\x32,.odml.infra.proto.TransformerParameters.Norm\x12?\n\tpost_norm\x18\x0e \x01(\x0e\x32,.odml.infra.proto.TransformerParameters.Norm\x12@\n\nfinal_norm\x18\x0f \x01(\x0e\x32,.odml.infra.proto.TransformerParameters.Norm\x12\x62\n\x19self_attention_parameters\x18\x10 \x01(\x0b\x32?.odml.infra.proto.TransformerParameters.SelfAttentionParameters\x12+\n#skip_absolute_positional_embeddings\x18\x12 \x01(\x08\x1a\xf1\x01\n\x15\x46\x65\x65\x64\x46orwardParameters\x12\x0f\n\x07no_bias\x18\x01 \x01(\x08\x12\x46\n\nactivation\x18\x02 \x01(\x0e\x32\x32.odml.infra.proto.TransformerParameters.Activation\x12>\n\x08pre_norm\x18\x03 \x01(\x0e\x32,.odml.infra.proto.TransformerParameters.Norm\x12?\n\tpost_norm\x18\x04 \x01(\x0e\x32,.odml.infra.proto.TransformerParameters.Norm\x1a\x41\n\x16\x46inalProjectParameters\x12\x0f\n\x07no_bias\x18\x01 \x01(\x08\x12\x16\n\x0esoft_cap_value\x18\x02 \x01(\x02\x1a\xb1\x02\n\x17SelfAttentionParameters\x12\x13\n\x0bqkv_no_bias\x18\x01 \x01(\x08\x12\x19\n\x11post_proj_no_bias\x18\x02 \x01(\x08\x12V\n\x13\x61ttention_mask_type\x18\x03 \x01(\x0e\x32\x39.odml.infra.proto.TransformerParameters.AttentionMaskType\x12\x16\n\x0esoft_cap_value\x18\x04 \x01(\x02\x12]\n\x14\x61ttention_scale_type\x18\x05 \x01(\x0e\x32:.odml.infra.proto.TransformerParameters.AttentionScaleTypeH\x00\x88\x01\x01\x42\x17\n\x15_attention_scale_type\"<\n\x11\x41ttentionMaskType\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\n\n\x06\x43\x41USAL\x10\x01\x12\n\n\x06PREFIX\x10\x02\"F\n\nActivation\x12\x1a\n\x16\x41\x43TIVATION_UNSPECIFIED\x10\x00\x12\x08\n\x04GELU\x10\x01\x12\x08\n\x04SILU\x10\x02\x12\x08\n\x04RELU\x10\x03\"G\n\x04Norm\x12\x14\n\x10NORM_UNSPECIFIED\x10\x00\x12\x0b\n\x07NO_NORM\x10\x01\x12\x0c\n\x08RMS_NORM\x10\x02\x12\x0e\n\nLAYER_NORM\x10\x03\"\x9f\x01\n\x12\x41ttentionScaleType\x12\x1a\n\x16SCALE_TYPE_UNSPECIFIED\x10\x00\x12\x1c\n\x18SCALE_TYPE_PER_DIM_SCALE\x10\x01\x12 \n\x1cSCALE_TYPE_INV_SQRT_HEAD_DIM\x10\x02\x12-\n)SCALE_TYPE_INV_SQRT_D_MODEL_DIV_NUM_HEADS\x10\x03J\x04\x08\x08\x10\tJ\x04\x08\n\x10\x0bJ\x04\x08\x11\x10\x12\x42\x39\n\x1b\x63om.google.odml.infra.protoB\x1aTransformerParametersProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.genai.inference.proto.transformer_params_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\033com.google.odml.infra.protoB\032TransformerParametersProto'
  _globals['_TRANSFORMERPARAMETERS']._serialized_start=88
  _globals['_TRANSFORMERPARAMETERS']._serialized_end=1829
  _globals['_TRANSFORMERPARAMETERS_FEEDFORWARDPARAMETERS']._serialized_start=826
  _globals['_TRANSFORMERPARAMETERS_FEEDFORWARDPARAMETERS']._serialized_end=1067
  _globals['_TRANSFORMERPARAMETERS_FINALPROJECTPARAMETERS']._serialized_start=1069
  _globals['_TRANSFORMERPARAMETERS_FINALPROJECTPARAMETERS']._serialized_end=1134
  _globals['_TRANSFORMERPARAMETERS_SELFATTENTIONPARAMETERS']._serialized_start=1137
  _globals['_TRANSFORMERPARAMETERS_SELFATTENTIONPARAMETERS']._serialized_end=1442
  _globals['_TRANSFORMERPARAMETERS_ATTENTIONMASKTYPE']._serialized_start=1444
  _globals['_TRANSFORMERPARAMETERS_ATTENTIONMASKTYPE']._serialized_end=1504
  _globals['_TRANSFORMERPARAMETERS_ACTIVATION']._serialized_start=1506
  _globals['_TRANSFORMERPARAMETERS_ACTIVATION']._serialized_end=1576
  _globals['_TRANSFORMERPARAMETERS_NORM']._serialized_start=1578
  _globals['_TRANSFORMERPARAMETERS_NORM']._serialized_end=1649
  _globals['_TRANSFORMERPARAMETERS_ATTENTIONSCALETYPE']._serialized_start=1652
  _globals['_TRANSFORMERPARAMETERS_ATTENTIONSCALETYPE']._serialized_end=1811
# @@protoc_insertion_point(module_scope)
