# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/components/containers/proto/embeddings.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n?mediapipe/tasks/cc/components/containers/proto/embeddings.proto\x12+mediapipe.tasks.components.containers.proto\"$\n\x0e\x46loatEmbedding\x12\x12\n\x06values\x18\x01 \x03(\x02\x42\x02\x10\x01\"$\n\x12QuantizedEmbedding\x12\x0e\n\x06values\x18\x01 \x01(\x0c\"\xf7\x01\n\tEmbedding\x12V\n\x0f\x66loat_embedding\x18\x01 \x01(\x0b\x32;.mediapipe.tasks.components.containers.proto.FloatEmbeddingH\x00\x12^\n\x13quantized_embedding\x18\x02 \x01(\x0b\x32?.mediapipe.tasks.components.containers.proto.QuantizedEmbeddingH\x00\x12\x12\n\nhead_index\x18\x03 \x01(\x05\x12\x11\n\thead_name\x18\x04 \x01(\tB\x0b\n\tembedding\"s\n\x0f\x45mbeddingResult\x12J\n\nembeddings\x18\x01 \x03(\x0b\x32\x36.mediapipe.tasks.components.containers.proto.Embedding\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x42I\n6com.google.mediapipe.tasks.components.containers.protoB\x0f\x45mbeddingsProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.components.containers.proto.embeddings_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n6com.google.mediapipe.tasks.components.containers.protoB\017EmbeddingsProto'
  _FLOATEMBEDDING.fields_by_name['values']._options = None
  _FLOATEMBEDDING.fields_by_name['values']._serialized_options = b'\020\001'
  _globals['_FLOATEMBEDDING']._serialized_start=112
  _globals['_FLOATEMBEDDING']._serialized_end=148
  _globals['_QUANTIZEDEMBEDDING']._serialized_start=150
  _globals['_QUANTIZEDEMBEDDING']._serialized_end=186
  _globals['_EMBEDDING']._serialized_start=189
  _globals['_EMBEDDING']._serialized_end=436
  _globals['_EMBEDDINGRESULT']._serialized_start=438
  _globals['_EMBEDDINGRESULT']._serialized_end=553
# @@protoc_insertion_point(module_scope)
