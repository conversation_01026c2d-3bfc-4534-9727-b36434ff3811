"""
Feature Extractor for GestureFlow application.
Extracts meaningful features from hand landmarks for gesture recognition.
Used in custom gesture training and recognition.
"""

import numpy as np
import math
from typing import List, Tuple, Dict, Any
from src.core.hand_tracker import HandLandmarks
from src.utils.logger import logger


class HandFeatureExtractor:
    """
    Extracts features from hand landmarks for gesture recognition.
    Provides various feature extraction methods for ML training.
    """
    
    def __init__(self):
        """Initialize feature extractor."""
        # MediaPipe hand landmark indices
        self.LANDMARK_INDICES = {
            'WRIST': 0,
            'THUMB_CMC': 1, 'THUMB_MCP': 2, 'THUMB_IP': 3, 'THUMB_TIP': 4,
            'INDEX_MCP': 5, 'INDEX_PIP': 6, 'INDEX_DIP': 7, 'INDEX_TIP': 8,
            'MIDDLE_MCP': 9, 'MIDDLE_PIP': 10, 'MIDDLE_DIP': 11, 'MIDDLE_TIP': 12,
            'RING_MCP': 13, 'RING_PIP': 14, 'RING_DIP': 15, 'RING_TIP': 16,
            'PINKY_MCP': 17, 'PINKY_PIP': 18, 'PINKY_DIP': 19, 'PINKY_TIP': 20
        }
        
        # Finger tip and MCP pairs for finger extension calculation
        self.FINGER_PAIRS = [
            ('THUMB_TIP', 'THUMB_MCP'),
            ('INDEX_TIP', 'INDEX_MCP'),
            ('MIDDLE_TIP', 'MIDDLE_MCP'),
            ('RING_TIP', 'RING_MCP'),
            ('PINKY_TIP', 'PINKY_MCP')
        ]
        
        logger.info("HandFeatureExtractor initialized")
    
    def extract_features(self, hand_landmarks: HandLandmarks) -> List[float]:
        """
        Extract comprehensive features from hand landmarks.
        
        Args:
            hand_landmarks: Hand landmarks data
            
        Returns:
            Feature vector as list of floats
        """
        try:
            landmarks = hand_landmarks.landmarks
            features = []
            
            # 1. Normalized landmark positions (relative to wrist)
            features.extend(self._extract_normalized_positions(landmarks))
            
            # 2. Finger extension features
            features.extend(self._extract_finger_extensions(landmarks))
            
            # 3. Inter-finger distances
            features.extend(self._extract_finger_distances(landmarks))
            
            # 4. Hand orientation features
            features.extend(self._extract_hand_orientation(landmarks))
            
            # 5. Finger angles
            features.extend(self._extract_finger_angles(landmarks))
            
            # 6. Palm features
            features.extend(self._extract_palm_features(landmarks))
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return []
    
    def _extract_normalized_positions(self, landmarks: List[Tuple[float, float, float]]) -> List[float]:
        """Extract normalized landmark positions relative to wrist."""
        features = []
        wrist = landmarks[self.LANDMARK_INDICES['WRIST']]
        
        # Normalize all landmarks relative to wrist position
        for landmark in landmarks:
            # Relative position
            rel_x = landmark[0] - wrist[0]
            rel_y = landmark[1] - wrist[1]
            rel_z = landmark[2] - wrist[2]
            
            features.extend([rel_x, rel_y, rel_z])
        
        return features
    
    def _extract_finger_extensions(self, landmarks: List[Tuple[float, float, float]]) -> List[float]:
        """Extract finger extension features."""
        features = []
        wrist = landmarks[self.LANDMARK_INDICES['WRIST']]
        
        for tip_name, mcp_name in self.FINGER_PAIRS:
            tip = landmarks[self.LANDMARK_INDICES[tip_name]]
            mcp = landmarks[self.LANDMARK_INDICES[mcp_name]]
            
            # Distance from wrist to tip vs distance from wrist to MCP
            tip_dist = self._distance_3d(tip, wrist)
            mcp_dist = self._distance_3d(mcp, wrist)
            
            # Extension ratio
            extension_ratio = tip_dist / mcp_dist if mcp_dist > 0 else 0
            features.append(extension_ratio)
            
            # Vertical extension (y-axis)
            vertical_extension = tip[1] - mcp[1]
            features.append(vertical_extension)
        
        return features
    
    def _extract_finger_distances(self, landmarks: List[Tuple[float, float, float]]) -> List[float]:
        """Extract distances between finger tips."""
        features = []
        
        finger_tips = [
            landmarks[self.LANDMARK_INDICES['THUMB_TIP']],
            landmarks[self.LANDMARK_INDICES['INDEX_TIP']],
            landmarks[self.LANDMARK_INDICES['MIDDLE_TIP']],
            landmarks[self.LANDMARK_INDICES['RING_TIP']],
            landmarks[self.LANDMARK_INDICES['PINKY_TIP']]
        ]
        
        # Calculate distances between all pairs of finger tips
        for i in range(len(finger_tips)):
            for j in range(i + 1, len(finger_tips)):
                distance = self._distance_3d(finger_tips[i], finger_tips[j])
                features.append(distance)
        
        return features
    
    def _extract_hand_orientation(self, landmarks: List[Tuple[float, float, float]]) -> List[float]:
        """Extract hand orientation features."""
        features = []
        
        # Use wrist, middle MCP, and middle tip to determine hand orientation
        wrist = landmarks[self.LANDMARK_INDICES['WRIST']]
        middle_mcp = landmarks[self.LANDMARK_INDICES['MIDDLE_MCP']]
        middle_tip = landmarks[self.LANDMARK_INDICES['MIDDLE_TIP']]
        
        # Vector from wrist to middle MCP (palm direction)
        palm_vector = (
            middle_mcp[0] - wrist[0],
            middle_mcp[1] - wrist[1],
            middle_mcp[2] - wrist[2]
        )
        
        # Vector from middle MCP to tip (finger direction)
        finger_vector = (
            middle_tip[0] - middle_mcp[0],
            middle_tip[1] - middle_mcp[1],
            middle_tip[2] - middle_mcp[2]
        )
        
        # Normalize vectors
        palm_length = math.sqrt(sum(x**2 for x in palm_vector))
        finger_length = math.sqrt(sum(x**2 for x in finger_vector))
        
        if palm_length > 0:
            palm_unit = [x / palm_length for x in palm_vector]
            features.extend(palm_unit)
        else:
            features.extend([0, 0, 0])
        
        if finger_length > 0:
            finger_unit = [x / finger_length for x in finger_vector]
            features.extend(finger_unit)
        else:
            features.extend([0, 0, 0])
        
        # Angle between palm and finger vectors
        if palm_length > 0 and finger_length > 0:
            dot_product = sum(p * f for p, f in zip(palm_unit, finger_unit))
            angle = math.acos(max(-1, min(1, dot_product)))  # Clamp to avoid numerical errors
            features.append(angle)
        else:
            features.append(0)
        
        return features
    
    def _extract_finger_angles(self, landmarks: List[Tuple[float, float, float]]) -> List[float]:
        """Extract angles at finger joints."""
        features = []
        
        # Define finger joint triplets for angle calculation
        finger_joints = [
            # Thumb
            ('THUMB_MCP', 'THUMB_IP', 'THUMB_TIP'),
            # Index
            ('INDEX_MCP', 'INDEX_PIP', 'INDEX_DIP'),
            ('INDEX_PIP', 'INDEX_DIP', 'INDEX_TIP'),
            # Middle
            ('MIDDLE_MCP', 'MIDDLE_PIP', 'MIDDLE_DIP'),
            ('MIDDLE_PIP', 'MIDDLE_DIP', 'MIDDLE_TIP'),
            # Ring
            ('RING_MCP', 'RING_PIP', 'RING_DIP'),
            ('RING_PIP', 'RING_DIP', 'RING_TIP'),
            # Pinky
            ('PINKY_MCP', 'PINKY_PIP', 'PINKY_DIP'),
            ('PINKY_PIP', 'PINKY_DIP', 'PINKY_TIP')
        ]
        
        for joint1, joint2, joint3 in finger_joints:
            p1 = landmarks[self.LANDMARK_INDICES[joint1]]
            p2 = landmarks[self.LANDMARK_INDICES[joint2]]
            p3 = landmarks[self.LANDMARK_INDICES[joint3]]
            
            angle = self._calculate_angle(p1, p2, p3)
            features.append(angle)
        
        return features
    
    def _extract_palm_features(self, landmarks: List[Tuple[float, float, float]]) -> List[float]:
        """Extract palm-related features."""
        features = []
        
        # Palm landmarks (MCPs)
        palm_landmarks = [
            landmarks[self.LANDMARK_INDICES['WRIST']],
            landmarks[self.LANDMARK_INDICES['THUMB_CMC']],
            landmarks[self.LANDMARK_INDICES['INDEX_MCP']],
            landmarks[self.LANDMARK_INDICES['MIDDLE_MCP']],
            landmarks[self.LANDMARK_INDICES['RING_MCP']],
            landmarks[self.LANDMARK_INDICES['PINKY_MCP']]
        ]
        
        # Calculate palm center
        palm_center = (
            sum(p[0] for p in palm_landmarks) / len(palm_landmarks),
            sum(p[1] for p in palm_landmarks) / len(palm_landmarks),
            sum(p[2] for p in palm_landmarks) / len(palm_landmarks)
        )
        
        # Distances from palm center to each MCP
        for landmark in palm_landmarks[1:]:  # Skip wrist
            distance = self._distance_3d(landmark, palm_center)
            features.append(distance)
        
        # Palm area approximation (using convex hull area of MCP points)
        # Simplified: use bounding box area
        x_coords = [p[0] for p in palm_landmarks]
        y_coords = [p[1] for p in palm_landmarks]
        
        palm_width = max(x_coords) - min(x_coords)
        palm_height = max(y_coords) - min(y_coords)
        palm_area = palm_width * palm_height
        
        features.extend([palm_width, palm_height, palm_area])
        
        return features
    
    def _distance_3d(self, point1: Tuple[float, float, float], point2: Tuple[float, float, float]) -> float:
        """Calculate 3D Euclidean distance between two points."""
        return math.sqrt(
            (point1[0] - point2[0])**2 +
            (point1[1] - point2[1])**2 +
            (point1[2] - point2[2])**2
        )
    
    def _calculate_angle(self, p1: Tuple[float, float, float], 
                        p2: Tuple[float, float, float], 
                        p3: Tuple[float, float, float]) -> float:
        """Calculate angle at point p2 formed by points p1-p2-p3."""
        # Vectors from p2 to p1 and p2 to p3
        v1 = (p1[0] - p2[0], p1[1] - p2[1], p1[2] - p2[2])
        v2 = (p3[0] - p2[0], p3[1] - p2[1], p3[2] - p2[2])
        
        # Calculate magnitudes
        mag1 = math.sqrt(sum(x**2 for x in v1))
        mag2 = math.sqrt(sum(x**2 for x in v2))
        
        if mag1 == 0 or mag2 == 0:
            return 0
        
        # Calculate dot product
        dot_product = sum(a * b for a, b in zip(v1, v2))
        
        # Calculate angle
        cos_angle = dot_product / (mag1 * mag2)
        cos_angle = max(-1, min(1, cos_angle))  # Clamp to avoid numerical errors
        
        return math.acos(cos_angle)
    
    def get_feature_names(self) -> List[str]:
        """Get names of all extracted features for debugging/analysis."""
        feature_names = []
        
        # Normalized positions (21 landmarks * 3 coordinates)
        for i in range(21):
            feature_names.extend([f'landmark_{i}_rel_x', f'landmark_{i}_rel_y', f'landmark_{i}_rel_z'])
        
        # Finger extensions (5 fingers * 2 features)
        fingers = ['thumb', 'index', 'middle', 'ring', 'pinky']
        for finger in fingers:
            feature_names.extend([f'{finger}_extension_ratio', f'{finger}_vertical_extension'])
        
        # Finger distances (10 pairs)
        for i, finger1 in enumerate(fingers):
            for finger2 in fingers[i+1:]:
                feature_names.append(f'distance_{finger1}_{finger2}')
        
        # Hand orientation (7 features)
        feature_names.extend(['palm_vector_x', 'palm_vector_y', 'palm_vector_z',
                             'finger_vector_x', 'finger_vector_y', 'finger_vector_z',
                             'palm_finger_angle'])
        
        # Finger angles (9 angles)
        joint_names = ['thumb_ip', 'index_pip', 'index_dip', 'middle_pip', 'middle_dip',
                      'ring_pip', 'ring_dip', 'pinky_pip', 'pinky_dip']
        for joint in joint_names:
            feature_names.append(f'{joint}_angle')
        
        # Palm features (8 features)
        feature_names.extend(['thumb_cmc_palm_dist', 'index_mcp_palm_dist', 'middle_mcp_palm_dist',
                             'ring_mcp_palm_dist', 'pinky_mcp_palm_dist',
                             'palm_width', 'palm_height', 'palm_area'])
        
        return feature_names
    
    def extract_features_from_landmarks_list(self, landmarks_list: List[List[Tuple[float, float, float]]]) -> List[List[float]]:
        """
        Extract features from multiple landmark samples.
        
        Args:
            landmarks_list: List of landmark sequences
            
        Returns:
            List of feature vectors
        """
        feature_vectors = []
        
        for landmarks in landmarks_list:
            # Create temporary HandLandmarks object
            hand_landmarks = HandLandmarks(
                landmarks=landmarks,
                handedness="Right",  # Doesn't matter for feature extraction
                confidence=1.0
            )
            
            features = self.extract_features(hand_landmarks)
            if features:
                feature_vectors.append(features)
        
        return feature_vectors
