# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/components/processors/proto/embedder_options.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nEmediapipe/tasks/cc/components/processors/proto/embedder_options.proto\x12+mediapipe.tasks.components.processors.proto\"9\n\x0f\x45mbedderOptions\x12\x14\n\x0cl2_normalize\x18\x01 \x01(\x08\x12\x10\n\x08quantize\x18\x02 \x01(\x08\x42N\n6com.google.mediapipe.tasks.components.processors.protoB\x14\x45mbedderOptionsProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.components.processors.proto.embedder_options_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n6com.google.mediapipe.tasks.components.processors.protoB\024EmbedderOptionsProto'
  _globals['_EMBEDDEROPTIONS']._serialized_start=118
  _globals['_EMBEDDEROPTIONS']._serialized_end=175
# @@protoc_insertion_point(module_scope)
