# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/core/proto/model_resources_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2
from mediapipe.tasks.cc.core.proto import external_file_pb2 as mediapipe_dot_tasks_dot_cc_dot_core_dot_proto_dot_external__file__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n>mediapipe/tasks/cc/core/proto/model_resources_calculator.proto\x12\x1amediapipe.tasks.core.proto\x1a$mediapipe/framework/calculator.proto\x1a\x31mediapipe/tasks/cc/core/proto/external_file.proto\"\xe8\x01\n\x1fModelResourcesCalculatorOptions\x12\x1b\n\x13model_resources_tag\x18\x01 \x01(\t\x12<\n\nmodel_file\x18\x02 \x01(\x0b\x32(.mediapipe.tasks.core.proto.ExternalFile2j\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xb0\xce\xf3\xd1\x01 \x01(\x0b\x32;.mediapipe.tasks.core.proto.ModelResourcesCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.core.proto.model_resources_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_MODELRESOURCESCALCULATOROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_MODELRESOURCESCALCULATOROPTIONS']._serialized_start=184
  _globals['_MODELRESOURCESCALCULATOROPTIONS']._serialized_end=416
# @@protoc_insertion_point(module_scope)
