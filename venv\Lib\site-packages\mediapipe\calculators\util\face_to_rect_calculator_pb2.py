# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/util/face_to_rect_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8mediapipe/calculators/util/face_to_rect_calculator.proto\x12\tmediapipe\"\xc1\x01\n\x1b\x46\x61\x63\x65ToRectCalculatorOptions\x12\x19\n\x11\x65ye_landmark_size\x18\x01 \x01(\x05\x12\x1a\n\x12nose_landmark_size\x18\x02 \x01(\x05\x12\x1b\n\x13mouth_landmark_size\x18\x03 \x01(\x05\x12\x18\n\x10\x65ye_to_mouth_mix\x18\x04 \x01(\x02\x12\x1a\n\x12\x65ye_to_mouth_scale\x18\x05 \x01(\x02\x12\x18\n\x10\x65ye_to_eye_scale\x18\x06 \x01(\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.util.face_to_rect_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_FACETORECTCALCULATOROPTIONS']._serialized_start=72
  _globals['_FACETORECTCALCULATOROPTIONS']._serialized_end=265
# @@protoc_insertion_point(module_scope)
