# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/util/non_max_suppression_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n?mediapipe/calculators/util/non_max_suppression_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\x94\x05\n\"NonMaxSuppressionCalculatorOptions\x12 \n\x15num_detection_streams\x18\x01 \x01(\x05:\x01\x31\x12\x1e\n\x12max_num_detections\x18\x02 \x01(\x05:\x02-1\x12\x1f\n\x13min_score_threshold\x18\x06 \x01(\x02:\x02-1\x12$\n\x19min_suppression_threshold\x18\x03 \x01(\x02:\x01\x31\x12X\n\x0coverlap_type\x18\x04 \x01(\x0e\x32\x39.mediapipe.NonMaxSuppressionCalculatorOptions.OverlapType:\x07JACCARD\x12\x1f\n\x17return_empty_detections\x18\x05 \x01(\x08\x12V\n\talgorithm\x18\x07 \x01(\x0e\x32:.mediapipe.NonMaxSuppressionCalculatorOptions.NmsAlgorithm:\x07\x44\x45\x46\x41ULT\x12\x1d\n\x0emulticlass_nms\x18\x08 \x01(\x08:\x05\x66\x61lse\"k\n\x0bOverlapType\x12\x1c\n\x18UNSPECIFIED_OVERLAP_TYPE\x10\x00\x12\x0b\n\x07JACCARD\x10\x01\x12\x14\n\x10MODIFIED_JACCARD\x10\x02\x12\x1b\n\x17INTERSECTION_OVER_UNION\x10\x03\")\n\x0cNmsAlgorithm\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x12\x0c\n\x08WEIGHTED\x10\x01\x32[\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xbc\xa8\xb4\x1a \x01(\x0b\x32-.mediapipe.NonMaxSuppressionCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.util.non_max_suppression_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_NONMAXSUPPRESSIONCALCULATOROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_NONMAXSUPPRESSIONCALCULATOROPTIONS']._serialized_start=117
  _globals['_NONMAXSUPPRESSIONCALCULATOROPTIONS']._serialized_end=777
  _globals['_NONMAXSUPPRESSIONCALCULATOROPTIONS_OVERLAPTYPE']._serialized_start=534
  _globals['_NONMAXSUPPRESSIONCALCULATOROPTIONS_OVERLAPTYPE']._serialized_end=641
  _globals['_NONMAXSUPPRESSIONCALCULATOROPTIONS_NMSALGORITHM']._serialized_start=643
  _globals['_NONMAXSUPPRESSIONCALCULATOROPTIONS_NMSALGORITHM']._serialized_end=684
# @@protoc_insertion_point(module_scope)
