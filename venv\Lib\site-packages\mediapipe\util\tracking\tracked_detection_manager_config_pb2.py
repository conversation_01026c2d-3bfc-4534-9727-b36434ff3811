# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/util/tracking/tracked_detection_manager_config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n>mediapipe/util/tracking/tracked_detection_manager_config.proto\x12\tmediapipe\"~\n\x1dTrackedDetectionManagerConfig\x12+\n is_same_detection_max_area_ratio\x18\x01 \x01(\x02:\x01\x33\x12\x30\n#is_same_detection_min_overlap_ratio\x18\x02 \x01(\x02:\x03\x30.5')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.util.tracking.tracked_detection_manager_config_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_TRACKEDDETECTIONMANAGERCONFIG']._serialized_start=77
  _globals['_TRACKEDDETECTIONMANAGERCONFIG']._serialized_end=203
# @@protoc_insertion_point(module_scope)
