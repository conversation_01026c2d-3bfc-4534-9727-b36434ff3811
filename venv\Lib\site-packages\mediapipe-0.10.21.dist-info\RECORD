mediapipe-0.10.21.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
mediapipe-0.10.21.dist-info/LICENSE,sha256=hwfu8FM5h-_FsVXWR2HutuIHk_ULm9Gmja0c9HGdDtg,12331
mediapipe-0.10.21.dist-info/METADATA,sha256=u2FFJLQZnzu3na6bQr3rxDWRrD2ErqJUlpiw66jb6Ws,10119
mediapipe-0.10.21.dist-info/RECORD,,
mediapipe-0.10.21.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe-0.10.21.dist-info/WHEEL,sha256=cRmSBGD-cl98KkuHMNqv9Ac9L9_VqTvcBYwpIvxN0cg,101
mediapipe-0.10.21.dist-info/top_level.txt,sha256=t9ZcpqnQP_6QmpAovAfYHRwq-9WyW6O6RzQiia5tkbA,175
mediapipe/__init__.py,sha256=HJtyH9KVUzWbZ0H4M0Fl5_QHKKi5KbPAsLynVkURSsA,820
mediapipe/__pycache__/__init__.cpython-312.pyc,,
mediapipe/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/__pycache__/__init__.cpython-312.pyc,,
mediapipe/calculators/audio/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/audio/__pycache__/__init__.cpython-312.pyc,,
mediapipe/calculators/audio/__pycache__/mfcc_mel_calculators_pb2.cpython-312.pyc,,
mediapipe/calculators/audio/__pycache__/rational_factor_resample_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/audio/__pycache__/resample_time_series_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/audio/__pycache__/spectrogram_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/audio/__pycache__/stabilized_log_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/audio/__pycache__/time_series_framer_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/audio/mfcc_mel_calculators_pb2.py,sha256=9NAu1iN9dEyol6xdK84KqkgWwMragHv4nBAfvviQ4nk,2611
mediapipe/calculators/audio/rational_factor_resample_calculator_pb2.py,sha256=5tqIfoRIm8gbwnHDzq9z8dlcJMgAmcYPSUbU_LkDp8g,2614
mediapipe/calculators/audio/resample_time_series_calculator_pb2.py,sha256=nSWRmfOzqec9iiCPvJo5iyRprP9sbCeWHYr-7GGPB8E,3685
mediapipe/calculators/audio/spectrogram_calculator_pb2.py,sha256=EuGAcifzxZN0W0P01BvXxLZLBWyDdxSJL0bN04Se8Ho,3524
mediapipe/calculators/audio/stabilized_log_calculator_pb2.py,sha256=qvkCV9PihewrMbtEQGEORZDUmKXEnLtZzCQZvdgsyLU,2059
mediapipe/calculators/audio/time_series_framer_calculator_pb2.py,sha256=1FMdCesvlcRw_7AZ3K7OITUgLJbz-HKiLGbC-bo44Z4,2620
mediapipe/calculators/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/core/__pycache__/__init__.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/bypass_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/clip_vector_size_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/concatenate_vector_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/constant_side_packet_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/dequantize_byte_array_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/flow_limiter_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/gate_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/get_vector_item_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/graph_profile_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/packet_cloner_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/packet_resampler_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/packet_thinner_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/quantize_float_vector_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/sequence_shift_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/__pycache__/split_vector_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/core/bypass_calculator_pb2.py,sha256=C5AJZyZLwKSJl3_WZNe88NPOxZo5ESSRUbQqFbmqpSs,1928
mediapipe/calculators/core/clip_vector_size_calculator_pb2.py,sha256=BxkH1lCgH7lXRPjHMWJEpP4QzIZz44N6c74O0gRPLJg,1958
mediapipe/calculators/core/concatenate_vector_calculator_pb2.py,sha256=Sx9ASVnm5hSFZLGtMMH1WR_50mRMI6DQKJ7ofoQNS_w,1981
mediapipe/calculators/core/constant_side_packet_calculator_pb2.py,sha256=AP10iYgp0IZNpdUTJ2RWCJUkBzQwXlkivg40guKCjio,4018
mediapipe/calculators/core/dequantize_byte_array_calculator_pb2.py,sha256=pIzHx0kJrWzwZFMT1m-zteS8lhlUbh1Ww20TPpRqn48,2041
mediapipe/calculators/core/flow_limiter_calculator_pb2.py,sha256=Da4h29htnr8V88S5Kq0pyElvInGvbQuygj-XjWJXwoI,2228
mediapipe/calculators/core/gate_calculator_pb2.py,sha256=bgEz1JrY3_sr68_tMnkxVDM6Rci0na8_awEuWHHrbV0,2327
mediapipe/calculators/core/get_vector_item_calculator_pb2.py,sha256=lDl8XhxSN4k8TP3AeWEXePGv0FrzMArCFwO7q_VAGB8,1985
mediapipe/calculators/core/graph_profile_calculator_pb2.py,sha256=BglsFzlDunUcp7dbDqcHI0dS7JMUPBQpUA3Md1i3rds,1961
mediapipe/calculators/core/packet_cloner_calculator_pb2.py,sha256=IXRQ9oFnYAvX8_3PEqBMJZ_zjQY982ubRXL9BNXaqCo,2040
mediapipe/calculators/core/packet_resampler_calculator_pb2.py,sha256=ZzrOpBWOm7RJQ3pIbwIdOZNb3IOf0PAGsplpI3osu5M,2883
mediapipe/calculators/core/packet_thinner_calculator_pb2.py,sha256=rhSa7rxBhMzRLmqtcwyDtjqGxUM-QvLxqvjpLftBc8s,2491
mediapipe/calculators/core/quantize_float_vector_calculator_pb2.py,sha256=FKSIFEntEGon6D-X-Ycnqdeac6r6aD_SaumsACM-QJs,2034
mediapipe/calculators/core/sequence_shift_calculator_pb2.py,sha256=LcpYC_pnzZltHFyFVzd9Omy_Rs9GxMLjyQC1SLU-iW0,2017
mediapipe/calculators/core/split_vector_calculator_pb2.py,sha256=6Qg0dA2fI5lKQiVcyCF_GMU-pHRFSFI0JYGqGpsdSFw,2231
mediapipe/calculators/image/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/image/__pycache__/__init__.cpython-312.pyc,,
mediapipe/calculators/image/__pycache__/bilateral_filter_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/image/__pycache__/feature_detector_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/image/__pycache__/image_clone_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/image/__pycache__/image_cropping_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/image/__pycache__/image_transformation_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/image/__pycache__/mask_overlay_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/image/__pycache__/opencv_encoded_image_to_image_frame_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/image/__pycache__/opencv_image_encoder_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/image/__pycache__/recolor_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/image/__pycache__/rotation_mode_pb2.cpython-312.pyc,,
mediapipe/calculators/image/__pycache__/scale_image_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/image/__pycache__/segmentation_smoothing_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/image/__pycache__/set_alpha_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/image/__pycache__/warp_affine_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/image/bilateral_filter_calculator_pb2.py,sha256=yMVHqd7CoTeCLcH70-GAQK9e59OfxBiir2WoV8N8K-c,1986
mediapipe/calculators/image/feature_detector_calculator_pb2.py,sha256=d_O7dxWpqWKrRsmdOHIfFYQihgfKPtONwp4UIvS0mtM,2116
mediapipe/calculators/image/image_clone_calculator_pb2.py,sha256=pRDwEmaORVeyv9C75ERbEwAcM8_m3nCBGphKM56tiJc,1926
mediapipe/calculators/image/image_cropping_calculator_pb2.py,sha256=XnNQOJdIs-9Z9tABQBjf2LijbrPVov8fduvv9rsFWCA,2704
mediapipe/calculators/image/image_transformation_calculator_pb2.py,sha256=Vb7twZndLeViRZUVPW58MpGfkjpkgrX21sjUMFGbyn4,3716
mediapipe/calculators/image/mask_overlay_calculator_pb2.py,sha256=02pSZo0f4px2fXLzDFfibEcF7knfz0JKKiKjFrxt_20,2236
mediapipe/calculators/image/opencv_encoded_image_to_image_frame_calculator_pb2.py,sha256=Hr4flhH_pBSmRTKXsVxshWjDH4iHr6s5q9FfcuWzpkc,2110
mediapipe/calculators/image/opencv_image_encoder_calculator_pb2.py,sha256=rumDpefWmTSRfKLPYxcW7nh3VWmyxjH6wwA2FOgtFjI,2641
mediapipe/calculators/image/recolor_calculator_pb2.py,sha256=W67qgxionjl0It8FF6ovnHbijEPnsFO4-8S3CodSqfw,2481
mediapipe/calculators/image/rotation_mode_pb2.py,sha256=4Jo8IEem7R4CeAzAOn7uGJ9u_Ze5B6f5k_QA4JmT1kI,1500
mediapipe/calculators/image/scale_image_calculator_pb2.py,sha256=X760xRJtusIYG0GIStmSENkP2IejOmljp5DDilroH_4,3401
mediapipe/calculators/image/segmentation_smoothing_calculator_pb2.py,sha256=k6QNYrLTm5xij1uroDS030DsVeupOKUcl-v_gEwF3Tw,2026
mediapipe/calculators/image/set_alpha_calculator_pb2.py,sha256=L2uGmFxlfIEUTdyFBtopY_XBlgczt7Zu2e-jxfLmB5U,1897
mediapipe/calculators/image/warp_affine_calculator_pb2.py,sha256=McIlGy2j8thLA6y20JwpZZIHt6H6sO2pMSdNek401bI,2833
mediapipe/calculators/internal/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/internal/__pycache__/__init__.cpython-312.pyc,,
mediapipe/calculators/internal/__pycache__/callback_packet_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/internal/callback_packet_calculator_pb2.py,sha256=2X69B7dkur4OMTIi_llyk7lYi_fPTBaVCJRef_wbQL8,2329
mediapipe/calculators/tensor/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/tensor/__pycache__/__init__.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/audio_to_tensor_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/bert_preprocessor_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/feedback_tensors_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/image_to_tensor_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/inference_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/landmarks_to_tensor_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/regex_preprocessor_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/tensor_converter_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/tensor_to_joints_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/tensors_readback_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/tensors_to_audio_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/tensors_to_classification_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/tensors_to_detections_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/tensors_to_floats_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/tensors_to_landmarks_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/tensors_to_segmentation_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/__pycache__/vector_to_tensor_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tensor/audio_to_tensor_calculator_pb2.py,sha256=iPZKub7RC8q17UE8sISnRCY0EF_cN67Dy0eg2QoF52Y,3391
mediapipe/calculators/tensor/bert_preprocessor_calculator_pb2.py,sha256=zna4M1qHF27kMLpoI826szjwTLWipYw1utp9BLtRuuI,2026
mediapipe/calculators/tensor/feedback_tensors_calculator_pb2.py,sha256=6chhYfWtfS6FA0kT6WvsD8yRUUz8eJTk4I5NmcGmwoc,2992
mediapipe/calculators/tensor/image_to_tensor_calculator_pb2.py,sha256=vwJaH8r4l1Y-KnsosP8MQrx0W_b9zD1jZQcDk8vWz_A,3734
mediapipe/calculators/tensor/inference_calculator_pb2.py,sha256=TGjRIgrDrOriK4QqE1yaUx5WtQAEAsMOtlM_Pngq8gc,8270
mediapipe/calculators/tensor/landmarks_to_tensor_calculator_pb2.py,sha256=EJB1liZDdoyDd2bnnZmYDvh_4JUrXrtYevgtGds3Hko,2396
mediapipe/calculators/tensor/regex_preprocessor_calculator_pb2.py,sha256=fD2v4xraKDJTRfnJ_0FB-bnZ-ajV3JtA8tU4BKuCWN8,1971
mediapipe/calculators/tensor/tensor_converter_calculator_pb2.py,sha256=mkSnjYxmTEg-6I6db6R9nsIKy5VWcGpnNzmkMIhARLM,2954
mediapipe/calculators/tensor/tensor_to_joints_calculator_pb2.py,sha256=fzCPSUcq1nxQFYe9OoE-_dKZ-sgtqhMeRagnxqOkKoc,2000
mediapipe/calculators/tensor/tensors_readback_calculator_pb2.py,sha256=uLjMFjgrCaEFyefMOzNSetmw1oiPPvrlVbcX00AMMfA,2448
mediapipe/calculators/tensor/tensors_to_audio_calculator_pb2.py,sha256=ygwyV7FGUCosWclpR4UHwuVsKPWUkZfyW1dkfSe6DZs,2610
mediapipe/calculators/tensor/tensors_to_classification_calculator_pb2.py,sha256=gnKjKOO7hKC1wQAi86TXowhCUSAKI8ObxQffqqakOME,4156
mediapipe/calculators/tensor/tensors_to_detections_calculator_pb2.py,sha256=1QUM9SVLELtFBrOpTldvCQome51WVeWixpXFlVVSCG0,4829
mediapipe/calculators/tensor/tensors_to_floats_calculator_pb2.py,sha256=UkHSEfTYKauTvcA_YovQp3CXhp3K7IS6V82CbOkSYCQ,2253
mediapipe/calculators/tensor/tensors_to_landmarks_calculator_pb2.py,sha256=oxp8oCN35YbGzZID7Abmwjilly448gLaBEh2sYT_dF0,2747
mediapipe/calculators/tensor/tensors_to_segmentation_calculator_pb2.py,sha256=07RYr48A0W7CAGlMnLR04iij9FqjKdhgCf5TF3ROVuY,2598
mediapipe/calculators/tensor/vector_to_tensor_calculator_pb2.py,sha256=h76sBJShVpKuCZiQHxGZ9Tb1xwv1d_EjytSnONXDnTA,1411
mediapipe/calculators/tflite/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/tflite/__pycache__/__init__.cpython-312.pyc,,
mediapipe/calculators/tflite/__pycache__/ssd_anchors_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tflite/__pycache__/tflite_converter_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tflite/__pycache__/tflite_custom_op_resolver_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tflite/__pycache__/tflite_inference_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tflite/__pycache__/tflite_tensors_to_classification_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tflite/__pycache__/tflite_tensors_to_detections_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tflite/__pycache__/tflite_tensors_to_landmarks_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tflite/__pycache__/tflite_tensors_to_segmentation_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/tflite/ssd_anchors_calculator_pb2.py,sha256=VNqzRdajceZ_ewk_s7PCSRL4WMWYT3LFD325hUQY71w,3217
mediapipe/calculators/tflite/tflite_converter_calculator_pb2.py,sha256=4Aqu6Iy2JJUX4UrN9SKni5oCZz8BaajriZv5c52ATdU,2758
mediapipe/calculators/tflite/tflite_custom_op_resolver_calculator_pb2.py,sha256=KFmbeFg823g8D-w6ks-hBQKpsCjv1WFtf06uBR1ISFk,2021
mediapipe/calculators/tflite/tflite_inference_calculator_pb2.py,sha256=0d0QGHH45rxVvXymk4m8Ibl8yUCBT3eAsPU-jxpnQeQ,5169
mediapipe/calculators/tflite/tflite_tensors_to_classification_calculator_pb2.py,sha256=B0eGQPdLbYxWSvbD5Bbf_Ou2x06PY3iPhpAkPPq3UEs,2213
mediapipe/calculators/tflite/tflite_tensors_to_detections_calculator_pb2.py,sha256=DdUK1kuW9ri531stGD43d-SAPnJxat2QyaIvpYBaKkg,2944
mediapipe/calculators/tflite/tflite_tensors_to_landmarks_calculator_pb2.py,sha256=LN74-9eP28kgw5GVmtwig-G0hIwYV2XCZTvdprBG1nY,2820
mediapipe/calculators/tflite/tflite_tensors_to_segmentation_calculator_pb2.py,sha256=4BmiX2kXpmNpvLamUnDTTanneja98urlWXL_ryqkwDo,2322
mediapipe/calculators/util/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/util/__pycache__/__init__.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/align_hand_to_pose_in_world_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/annotation_overlay_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/association_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/collection_has_min_size_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/combine_joints_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/detection_label_id_to_text_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/detections_to_rects_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/detections_to_render_data_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/face_to_rect_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/filter_detections_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/flat_color_image_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/labels_to_render_data_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/landmark_projection_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/landmarks_refinement_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/landmarks_smoothing_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/landmarks_to_detection_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/landmarks_to_floats_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/landmarks_to_render_data_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/landmarks_transformation_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/latency_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/local_file_contents_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/logic_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/non_max_suppression_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/packet_frequency_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/packet_frequency_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/packet_latency_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/rect_to_render_data_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/rect_to_render_scale_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/rect_transformation_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/refine_landmarks_from_heatmap_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/resource_provider_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/set_joints_visibility_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/thresholding_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/timed_box_list_id_to_label_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/timed_box_list_to_render_data_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/top_k_scores_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/visibility_copy_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/__pycache__/visibility_smoothing_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/util/align_hand_to_pose_in_world_calculator_pb2.py,sha256=sVTzeKkrKYxjHMos2FXuCg_43sUUwN8tDDEx_muxhy8,2071
mediapipe/calculators/util/annotation_overlay_calculator_pb2.py,sha256=wMBEIg54JHOQT0SaEaUXwSlrL6qZHM-1MNIO6plI8bo,2429
mediapipe/calculators/util/association_calculator_pb2.py,sha256=62zRdq5nf_4poLkavzmcxew_0YFWETzpfunJpzkauiY,1938
mediapipe/calculators/util/collection_has_min_size_calculator_pb2.py,sha256=VF5YuptEef85i1shTCGh77jl6SpFAxhadGWXNuJzsZc,1995
mediapipe/calculators/util/combine_joints_calculator_pb2.py,sha256=WUvLtTqKSTRQ0UIsrtQSRtUlEev_acR19Pu_EUIxEwI,2688
mediapipe/calculators/util/detection_label_id_to_text_calculator_pb2.py,sha256=XVXazM-MEIzYRdcwR03EFzXmvawyCgFFC3yi3hxGHdY,2809
mediapipe/calculators/util/detections_to_rects_calculator_pb2.py,sha256=xPLJKY2E0S5de-MPMwTuOjPPtdvcghqsNYsts0GIYVs,2644
mediapipe/calculators/util/detections_to_render_data_calculator_pb2.py,sha256=K4thzssquOUvcHWZpRn45gX5ONcUHe-DrUZP3EAZIIc,2664
mediapipe/calculators/util/face_to_rect_calculator_pb2.py,sha256=Cdvw9av8woTbsqJ6cMaWamnivWez7na0f_Us9cuJaNc,1465
mediapipe/calculators/util/filter_detections_calculator_pb2.py,sha256=wRk__q1fEcNWzl4ErhuQ8g3qpdLRTg2vo9ASWI3TN2k,2043
mediapipe/calculators/util/flat_color_image_calculator_pb2.py,sha256=VBiaPUJis0fuLBLDAyr2DJNO0K9OqfVMbGxQvP2PBUU,2161
mediapipe/calculators/util/labels_to_render_data_calculator_pb2.py,sha256=fGkxuVyOlpAu0nvBAlM8XIkfp30VLPT5zno8FJyvsjY,3047
mediapipe/calculators/util/landmark_projection_calculator_pb2.py,sha256=slNVSSUrchIAJ_jA1FOt_OlgP6bQ7jAqcTGJVC1n1dM,1982
mediapipe/calculators/util/landmarks_refinement_calculator_pb2.py,sha256=1Mvl4wMtvHUqTi7N0cQvjDFVEX5qtKGkM5zdRBXf93g,3650
mediapipe/calculators/util/landmarks_smoothing_calculator_pb2.py,sha256=fdUdEatEL8UrhQ8yC5bzQuf6KS9XrdRcdmaz8eetMy0,3177
mediapipe/calculators/util/landmarks_to_detection_calculator_pb2.py,sha256=EWUyREBu47eorLzsdVAz_UOd_G1xLXx1e28SCvQjMKY,1997
mediapipe/calculators/util/landmarks_to_floats_calculator_pb2.py,sha256=KQ6rCRsGFD23o1wecyhTOOn_0SOSIyWkl7-UifOToyI,1980
mediapipe/calculators/util/landmarks_to_render_data_calculator_pb2.py,sha256=Ym_pBsDuk9G-IKRjAo9r3-km1O6vUqZGrCWoTx0OtUk,2936
mediapipe/calculators/util/landmarks_transformation_calculator_pb2.py,sha256=L51AIogWCJfiLIewa_dZDKTWudj42TP6hrV-e-xIxjY,3162
mediapipe/calculators/util/latency_pb2.py,sha256=BWJQxhlue7mD4KaNTYgRkng-iP9LVKXTnCv4yyCzgsw,1433
mediapipe/calculators/util/local_file_contents_calculator_pb2.py,sha256=lHvfSfw02aiMV_YIRqP0KMtRiTu1nRZkHHc7y8I6o8o,1964
mediapipe/calculators/util/logic_calculator_pb2.py,sha256=atYyXk-1XQYGzcIbasjxX9r0he-hEtu3g8evzMxQRxs,2403
mediapipe/calculators/util/non_max_suppression_calculator_pb2.py,sha256=TqqURoL9k9KPEGwH1O0xMmneoOcl5RY76hiOSDX7gEs,3119
mediapipe/calculators/util/packet_frequency_calculator_pb2.py,sha256=4dN3syTW7LJuANul2nm3Fh1bU2f3D8LufdmbO3EEPgw,1983
mediapipe/calculators/util/packet_frequency_pb2.py,sha256=6_7Suw7SStiR7t1F35oHOW1a_sTjZlZoI9p9qhz6JvY,1151
mediapipe/calculators/util/packet_latency_calculator_pb2.py,sha256=uGFCJWQ09OpXC8ClpH87oU94Qm4OvsEOAuna9RMBGF8,2108
mediapipe/calculators/util/rect_to_render_data_calculator_pb2.py,sha256=PY7P_eM6YB1d1XciUtTB5Fuci8PQih-D_xjBeR-GggI,2274
mediapipe/calculators/util/rect_to_render_scale_calculator_pb2.py,sha256=n7ND9BDCevyIcwmeQ3Kwij04DAla2Rugx3KKn5nb5jg,2047
mediapipe/calculators/util/rect_transformation_calculator_pb2.py,sha256=yp-V-luUUr6upe_z7HULKw5i_gSuIPCSSMWcJe6I7II,2271
mediapipe/calculators/util/refine_landmarks_from_heatmap_calculator_pb2.py,sha256=gW4CvA_BCZkK9T44WV6J7IZYt5g44EKKnCKCWSkPh98,2239
mediapipe/calculators/util/resource_provider_calculator_pb2.py,sha256=1DkhY0nqCDYlHZ9TYAmG3FtNkMapS6HSjDVpB2Al_T8,1603
mediapipe/calculators/util/set_joints_visibility_calculator_pb2.py,sha256=sBwnVRm47dDDe5Gu1gTh1NglcJAx64EKvsOQopNB4Ug,3457
mediapipe/calculators/util/thresholding_calculator_pb2.py,sha256=Znhd9grXsVNLF5xXDzQXf3rpoSw9B-AteDP2QwrYMpo,1912
mediapipe/calculators/util/timed_box_list_id_to_label_calculator_pb2.py,sha256=nUueVEgYBYePnTI6aqbRlQbju-kUK6kcM4-sE2hIpXI,2008
mediapipe/calculators/util/timed_box_list_to_render_data_calculator_pb2.py,sha256=t8ahphSIo76ji5EKw9fckRwvc5-oyEcJ84kkn3No59g,2210
mediapipe/calculators/util/top_k_scores_calculator_pb2.py,sha256=-wmjguPtLmneW3NmqEuZzPF8pVWKbeBEIIReI6zuRgM,1985
mediapipe/calculators/util/visibility_copy_calculator_pb2.py,sha256=y_9jxsUv9GT0paQmXlfoO-jfrJw1ax3TgRkT2dsSDUI,1706
mediapipe/calculators/util/visibility_smoothing_calculator_pb2.py,sha256=Rz9VVs8XWhZ--NJZDeG1HY9psX6KGnwhJggdByPoEy4,2311
mediapipe/calculators/video/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/video/__pycache__/__init__.cpython-312.pyc,,
mediapipe/calculators/video/__pycache__/box_detector_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/video/__pycache__/box_tracker_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/video/__pycache__/flow_packager_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/video/__pycache__/flow_to_image_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/video/__pycache__/motion_analysis_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/video/__pycache__/opencv_video_encoder_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/video/__pycache__/tracked_detection_manager_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/video/__pycache__/video_pre_stream_calculator_pb2.cpython-312.pyc,,
mediapipe/calculators/video/box_detector_calculator_pb2.py,sha256=9_V-48URNrH4SYx32E9MVxp8LuCetFq_yolaX8MadKw,2178
mediapipe/calculators/video/box_tracker_calculator_pb2.py,sha256=ptBNxrMfW7TdxAy_7hdDkz6tTN6X5N-asxn_EIFZkF8,2530
mediapipe/calculators/video/flow_packager_calculator_pb2.py,sha256=JjuVVwthbqf6Rtyt-vL-F94NbBCirdlB_3X4u2SuyGs,2286
mediapipe/calculators/video/flow_to_image_calculator_pb2.py,sha256=Mn6NdnxVK51098SpJO3vY76sLbTNhcT1tvYjwY0oX9E,1977
mediapipe/calculators/video/motion_analysis_calculator_pb2.py,sha256=Sjb8Qam9A6VTN1r5JGJqJdA6XfDHivfEQw6yD9jLkDA,4013
mediapipe/calculators/video/opencv_video_encoder_calculator_pb2.py,sha256=w1Njy2t3CoPT1n_8UvEH201GyVl9L0pTYxFj6keN4Sw,2120
mediapipe/calculators/video/tool/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/video/tool/__pycache__/__init__.cpython-312.pyc,,
mediapipe/calculators/video/tool/__pycache__/flow_quantizer_model_pb2.cpython-312.pyc,,
mediapipe/calculators/video/tool/flow_quantizer_model_pb2.py,sha256=3-nnlB9TP1ovHIynFKtSjbaC-LPtnc8RkgAkreXifHw,1184
mediapipe/calculators/video/tracked_detection_manager_calculator_pb2.py,sha256=vXw2px438slCfeD7MmszETOpVa1mTs2xbPaoSbLKK3Q,2298
mediapipe/calculators/video/video_pre_stream_calculator_pb2.py,sha256=SwT3RkelXukqMkKXvqB4AOIklgWQTZ2Rfw1ery_l4cY,2562
mediapipe/examples/__init__.py,sha256=bglKd2k2C7QGT1i-vstURXPJX2Cvq9FO9opr6cVeBp0,571
mediapipe/examples/__pycache__/__init__.cpython-312.pyc,,
mediapipe/examples/desktop/__init__.py,sha256=bglKd2k2C7QGT1i-vstURXPJX2Cvq9FO9opr6cVeBp0,571
mediapipe/examples/desktop/__pycache__/__init__.cpython-312.pyc,,
mediapipe/framework/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/__pycache__/__init__.cpython-312.pyc,,
mediapipe/framework/__pycache__/calculator_options_pb2.cpython-312.pyc,,
mediapipe/framework/__pycache__/calculator_pb2.cpython-312.pyc,,
mediapipe/framework/__pycache__/calculator_profile_pb2.cpython-312.pyc,,
mediapipe/framework/__pycache__/graph_runtime_info_pb2.cpython-312.pyc,,
mediapipe/framework/__pycache__/mediapipe_options_pb2.cpython-312.pyc,,
mediapipe/framework/__pycache__/packet_factory_pb2.cpython-312.pyc,,
mediapipe/framework/__pycache__/packet_generator_pb2.cpython-312.pyc,,
mediapipe/framework/__pycache__/status_handler_pb2.cpython-312.pyc,,
mediapipe/framework/__pycache__/stream_handler_pb2.cpython-312.pyc,,
mediapipe/framework/__pycache__/test_calculators_pb2.cpython-312.pyc,,
mediapipe/framework/__pycache__/thread_pool_executor_pb2.cpython-312.pyc,,
mediapipe/framework/calculator_options_pb2.py,sha256=cxelCERLGNxTlAnQHXZ40dGtU6TAU7aSEw0TuZZr9uU,1495
mediapipe/framework/calculator_pb2.py,sha256=ZMozPLm3m8PHQZNOzYDsO8-q_z2Kr45QUz35wJXXW6g,8244
mediapipe/framework/calculator_profile_pb2.py,sha256=utsdLDlwyguo7YwZcpovc4nFcCr7I90IFearQ6ZGfP0,5589
mediapipe/framework/deps/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/deps/__pycache__/__init__.cpython-312.pyc,,
mediapipe/framework/deps/__pycache__/proto_descriptor_pb2.cpython-312.pyc,,
mediapipe/framework/deps/proto_descriptor_pb2.py,sha256=Z7_SQbqlEJWXFrf75K_XjB0ybOQuHd8fbaMQFggLdN0,2004
mediapipe/framework/formats/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/formats/__pycache__/__init__.cpython-312.pyc,,
mediapipe/framework/formats/__pycache__/affine_transform_data_pb2.cpython-312.pyc,,
mediapipe/framework/formats/__pycache__/body_rig_pb2.cpython-312.pyc,,
mediapipe/framework/formats/__pycache__/classification_pb2.cpython-312.pyc,,
mediapipe/framework/formats/__pycache__/detection_pb2.cpython-312.pyc,,
mediapipe/framework/formats/__pycache__/image_file_properties_pb2.cpython-312.pyc,,
mediapipe/framework/formats/__pycache__/image_format_pb2.cpython-312.pyc,,
mediapipe/framework/formats/__pycache__/landmark_pb2.cpython-312.pyc,,
mediapipe/framework/formats/__pycache__/location_data_pb2.cpython-312.pyc,,
mediapipe/framework/formats/__pycache__/matrix_data_pb2.cpython-312.pyc,,
mediapipe/framework/formats/__pycache__/rect_pb2.cpython-312.pyc,,
mediapipe/framework/formats/__pycache__/time_series_header_pb2.cpython-312.pyc,,
mediapipe/framework/formats/affine_transform_data_pb2.py,sha256=W9RBThOUVvnKOv4qe4O0LeAm8_y8VchooL4kXnVx37c,1529
mediapipe/framework/formats/annotation/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/formats/annotation/__pycache__/__init__.cpython-312.pyc,,
mediapipe/framework/formats/annotation/__pycache__/locus_pb2.cpython-312.pyc,,
mediapipe/framework/formats/annotation/__pycache__/rasterization_pb2.cpython-312.pyc,,
mediapipe/framework/formats/annotation/locus_pb2.py,sha256=vjT9tZUki-0qhG_HaK_cZGVGvjQlcBU9Z0Av0vyNqws,2311
mediapipe/framework/formats/annotation/rasterization_pb2.py,sha256=37xS4SjQNIb2tbCVg68ftGUax4LdJQlRo1hH-9UBUds,1625
mediapipe/framework/formats/body_rig_pb2.py,sha256=Z434hL08MbS3kW4dgy7mjwEHuVV-AMjXSWuDrd_OFH4,1268
mediapipe/framework/formats/classification_pb2.py,sha256=VpxojfekAJqpC1g9NDX86fJnFk2WwubSvXdP5j6Ciow,1860
mediapipe/framework/formats/detection_pb2.py,sha256=t0n3B9WfZj15-GTCz5CJIeaoPCyNdoCCiLOlZed0gyY,2586
mediapipe/framework/formats/image_file_properties_pb2.py,sha256=wxU4NpaG5Jb0l41ydhtmHuAoyX1Utp35N4KUnm_HvNQ,1343
mediapipe/framework/formats/image_format_pb2.py,sha256=8v0uswkphOCNCaOgjVLAyb0BVU3WiW3Xag7kAg7cG1g,1730
mediapipe/framework/formats/landmark_pb2.py,sha256=To93y6Qo731s4YZoVqpLSbzqzjJKLqrI1lhnM88TdiQ,2541
mediapipe/framework/formats/location_data_pb2.py,sha256=PK71Ux8Mo5aXC8vSQfuweHXRQ-UH_fCPQ0xlaI6JKgc,3320
mediapipe/framework/formats/matrix_data_pb2.py,sha256=SGkWD-oLuoLGHDf9bPvStshUqDrrb-F1f-OAGD9vfq4,1758
mediapipe/framework/formats/motion/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/formats/motion/__pycache__/__init__.cpython-312.pyc,,
mediapipe/framework/formats/motion/__pycache__/optical_flow_field_data_pb2.cpython-312.pyc,,
mediapipe/framework/formats/motion/optical_flow_field_data_pb2.py,sha256=zLHZBBhfZFqJIhjz8oIyrIh8EF6S0qKHDgAheMLMi4I,1586
mediapipe/framework/formats/object_detection/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/formats/object_detection/__pycache__/__init__.cpython-312.pyc,,
mediapipe/framework/formats/object_detection/__pycache__/anchor_pb2.cpython-312.pyc,,
mediapipe/framework/formats/object_detection/anchor_pb2.py,sha256=UZCIjDpktV1fLH1TDpCthRR0C27apr4ONDKj5r8OjDg,1209
mediapipe/framework/formats/rect_pb2.py,sha256=PaPRD0o07dhik6Ul9ly1Qs0uqlQkuY-BAeYZfmvBpzw,1752
mediapipe/framework/formats/time_series_header_pb2.py,sha256=RRJR5IlFRtiX88dcakTcOcszzZGg8fgWj_G4mt9ECN0,1646
mediapipe/framework/graph_runtime_info_pb2.py,sha256=v310XuXRjYIK4rcAIelhlpbnCEq1pxygh_kCTKEkD78,2160
mediapipe/framework/mediapipe_options_pb2.py,sha256=enTGbK4rB4tIG-KT0kp3lbsiCYnwfVC1nCI2fTLLKtE,1263
mediapipe/framework/packet_factory_pb2.py,sha256=Ag6hjHW7G0zcPVQhOJtxM9cEecL59T6ASeBGkEZX_YQ,1833
mediapipe/framework/packet_generator_pb2.py,sha256=LnDRduUpDE5wpBIEeP-TtFMzEPFoBikW2QtpSlk2Xyo,2029
mediapipe/framework/status_handler_pb2.py,sha256=cZbF_oiVxSYfMZzZopD0RO5KEXLxqp6B5X8wkPSouhI,1584
mediapipe/framework/stream_handler/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/stream_handler/__pycache__/__init__.cpython-312.pyc,,
mediapipe/framework/stream_handler/__pycache__/default_input_stream_handler_pb2.cpython-312.pyc,,
mediapipe/framework/stream_handler/__pycache__/fixed_size_input_stream_handler_pb2.cpython-312.pyc,,
mediapipe/framework/stream_handler/__pycache__/sync_set_input_stream_handler_pb2.cpython-312.pyc,,
mediapipe/framework/stream_handler/__pycache__/timestamp_align_input_stream_handler_pb2.cpython-312.pyc,,
mediapipe/framework/stream_handler/default_input_stream_handler_pb2.py,sha256=I6GmO2cPVx5fMELTVn9fEEgUe2ShTVGWeZkMySZ0pXw,1666
mediapipe/framework/stream_handler/fixed_size_input_stream_handler_pb2.py,sha256=05UC7SDg94ywV9UiYoQEtJY_c4P52fJ-Y8ekmd_bMOo,1816
mediapipe/framework/stream_handler/sync_set_input_stream_handler_pb2.py,sha256=LrEZ9vU9HsWa7-jxhGnjAsDiTZrpEwxdtKLM3R3fLuY,1928
mediapipe/framework/stream_handler/timestamp_align_input_stream_handler_pb2.py,sha256=zi4xi80r07tU9N7cDEpeSdPkSEW2hLmC7aUrjntHRRY,1728
mediapipe/framework/stream_handler_pb2.py,sha256=hYOKxbOJ6pTP2GBaaR1feeEZBn3Hvd3mPe7rRwR4u2Q,1914
mediapipe/framework/test_calculators_pb2.py,sha256=nLWnU9WOVTdF88oYhAr1U2UOPLEl9aFWJOD7f23_o9Y,2048
mediapipe/framework/thread_pool_executor_pb2.py,sha256=UyNwmvMmHGFziFOL-fKlEiH6h7FH9Ssv8pbBMSg2p8Y,2093
mediapipe/framework/tool/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/tool/__pycache__/__init__.cpython-312.pyc,,
mediapipe/framework/tool/__pycache__/calculator_graph_template_pb2.cpython-312.pyc,,
mediapipe/framework/tool/__pycache__/field_data_pb2.cpython-312.pyc,,
mediapipe/framework/tool/__pycache__/node_chain_subgraph_pb2.cpython-312.pyc,,
mediapipe/framework/tool/__pycache__/packet_generator_wrapper_calculator_pb2.cpython-312.pyc,,
mediapipe/framework/tool/__pycache__/source_pb2.cpython-312.pyc,,
mediapipe/framework/tool/__pycache__/switch_container_pb2.cpython-312.pyc,,
mediapipe/framework/tool/calculator_graph_template_pb2.py,sha256=HF1YlrqXek3YUcGT6Gehgg9yxZraeUOIOTU01NNMXZ4,4040
mediapipe/framework/tool/field_data_pb2.py,sha256=AE1fLuy_pUu5DM_5sZIoYybyMf_7yB9ZY3i_diylrLc,1741
mediapipe/framework/tool/node_chain_subgraph_pb2.py,sha256=ESGP0xp_UtjRd6K1gnJw8XdK6b0MVn3_xcq1rjifMQY,1914
mediapipe/framework/tool/packet_generator_wrapper_calculator_pb2.py,sha256=-wNq0Wx9XyxQTIlQEw8yn8W2iTNiW6nK6B6AquvDX2g,1971
mediapipe/framework/tool/source_pb2.py,sha256=d3WKy5HCd8BXoiyXbgEvgcEXOck0jG5K1A8BZSAEFmc,2427
mediapipe/framework/tool/switch_container_pb2.py,sha256=MUL5PLYl78fVUIYv-S0imuajOEA8ymbfVjxjLmWrhcI,2310
mediapipe/gpu/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/gpu/__pycache__/__init__.cpython-312.pyc,,
mediapipe/gpu/__pycache__/copy_calculator_pb2.cpython-312.pyc,,
mediapipe/gpu/__pycache__/gl_animation_overlay_calculator_pb2.cpython-312.pyc,,
mediapipe/gpu/__pycache__/gl_context_options_pb2.cpython-312.pyc,,
mediapipe/gpu/__pycache__/gl_scaler_calculator_pb2.cpython-312.pyc,,
mediapipe/gpu/__pycache__/gl_surface_sink_calculator_pb2.cpython-312.pyc,,
mediapipe/gpu/__pycache__/gpu_origin_pb2.cpython-312.pyc,,
mediapipe/gpu/__pycache__/scale_mode_pb2.cpython-312.pyc,,
mediapipe/gpu/copy_calculator_pb2.py,sha256=dcJTSF3B83fulWymhnpHfjFjEiGDo7tT_XZfIjoE570,2111
mediapipe/gpu/gl_animation_overlay_calculator_pb2.py,sha256=B39FNMICwnPDwg4R_7KOsB3URV-7wim0hEBzjAf9PUw,2207
mediapipe/gpu/gl_context_options_pb2.py,sha256=tWve26KQdyvfps4etNp-vHTmGhd2w9FcTQAC2k-LsdE,1791
mediapipe/gpu/gl_scaler_calculator_pb2.py,sha256=dyG_dgGBzhs-xWhu_qfyY3ziqIx8Gozfcrb02IzU6kk,2416
mediapipe/gpu/gl_surface_sink_calculator_pb2.py,sha256=LjQf8r-W1rgdu3DjzARzsLcwCqwiy3duJflCZvZVIZI,2050
mediapipe/gpu/gpu_origin_pb2.py,sha256=yeD56kaFdZ50rdXn_rbz6l0FIkkNp10BlXVQpmHgfS8,1371
mediapipe/gpu/scale_mode_pb2.py,sha256=F8jwpiTk7-bozCg_65GfKdgsimtFrjFAyNZbtE9HhR0,1238
mediapipe/model_maker/__init__.py,sha256=IswWcmeOPaRBNNdVp3K6m_ISXrI9_Enxnp-1kiaUEKs,1153
mediapipe/model_maker/__pycache__/__init__.cpython-312.pyc,,
mediapipe/model_maker/__pycache__/setup.cpython-312.pyc,,
mediapipe/model_maker/setup.py,sha256=GaOQVu7uTMp3gjpDHIVV45GEnLvSrgvg-BJoJAJXMbI,4064
mediapipe/modules/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/face_detection/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/face_detection/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/face_detection/__pycache__/face_detection_pb2.cpython-312.pyc,,
mediapipe/modules/face_detection/face_detection_full_range_cpu.binarypb,sha256=APBy5xqXUlNqpLCSFsa_jZzHSTIiqNOkcuVj6w4jFjs,260
mediapipe/modules/face_detection/face_detection_full_range_sparse.tflite,sha256=LDco5tpW8h4hoyBDM5b7BtQNkIjyJHwF5WNaaI1F3-E,676746
mediapipe/modules/face_detection/face_detection_pb2.py,sha256=07v5mfJ7pzdVj7vlkFkpIwC5PLxlXS2YpQaPGzKUQOg,2771
mediapipe/modules/face_detection/face_detection_short_range.tflite,sha256=u_8Rzr0esnoeAEyuCw5j7IxVHL80pEURSLSQi42z7Kg,229714
mediapipe/modules/face_detection/face_detection_short_range_cpu.binarypb,sha256=hOZXlNTh0cbVaJb036q5e8dpAzIFR1wbuZZ6IvDaRpw,262
mediapipe/modules/face_geometry/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/face_geometry/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/face_geometry/__pycache__/effect_renderer_calculator_pb2.cpython-312.pyc,,
mediapipe/modules/face_geometry/__pycache__/env_generator_calculator_pb2.cpython-312.pyc,,
mediapipe/modules/face_geometry/__pycache__/geometry_pipeline_calculator_pb2.cpython-312.pyc,,
mediapipe/modules/face_geometry/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/face_geometry/data/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/face_geometry/effect_renderer_calculator_pb2.py,sha256=Ey32VJH4wGOV956wcMbZoCv2Iul-3N684OipXZgjsqg,1798
mediapipe/modules/face_geometry/env_generator_calculator_pb2.py,sha256=RC1SX0AnrsA9-wU6LjyDRdCWVlbskgMHmc22e49_Ivs,1948
mediapipe/modules/face_geometry/geometry_pipeline_calculator_pb2.py,sha256=jTUNPOudttBiwwRm26z_Rc_5IGWqIlknZph5Zd3JppI,1687
mediapipe/modules/face_geometry/libs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/face_geometry/libs/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/face_geometry/protos/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/face_geometry/protos/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/face_geometry/protos/__pycache__/environment_pb2.cpython-312.pyc,,
mediapipe/modules/face_geometry/protos/__pycache__/face_geometry_pb2.cpython-312.pyc,,
mediapipe/modules/face_geometry/protos/__pycache__/geometry_pipeline_metadata_pb2.cpython-312.pyc,,
mediapipe/modules/face_geometry/protos/__pycache__/mesh_3d_pb2.cpython-312.pyc,,
mediapipe/modules/face_geometry/protos/environment_pb2.py,sha256=Jxbiv00ITsowID4Kn1EBR-K53ECkYDcA3W6cv7F33nM,1957
mediapipe/modules/face_geometry/protos/face_geometry_pb2.py,sha256=0Jpz5NGIyxksx-5pzlg2INf4VeCDhnlaJRh0w-lKGkI,1816
mediapipe/modules/face_geometry/protos/geometry_pipeline_metadata_pb2.py,sha256=-pGcleOjJGmARayTf5n_8dczG3MaZ9Xb1LShR_VlZ_c,2344
mediapipe/modules/face_geometry/protos/mesh_3d_pb2.py,sha256=-QJvrlWuarKcRNrnb2se0JBXVPZHEw1EFBrEMCCj2vE,1829
mediapipe/modules/face_landmark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/face_landmark/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/face_landmark/face_landmark.tflite,sha256=EFXLnUqcqLjGiJAqOlGUMRE4uiVrzJTjNtg3Ol8wyBQ,1242398
mediapipe/modules/face_landmark/face_landmark_front_cpu.binarypb,sha256=-jwJLaJ9Ax1U3dYaKbDj3VIdU_VfqImMsPK2TXnNZXM,2361
mediapipe/modules/face_landmark/face_landmark_with_attention.tflite,sha256=4GqATgFE-ZKe2nghIpFrNdYMaXw8k0QBPKK752ps4rQ,2495106
mediapipe/modules/hand_landmark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/hand_landmark/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/hand_landmark/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/hand_landmark/calculators/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/hand_landmark/hand_landmark_full.tflite,sha256=EcJyuJHhqZqwNCCOI5N6gAg4jPEe0qnXdu09AdC6AOM,5478917
mediapipe/modules/hand_landmark/hand_landmark_lite.tflite,sha256=BI7dNkXJv3OX0ZqfbjpClX1uQUyb6mWYAwoum2JBVuY,2071597
mediapipe/modules/hand_landmark/hand_landmark_tracking_cpu.binarypb,sha256=lCrU5rPs0ZA_0nQ60iVaNBhU3pde-ckEGKTHtk8LRCU,2901
mediapipe/modules/hand_landmark/handedness.txt,sha256=O4LwJGhw_sRWyGMtRVpOHVZI0oNqelE6JxeBFE9SWRY,11
mediapipe/modules/holistic_landmark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/holistic_landmark/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/holistic_landmark/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/holistic_landmark/calculators/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/holistic_landmark/calculators/__pycache__/roi_tracking_calculator_pb2.cpython-312.pyc,,
mediapipe/modules/holistic_landmark/calculators/roi_tracking_calculator_pb2.py,sha256=qq0n4ytY7-vWOJ7ten3yH10doWAZS7nP62XA1b5YppA,3135
mediapipe/modules/holistic_landmark/hand_recrop.tflite,sha256=Z9mWzpb502_hfSaTAixtqTFoAmqy8Cj54jZTmNisfV0,123792
mediapipe/modules/holistic_landmark/holistic_landmark_cpu.binarypb,sha256=wdo-qSzQ366zWhs4Y6OjQgLG9L7_5BDv9KM3VpyAj70,1370
mediapipe/modules/iris_landmark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/iris_landmark/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/iris_landmark/iris_landmark.tflite,sha256=0XRNKgnCX1AdOeuk-v9H5T7MqIUsXOGbzo7qw5NXUh8,2640568
mediapipe/modules/objectron/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/objectron/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/objectron/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/objectron/calculators/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/a_r_capture_metadata_pb2.cpython-312.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/annotation_data_pb2.cpython-312.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/belief_decoder_config_pb2.cpython-312.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/camera_parameters_pb2.cpython-312.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/filter_detection_calculator_pb2.cpython-312.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/frame_annotation_to_rect_calculator_pb2.cpython-312.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/frame_annotation_tracker_calculator_pb2.cpython-312.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/lift_2d_frame_annotation_to_3d_calculator_pb2.cpython-312.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/object_pb2.cpython-312.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/tensors_to_objects_calculator_pb2.cpython-312.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/tflite_tensors_to_objects_calculator_pb2.cpython-312.pyc,,
mediapipe/modules/objectron/calculators/a_r_capture_metadata_pb2.py,sha256=BTcBB3VZyV2a-8FZkPkyRFm1jHE9ksFiQ3_7relx2iM,13458
mediapipe/modules/objectron/calculators/annotation_data_pb2.py,sha256=jrppzLNr1dcgjp_WMb_ExZz4GG_2eX8ekj194X1U3NY,3323
mediapipe/modules/objectron/calculators/belief_decoder_config_pb2.py,sha256=uOxzPD-evAq-03ndW2zEG4HTBAdZDSW1D-BRroKr_pQ,1647
mediapipe/modules/objectron/calculators/camera_parameters_pb2.py,sha256=u0iU9kIqM5bIzUeTkbSfrP5_pOc30YnRxJolYTfcD3Y,2162
mediapipe/modules/objectron/calculators/filter_detection_calculator_pb2.py,sha256=og5tBru2cwrRfRGeX3HPOW7GUnWMBz4xFcWeukcZ_fY,2714
mediapipe/modules/objectron/calculators/frame_annotation_to_rect_calculator_pb2.py,sha256=PmW4p8DbLXivb0kMOgAG6r5K1eWS888lsBHqs8EB4T0,2113
mediapipe/modules/objectron/calculators/frame_annotation_tracker_calculator_pb2.py,sha256=RsPw26b_lBZXTQXYBN-5x7kEXw0w2ZSH-2jgbD0LsW8,2142
mediapipe/modules/objectron/calculators/lift_2d_frame_annotation_to_3d_calculator_pb2.py,sha256=KcyP4ib-9GZBQ9jTixhqS2lJ1M-Hry4wuRo8Okc4kOY,2627
mediapipe/modules/objectron/calculators/object_pb2.py,sha256=xhFW5SQ8p4ebpO-2wzlO_bCUvESmRAmK4PyQqaZPMBc,2890
mediapipe/modules/objectron/calculators/tensors_to_objects_calculator_pb2.py,sha256=t1hpbPK0UfvI0_vMWcNZub06Xm7NSX8iV9-5TOAZuvQ,2433
mediapipe/modules/objectron/calculators/tflite_tensors_to_objects_calculator_pb2.py,sha256=ayM_LK4s_qeVpdXiGJ7F15FYz8TtaNcqasdfmzlCcHg,2736
mediapipe/modules/objectron/object_detection_oidv4_labelmap.txt,sha256=wUgzamX2cwE2Cakj_cve7A9J35NQRQKcN0ajgooOgOA,184
mediapipe/modules/objectron/objectron_cpu.binarypb,sha256=lu3H83WDBFhYeEXV4w3omPOcIfUo7FxYuv2hvWWyoYo,2267
mediapipe/modules/palm_detection/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/palm_detection/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/palm_detection/palm_detection_full.tflite,sha256=GxTpQixq0AbN5lgaRsi5DdVzwHq385NLVYnnzqP4mlQ,2339846
mediapipe/modules/palm_detection/palm_detection_lite.tflite,sha256=6aSq3fkN2laocjUwPPAOTC0_socl9o_Yh3KZfayQXBg,1985440
mediapipe/modules/pose_detection/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/pose_detection/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/pose_detection/pose_detection.tflite,sha256=m6ndPULvqrqGtP8BIrBvKcQSLnVrMp2J3KHil_2Phmw,2959046
mediapipe/modules/pose_landmark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/pose_landmark/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/pose_landmark/pose_landmark_cpu.binarypb,sha256=kN3HF2JaPBrt_coSQhKjbOWVzbvkZRP9k63KUgLY_yE,2418
mediapipe/modules/pose_landmark/pose_landmark_full.tflite,sha256=6aXFyxf3Nvr9TC7B2js9Mx1u2-ig0yOVhVrrLN_WS58,6440512
mediapipe/modules/selfie_segmentation/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/selfie_segmentation/__pycache__/__init__.cpython-312.pyc,,
mediapipe/modules/selfie_segmentation/selfie_segmentation.tflite,sha256=nuFo7HyPKhbFb-jhz7xRSXTLu35DQFG0VWNfG9FGL1w,249505
mediapipe/modules/selfie_segmentation/selfie_segmentation_cpu.binarypb,sha256=358pHq3CrXekoPq9g9TywMK02GJsj0T7DRTvwvyB9G0,815
mediapipe/modules/selfie_segmentation/selfie_segmentation_landscape.tflite,sha256=p30D9GWbn2tsH1EGlHv0DpnXZVCUtlJ_IU6n1FEQbt0,250145
mediapipe/python/__init__.py,sha256=BQglgytZUe7_ZuD8amosz-szWdJ2LQp81nsuiEY3W84,1493
mediapipe/python/__pycache__/__init__.cpython-312.pyc,,
mediapipe/python/__pycache__/calculator_graph_test.cpython-312.pyc,,
mediapipe/python/__pycache__/image_frame_test.cpython-312.pyc,,
mediapipe/python/__pycache__/image_test.cpython-312.pyc,,
mediapipe/python/__pycache__/packet_creator.cpython-312.pyc,,
mediapipe/python/__pycache__/packet_getter.cpython-312.pyc,,
mediapipe/python/__pycache__/packet_test.cpython-312.pyc,,
mediapipe/python/__pycache__/solution_base.cpython-312.pyc,,
mediapipe/python/__pycache__/solution_base_test.cpython-312.pyc,,
mediapipe/python/__pycache__/timestamp_test.cpython-312.pyc,,
mediapipe/python/_framework_bindings.cp312-win_amd64.pyd,sha256=O6Nj71r7SKkAdWJfe2nzend1KwfCMitMNaqO3KbktDM,11524608
mediapipe/python/calculator_graph_test.py,sha256=GpUQPxetP803jNNLyaI5pxujIvKsMutB5dGoEsOVR2M,8876
mediapipe/python/image_frame_test.py,sha256=ZSjdE-an2t8i6MiA4_Xri91VMH5_CCx45fjhWUQptMY,8602
mediapipe/python/image_test.py,sha256=3YABND327mtfbEMJyr9wr-_Ilhrf5tyxNaIGaiGDBIU,9436
mediapipe/python/opencv_world3410.dll,sha256=uSXhlVrxcY7RrhDR8_HbV-WrjEhlcp5HSNC6ch7cpoc,55907328
mediapipe/python/packet_creator.py,sha256=yUrRQL5B-wFjfSLM34qBXYI13JGLZ-P8_bE31S7iuXU,11502
mediapipe/python/packet_getter.py,sha256=vD8empqGtMG2-OLFahqKorqNohU9S3iHBxZL2i3IcGs,4235
mediapipe/python/packet_test.py,sha256=SrAkhIptP8Yk0yL6PoHwfs9OT1iDzAwz79m-TsImlmk,22306
mediapipe/python/solution_base.py,sha256=nEIqsho9DlutfvWWzdSxCOpJ2QzN7n2938WLDmFzn38,26072
mediapipe/python/solution_base_test.py,sha256=1u5Lo4aEUrMKj8Ha_34XMyKnI-3A1AvpaX3MCI0b2MM,15632
mediapipe/python/solutions/__init__.py,sha256=wta23yyLyQthEl5lHe4gKxaKs1sqyp2RLlX6HOUc8iE,1145
mediapipe/python/solutions/__pycache__/__init__.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/download_utils.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/drawing_styles.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/drawing_utils.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/drawing_utils_test.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/face_detection.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/face_detection_test.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/face_mesh.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/face_mesh_connections.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/face_mesh_test.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/hands.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/hands_connections.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/hands_test.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/holistic.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/holistic_test.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/objectron.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/objectron_test.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/pose.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/pose_connections.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/pose_test.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/selfie_segmentation.cpython-312.pyc,,
mediapipe/python/solutions/__pycache__/selfie_segmentation_test.cpython-312.pyc,,
mediapipe/python/solutions/download_utils.py,sha256=DQr0b340aUgfloKCmZMjH-knsBRbIJW4lTgX4CzTPO8,1508
mediapipe/python/solutions/drawing_styles.py,sha256=u52h0xbgNPzzqAKhX7Yt2Nrxdloq_br_8nivNp2CoAM,9599
mediapipe/python/solutions/drawing_utils.py,sha256=di2hvd8HmBcMErDdRKto7WgKr6ralq5w-rYAy76hx-U,13775
mediapipe/python/solutions/drawing_utils_test.py,sha256=5YGiD-hdtSbtW4mvPlnIYbhlSAQ4wIHwj22qCzHFzW0,11848
mediapipe/python/solutions/face_detection.py,sha256=KNLDRKssdSUkS3oGIgfYvDH_QBUu7z9Ja9FuHJi0VRI,3772
mediapipe/python/solutions/face_detection_test.py,sha256=qgHrbywIuw39sF9UB5DYtkHWJIEPojeVdfsxR8YRyxI,3776
mediapipe/python/solutions/face_mesh.py,sha256=gUc_8snx8ENZ2L2W6qcMbXg503BvKGdDQ5MmaFVpYYc,5886
mediapipe/python/solutions/face_mesh_connections.py,sha256=zHFx5E0GEts4cgyy_SUWw5WZLN10V-_jQVQ75vPCLYc,36170
mediapipe/python/solutions/face_mesh_test.py,sha256=cmpXx4JZr7SinUjQixgmGPhge5IEhfTSLeSxsWdyots,5919
mediapipe/python/solutions/hands.py,sha256=TuoT4PY-rlzy3CFEjqeN849MkKKjb0Px242Hl_eItRo,6132
mediapipe/python/solutions/hands_connections.py,sha256=KHShpe4oSSykZGJNRlYSgPNYbuyaK-gJ2AAkBVH2PT0,1226
mediapipe/python/solutions/hands_test.py,sha256=DPj-mvn_tFLU-0oJpryvBeWuLKUT-nQaXEb7yq5hGLE,9481
mediapipe/python/solutions/holistic.py,sha256=qcXP-MS2G7NUa9AB1HU2ZsveT2hvHdxTIN6zeOpu-eo,8226
mediapipe/python/solutions/holistic_test.py,sha256=81K7WvWBNiDjAT__DDFgC7V2i5U3spng29oBRKi28-U,6746
mediapipe/python/solutions/objectron.py,sha256=GU9oWOp-RHXchvS833n7_ifYYqEFMDBUlx1iIE9cHcQ,11653
mediapipe/python/solutions/objectron_test.py,sha256=dBYaGQtaY1DbEKHguBgg_SoP7tEUjng2IRwwyU_kbb0,3304
mediapipe/python/solutions/pose.py,sha256=uer1phbEF2aI00KQZRp3GqWK03L68PVaU1Lcmy9LFV4,7921
mediapipe/python/solutions/pose_connections.py,sha256=bY6IbTOXpb9_SmcD05sn2qo8-A3Ni8zgsRH2i9W9xuw,1166
mediapipe/python/solutions/pose_test.py,sha256=TKhiHbrQroAoh7N8u1ttIapp0uiGn8lP60-NGnwpGRU,11418
mediapipe/python/solutions/selfie_segmentation.py,sha256=YAoacNZn_JD3s5VYBjAbOopR4XQhOmWjIImZfH0pKyY,2774
mediapipe/python/solutions/selfie_segmentation_test.py,sha256=x1nPszVUxtOwibYd-LmbrkGFhjO3Fwb6h4AF9REbMgE,2690
mediapipe/python/timestamp_test.py,sha256=oWKTZMsV586jH57OBV30rihcymETyGC29VbYURNLJQQ,2528
mediapipe/tasks/__init__.py,sha256=sVJS2p8J2PNVl8DLRPVY6KLpHenP_z3QVPRU0x_iL5g,571
mediapipe/tasks/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/audio/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/audio/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/audio/audio_classifier/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/audio/audio_classifier/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/audio/audio_classifier/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/audio/audio_classifier/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/audio/audio_classifier/proto/__pycache__/audio_classifier_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/audio/audio_classifier/proto/audio_classifier_graph_options_pb2.py,sha256=4p51q8bCdLsjQh3u6SwY9sq0MsnLzYnZeuQsnMlDB-g,3135
mediapipe/tasks/cc/audio/audio_embedder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/audio/audio_embedder/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/audio/audio_embedder/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/audio/audio_embedder/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/audio/audio_embedder/proto/__pycache__/audio_embedder_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/audio/audio_embedder/proto/audio_embedder_graph_options_pb2.py,sha256=CIJNOuxhrH3JkpTTxHrRrkN4RmgdTDYqAjBGuQjub6Y,3030
mediapipe/tasks/cc/audio/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/audio/core/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/audio/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/audio/utils/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/components/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/components/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/components/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/components/calculators/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/components/calculators/__pycache__/classification_aggregation_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/calculators/__pycache__/score_calibration_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/calculators/__pycache__/tensors_to_embeddings_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/calculators/classification_aggregation_calculator_pb2.py,sha256=4jDlc3NvpyGJ2ooGgQBBymFq2x12j1lCz_Wn4Hhc58o,2067
mediapipe/tasks/cc/components/calculators/score_calibration_calculator_pb2.py,sha256=QXeL52xrs-XGpD6r1EkYZ8eVokl166-sYxx3OHR8U54,2915
mediapipe/tasks/cc/components/calculators/tensors_to_embeddings_calculator_pb2.py,sha256=5KYAhCFl8oMQHaBh-w3m-dq75JR5b-o-Gce9irG2nGE,2434
mediapipe/tasks/cc/components/containers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/components/containers/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/components/containers/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/components/containers/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/components/containers/proto/__pycache__/classifications_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/containers/proto/__pycache__/embeddings_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/containers/proto/__pycache__/landmarks_detection_result_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/containers/proto/classifications_pb2.py,sha256=tmY5aw0_DV5ofYofdRQl-bKUu6-XIg4oaOfN642yQj8,2045
mediapipe/tasks/cc/components/containers/proto/embeddings_pb2.py,sha256=9acLu01wr4AXKYknSzHZ6266gcId_f6CWP_sYK77DHw,2430
mediapipe/tasks/cc/components/containers/proto/landmarks_detection_result_pb2.py,sha256=WRnJ9PA05iyHO3TDlGuSEEkf8H3MbQH12t0dD3uiduU,2712
mediapipe/tasks/cc/components/processors/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/components/processors/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/components/processors/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/components/processors/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/classification_postprocessing_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/classifier_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/detection_postprocessing_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/detector_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/embedder_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/embedding_postprocessing_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/image_preprocessing_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/text_model_type_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/text_preprocessing_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/components/processors/proto/classification_postprocessing_graph_options_pb2.py,sha256=8tUmttl0s_Oa-BL_n7lN2OzVVXxzHtGa239DcSjdF_I,4004
mediapipe/tasks/cc/components/processors/proto/classifier_options_pb2.py,sha256=GiE8FXqH36P_eNG3223jpVf7kmXb8NQmeWFRLTgCq04,1659
mediapipe/tasks/cc/components/processors/proto/detection_postprocessing_graph_options_pb2.py,sha256=mww1HEaje1Dy5xaFU0EO6UEkiFnxiqreuF825fmk3TQ,3866
mediapipe/tasks/cc/components/processors/proto/detector_options_pb2.py,sha256=nuXOk4EHyx209TnI0Be2-f8O0OEsSjqW0fRJIEMihxk,1765
mediapipe/tasks/cc/components/processors/proto/embedder_options_pb2.py,sha256=Uamq_KX8ay0QIqk05Btm0K9wos3DsW7C47ZEpqOl1A8,1459
mediapipe/tasks/cc/components/processors/proto/embedding_postprocessing_graph_options_pb2.py,sha256=yEip03UtprNYtnHu2JL_s5Va7-mOus4dM4G8qK4fHUk,2527
mediapipe/tasks/cc/components/processors/proto/image_preprocessing_graph_options_pb2.py,sha256=Yy-FOAdbv9PuqCpPh4VbKSMLtnU7ULiuzAMJ8nEIKrc,2799
mediapipe/tasks/cc/components/processors/proto/text_model_type_pb2.py,sha256=ZTh4kcpRweSS7FXbJbiwzHIWmgt_Rxk3eHa9nUBM9GU,1449
mediapipe/tasks/cc/components/processors/proto/text_preprocessing_graph_options_pb2.py,sha256=NvhpHqlRaQ3mycKEnr1jFV6IU1KDofiZ9SqmnNv5Ojo,2496
mediapipe/tasks/cc/components/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/components/utils/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/core/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/core/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/core/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/core/proto/__pycache__/acceleration_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/core/proto/__pycache__/base_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/core/proto/__pycache__/external_file_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/core/proto/__pycache__/inference_subgraph_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/core/proto/__pycache__/model_resources_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/core/proto/acceleration_pb2.py,sha256=oUBEdbOHmB_332tPO8gdcR24jL5VwQn7koUIhyRO0Dk,1901
mediapipe/tasks/cc/core/proto/base_options_pb2.py,sha256=cGgBoSMu14yr8b8i7IhseN-8p0EyC76eenrnrZ9HMBo,2083
mediapipe/tasks/cc/core/proto/external_file_pb2.py,sha256=xrQxCWLYiFlkt9o_TExlz8BsJ7e9lTiup8pPPLkqSpQ,2007
mediapipe/tasks/cc/core/proto/inference_subgraph_pb2.py,sha256=z5kj_aumrAKVCOJrs8QwCmg1QTBWm3bvkMrJvRV8m3A,2213
mediapipe/tasks/cc/core/proto/model_resources_calculator_pb2.py,sha256=Z3hRk2aL2XpocaZhm3SwOLyK4hsxZ9fhZ8_L9RbEd5c,2262
mediapipe/tasks/cc/genai/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/c/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/c/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/calculators/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/calculators/__pycache__/detokenizer_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/calculators/__pycache__/llm_gpu_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/calculators/__pycache__/model_data_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/calculators/__pycache__/tokenizer_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/calculators/detokenizer_calculator_pb2.py,sha256=sriTekYvLuxnDqvT4LiFQgBPfOieMggTHc27y0sD4Pg,1629
mediapipe/tasks/cc/genai/inference/calculators/llm_gpu_calculator_pb2.py,sha256=Ut9SWG4oW78eZqdj7BCL9fYlNEiBp38lwwHDHchPurI,3435
mediapipe/tasks/cc/genai/inference/calculators/model_data_calculator_pb2.py,sha256=-_rsgo45Mll2dKWCL-lTmDzO6rlP1sVCk42W_yC2HjQ,1486
mediapipe/tasks/cc/genai/inference/calculators/tokenizer_calculator_pb2.py,sha256=H85aKnFQLoHn_m7EDGCSd4VcYxyO3pZttvC8tN19NXw,1935
mediapipe/tasks/cc/genai/inference/common/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/common/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/proto/__pycache__/llm_file_metadata_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/proto/__pycache__/llm_params_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/proto/__pycache__/prompt_template_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/proto/__pycache__/sampler_params_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/proto/__pycache__/transformer_params_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/proto/llm_file_metadata_pb2.py,sha256=BUCCz6ZMvWUiaL0ytZLC_q3jMyoDCadZVi0FYOxU1zo,2397
mediapipe/tasks/cc/genai/inference/proto/llm_params_pb2.py,sha256=9qRhrQMd6hMfSQDtTgeg8HfaQqdR3EiZGarDd_xqoR4,3947
mediapipe/tasks/cc/genai/inference/proto/prompt_template_pb2.py,sha256=QG5B-hpDflfyZ1ZD3On2uTtoSIGc0Ny6YTWANcAxvTc,1407
mediapipe/tasks/cc/genai/inference/proto/sampler_params_pb2.py,sha256=YCG0vq9uXcv8mDezVNZQ01rFfvQz71r2OehbOD450rQ,1803
mediapipe/tasks/cc/genai/inference/proto/transformer_params_pb2.py,sha256=vRT5RRQKmBxgERCakySK27nd_Gwwvwa7rxXYsG80CZs,6593
mediapipe/tasks/cc/genai/inference/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/utils/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/utils/llm_utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/utils/llm_utils/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/genai/inference/utils/xnn_utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/utils/xnn_utils/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/metadata/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/metadata/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/metadata/python/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/metadata/python/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/metadata/python/_pywrap_metadata_version.cp312-win_amd64.pyd,sha256=8N8sJJEhXCMz3jAPlmK9vRbF6nnpjQQwluxFRh0rFL4,153088
mediapipe/tasks/cc/metadata/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/metadata/tests/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/metadata/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/metadata/utils/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/custom_ops/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/custom_ops/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/custom_ops/ragged/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/custom_ops/ragged/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/custom_ops/sentencepiece/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/custom_ops/sentencepiece/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/custom_ops/sentencepiece/testdata/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/custom_ops/sentencepiece/testdata/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/language_detector/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/language_detector/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/language_detector/custom_ops/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/language_detector/custom_ops/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/language_detector/custom_ops/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/language_detector/custom_ops/utils/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/language_detector/custom_ops/utils/hash/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/language_detector/custom_ops/utils/hash/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/language_detector/custom_ops/utils/utf/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/language_detector/custom_ops/utils/utf/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/text_classifier/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/text_classifier/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/text_classifier/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/text_classifier/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/text_classifier/proto/__pycache__/text_classifier_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/text/text_classifier/proto/text_classifier_graph_options_pb2.py,sha256=3VG1Q8lkndKXwGro-nNQqwdBX2JTtpuarPupdyzbZPY,3041
mediapipe/tasks/cc/text/text_embedder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/text_embedder/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/text_embedder/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/text_embedder/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/text_embedder/proto/__pycache__/text_embedder_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/text/text_embedder/proto/text_embedder_graph_options_pb2.py,sha256=xfdufbNhp3goTT20zSoW2AFyiyLeK5o0w3_1Yswoq5o,3000
mediapipe/tasks/cc/text/tokenizers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/tokenizers/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/text/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/utils/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/core/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/custom_ops/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/custom_ops/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_detector/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_detector/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_detector/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_detector/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_detector/proto/__pycache__/face_detector_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_detector/proto/face_detector_graph_options_pb2.py,sha256=EcIKsLHrT8VNCYv70aEkaTLmO88-xxod1eOUGGjv46I,2854
mediapipe/tasks/cc/vision/face_geometry/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_geometry/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_geometry/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_geometry/calculators/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_geometry/calculators/__pycache__/env_generator_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_geometry/calculators/__pycache__/geometry_pipeline_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_geometry/calculators/env_generator_calculator_pb2.py,sha256=zOD3LtRKJNIiSUtC4BDkIu9hDqrh-EtiwqDJyxqP2q0,2103
mediapipe/tasks/cc/vision/face_geometry/calculators/geometry_pipeline_calculator_pb2.py,sha256=0GUe-NckThZdt9vKctZbSOp1FEJTxzChTmstYUkL-j4,2294
mediapipe/tasks/cc/vision/face_geometry/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_geometry/data/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_geometry/libs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_geometry/libs/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_geometry/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_geometry/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_geometry/proto/__pycache__/environment_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_geometry/proto/__pycache__/face_geometry_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_geometry/proto/__pycache__/face_geometry_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_geometry/proto/__pycache__/geometry_pipeline_metadata_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_geometry/proto/__pycache__/mesh_3d_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_geometry/proto/environment_pb2.py,sha256=LMcmSDZDNxMBXGwBJ8kSuV4unV9crB2vA-d2XM4lWbw,2052
mediapipe/tasks/cc/vision/face_geometry/proto/face_geometry_graph_options_pb2.py,sha256=pO3RJwqcM2iAIX5Br9_dS-lkwB6tfaGCjiKj6gOFS48,2369
mediapipe/tasks/cc/vision/face_geometry/proto/face_geometry_pb2.py,sha256=zFypESL0YhnNDKmU56n1Qr2AHkWJ3BQvU92KAUOwK_U,1927
mediapipe/tasks/cc/vision/face_geometry/proto/geometry_pipeline_metadata_pb2.py,sha256=SGPt6nRFKmN2LhMhK6GK_mORQH5Hp5SqHOeYx61VJ4I,2489
mediapipe/tasks/cc/vision/face_geometry/proto/mesh_3d_pb2.py,sha256=ITv0yfW86SUN5d9IK-0nMVE_U99vGGiNNWzJ1jCOlV8,1924
mediapipe/tasks/cc/vision/face_landmarker/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_landmarker/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_landmarker/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_landmarker/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_landmarker/proto/__pycache__/face_blendshapes_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_landmarker/proto/__pycache__/face_landmarker_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_landmarker/proto/__pycache__/face_landmarks_detector_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_landmarker/proto/__pycache__/tensors_to_face_landmarks_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_landmarker/proto/face_blendshapes_graph_options_pb2.py,sha256=u-xaeTIJUdNrV84FtLhAavp6qs1e6pk0aFCkwbCMQpE,2697
mediapipe/tasks/cc/vision/face_landmarker/proto/face_landmarker_graph_options_pb2.py,sha256=W-HNEsvl6bRdzKt4JbgYituuTdPrdwZgIeEmwfVdR9I,4090
mediapipe/tasks/cc/vision/face_landmarker/proto/face_landmarks_detector_graph_options_pb2.py,sha256=TxALrJ3cbeyw-ZinxVuPipnDUVA-h3ng4DVM_-bqo50,3302
mediapipe/tasks/cc/vision/face_landmarker/proto/tensors_to_face_landmarks_graph_options_pb2.py,sha256=_QO0nFx_MndLWAbcy5HNWuXBgzy-uMtbA4e9hKcDAiQ,2345
mediapipe/tasks/cc/vision/face_stylizer/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_stylizer/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_stylizer/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_stylizer/calculators/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_stylizer/calculators/__pycache__/tensors_to_image_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_stylizer/calculators/tensors_to_image_calculator_pb2.py,sha256=RUPbkEVpg5UJrma_fjt0rUH4rQ5Hk798EzxzJDJftO4,2990
mediapipe/tasks/cc/vision/face_stylizer/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_stylizer/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_stylizer/proto/__pycache__/face_stylizer_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/face_stylizer/proto/face_stylizer_graph_options_pb2.py,sha256=4xWJgafVayev8pYfZActm_4u0Fz_r7Xnm66qMZpi9LM,3194
mediapipe/tasks/cc/vision/gesture_recognizer/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/gesture_recognizer/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/gesture_recognizer/calculators/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/calculators/__pycache__/combined_prediction_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/calculators/__pycache__/landmarks_to_matrix_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/calculators/combined_prediction_calculator_pb2.py,sha256=FLppvzlrsM-jIgci_N00SVOqgrL8S12K0iN9lybFBcY,2502
mediapipe/tasks/cc/vision/gesture_recognizer/calculators/landmarks_to_matrix_calculator_pb2.py,sha256=cByvD9O-eyFCRBtOvZpj3ZJIjly4iybijQVKLbGRnz8,2137
mediapipe/tasks/cc/vision/gesture_recognizer/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/gesture_recognizer/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/proto/__pycache__/gesture_classifier_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/proto/__pycache__/gesture_embedder_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/proto/__pycache__/gesture_recognizer_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/proto/__pycache__/hand_gesture_recognizer_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/proto/gesture_classifier_graph_options_pb2.py,sha256=a9_iDO71y5ACLLDO3BH85uXuJUruBS8mAHFeywuHwkU,3099
mediapipe/tasks/cc/vision/gesture_recognizer/proto/gesture_embedder_graph_options_pb2.py,sha256=LKIoNoC0iopPsVqqvOR6zwU6h6mPnTwmXDWM4EXUZBY,2703
mediapipe/tasks/cc/vision/gesture_recognizer/proto/gesture_recognizer_graph_options_pb2.py,sha256=JsV7fCvEhAI_m2V1qjKs5bbyYK0trO4QrNfdZg-JhXM,3643
mediapipe/tasks/cc/vision/gesture_recognizer/proto/hand_gesture_recognizer_graph_options_pb2.py,sha256=C0BZzonlkQ7I8RXECpfu8VT1nuH7oW_K9ci5dyU_XAI,3837
mediapipe/tasks/cc/vision/hand_detector/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/hand_detector/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/hand_detector/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/hand_detector/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/hand_detector/proto/__pycache__/hand_detector_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/hand_detector/proto/__pycache__/hand_detector_result_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/hand_detector/proto/hand_detector_graph_options_pb2.py,sha256=fBKZlhrtgiZT_u62SUWmdCjM4ajVKecm0G19Y8ygwVY,2753
mediapipe/tasks/cc/vision/hand_detector/proto/hand_detector_result_pb2.py,sha256=YyGqLHbMsQi44Dz_69Xl_P02lz9SWxOaA9z6OJt6Hd8,1861
mediapipe/tasks/cc/vision/hand_landmarker/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/hand_landmarker/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/hand_landmarker/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/hand_landmarker/calculators/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/hand_landmarker/calculators/__pycache__/hand_association_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/hand_landmarker/calculators/hand_association_calculator_pb2.py,sha256=PpSgBiLgj2PtpQ0AjoF-j2hmfCNMTLY8LWMvyjXTfbw,2047
mediapipe/tasks/cc/vision/hand_landmarker/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/hand_landmarker/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/hand_landmarker/proto/__pycache__/hand_landmarker_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/hand_landmarker/proto/__pycache__/hand_landmarks_detector_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/hand_landmarker/proto/__pycache__/hand_roi_refinement_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/hand_landmarker/proto/hand_landmarker_graph_options_pb2.py,sha256=lmU5GTrvBJ4XEXjPuv6lgx3lVtF6RtBLjGsUsgBweJY,3625
mediapipe/tasks/cc/vision/hand_landmarker/proto/hand_landmarks_detector_graph_options_pb2.py,sha256=IS6qoFRUo9wnHYkmVLvrlFV0qUjeD-LfTTpKlO0yJ5s,2806
mediapipe/tasks/cc/vision/hand_landmarker/proto/hand_roi_refinement_graph_options_pb2.py,sha256=Gl0HRvviAdmHbv0aeR8f1v_ST4DenDiiHGCiIGa3GyM,1778
mediapipe/tasks/cc/vision/holistic_landmarker/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/holistic_landmarker/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/holistic_landmarker/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/holistic_landmarker/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/holistic_landmarker/proto/__pycache__/holistic_landmarker_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/holistic_landmarker/proto/__pycache__/holistic_result_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/holistic_landmarker/proto/holistic_landmarker_graph_options_pb2.py,sha256=lqX_XOc5-jM-VfWVosHA8rUM4XzgBWAHTbyCGvQWAto,4511
mediapipe/tasks/cc/vision/holistic_landmarker/proto/holistic_result_pb2.py,sha256=252hDa3fH2Wd5kaEX6uPeHZX0D5CvhPELXeH7mgVjKU,2352
mediapipe/tasks/cc/vision/image_classifier/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_classifier/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_classifier/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_classifier/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_classifier/proto/__pycache__/image_classifier_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_classifier/proto/image_classifier_graph_options_pb2.py,sha256=nrIQDRMKeRbD0NrNFksXsuX7a7Dxk-KElcblxdyCc1M,3063
mediapipe/tasks/cc/vision/image_embedder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_embedder/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_embedder/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_embedder/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_embedder/proto/__pycache__/image_embedder_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_embedder/proto/image_embedder_graph_options_pb2.py,sha256=EIhXI0Vv-1T4nY86SR0v80MtgJsRBP19B3175u0w67Q,3031
mediapipe/tasks/cc/vision/image_generator/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_generator/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_generator/diffuser/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_generator/diffuser/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_generator/diffuser/__pycache__/stable_diffusion_iterate_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_generator/diffuser/stable_diffusion_iterate_calculator_pb2.py,sha256=8xBGRA4EowOpyAIohpySK_DdOSgGF5XTVQiioGGh89Q,4246
mediapipe/tasks/cc/vision/image_generator/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_generator/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_generator/proto/__pycache__/conditioned_image_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_generator/proto/__pycache__/control_plugin_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_generator/proto/__pycache__/image_generator_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_generator/proto/conditioned_image_graph_options_pb2.py,sha256=YWLYgRQfXKka7AdX2XovQMtJMoK7eJH124KHidacl0U,4282
mediapipe/tasks/cc/vision/image_generator/proto/control_plugin_graph_options_pb2.py,sha256=fqjb3PV-iSf2uZhg4fdmpyH7WGBA8iWs8X2kbwciSjg,2664
mediapipe/tasks/cc/vision/image_generator/proto/image_generator_graph_options_pb2.py,sha256=6H2zaNuYZufv8e6Ksj9Xast8lHCz5g5zj7Ll3hNnOEI,2691
mediapipe/tasks/cc/vision/image_segmenter/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_segmenter/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_segmenter/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_segmenter/calculators/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_segmenter/calculators/__pycache__/tensors_to_segmentation_calculator_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_segmenter/calculators/tensors_to_segmentation_calculator_pb2.py,sha256=hz3ovXXaGG1z4nzWf7Zz78VSURMa_rPA79xjtSgapzI,3046
mediapipe/tasks/cc/vision/image_segmenter/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_segmenter/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_segmenter/proto/__pycache__/image_segmenter_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_segmenter/proto/__pycache__/segmenter_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/image_segmenter/proto/image_segmenter_graph_options_pb2.py,sha256=dcjLdOJ7mVlMoY-nFXKCYf2Fs0RYwfTPXCMlem3_fE4,3109
mediapipe/tasks/cc/vision/image_segmenter/proto/segmenter_options_pb2.py,sha256=QXOx0KpjgMzb3UUwPaUGuf40UdoiWZTJgfnPGeUdcdY,2279
mediapipe/tasks/cc/vision/interactive_segmenter/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/interactive_segmenter/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/object_detector/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/object_detector/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/object_detector/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/object_detector/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/object_detector/proto/__pycache__/object_detector_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/object_detector/proto/object_detector_options_pb2.py,sha256=LB0cYRAlohnhbAdwvG-tKYZQE7EzQSfkoOxILEJmphQ,3019
mediapipe/tasks/cc/vision/pose_detector/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/pose_detector/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/pose_detector/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/pose_detector/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/pose_detector/proto/__pycache__/pose_detector_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/pose_detector/proto/pose_detector_graph_options_pb2.py,sha256=vTpWGLfBm213xZoiSa2tH_U02r7xLI41zqhJfe4-Sjs,2818
mediapipe/tasks/cc/vision/pose_landmarker/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/pose_landmarker/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/pose_landmarker/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/pose_landmarker/proto/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/pose_landmarker/proto/__pycache__/pose_landmarker_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/pose_landmarker/proto/__pycache__/pose_landmarks_detector_graph_options_pb2.cpython-312.pyc,,
mediapipe/tasks/cc/vision/pose_landmarker/proto/pose_landmarker_graph_options_pb2.py,sha256=ka8ov5--uPEjsbfm_m7_tqDikWWJabqhiWm5LJdC_dY,3625
mediapipe/tasks/cc/vision/pose_landmarker/proto/pose_landmarks_detector_graph_options_pb2.py,sha256=rU8lGsfGdZ_O7O91hlTu-Yl3nZfuLmFRekfg6JghhJU,2857
mediapipe/tasks/cc/vision/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/utils/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/cc/vision/utils/ghum/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/utils/ghum/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/metadata/__pycache__/image_segmenter_metadata_schema_py_generated.cpython-312.pyc,,
mediapipe/tasks/metadata/__pycache__/metadata_schema_py_generated.cpython-312.pyc,,
mediapipe/tasks/metadata/__pycache__/object_detector_metadata_schema_py_generated.cpython-312.pyc,,
mediapipe/tasks/metadata/__pycache__/schema_py_generated.cpython-312.pyc,,
mediapipe/tasks/metadata/image_segmenter_metadata_schema.fbs,sha256=DyxXCWLtfa72rcUMGgt1eyLrFKsbf2d30M0-0hnR7IQ,2308
mediapipe/tasks/metadata/image_segmenter_metadata_schema_py_generated.py,sha256=AAFj_R2lGpo5fDn_ueYxu79CDGQ1lEHp3MqzBiI9TyQ,3509
mediapipe/tasks/metadata/metadata_schema.fbs,sha256=VPDMmah9VgRbwyrRd79dAqKYNkFkk8qz6YoGBXtgeXs,28030
mediapipe/tasks/metadata/metadata_schema_py_generated.py,sha256=vNhXsPWvfXTslI6ILU2OMfS91B3s5WIRDR_8vYRTv1A,117481
mediapipe/tasks/metadata/object_detector_metadata_schema.fbs,sha256=LMhxmCbeYhKjHTZ8bScENy4uoKK_pswxcnp2Y_bITiI,3507
mediapipe/tasks/metadata/object_detector_metadata_schema_py_generated.py,sha256=9qV0Q23rGRKj3NTZuJw23fkNRmsAkC9-WgOZb5b-OZw,24287
mediapipe/tasks/metadata/schema_py_generated.py,sha256=SaJs-QzfUnEnHmH2HUvzopPaR3eCTHPQj1y54OLLwpo,640817
mediapipe/tasks/python/__init__.py,sha256=wIM_WOWboOVI1MeehN8fkN_DjoA0MEBVw5mShAd8AS4,858
mediapipe/tasks/python/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/audio/__init__.py,sha256=xHcc-Vb7cZQVbq4Q9X3inHfStMHFDq8KajhFtXRTKHA,1299
mediapipe/tasks/python/audio/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/audio/__pycache__/audio_classifier.cpython-312.pyc,,
mediapipe/tasks/python/audio/__pycache__/audio_embedder.cpython-312.pyc,,
mediapipe/tasks/python/audio/audio_classifier.py,sha256=ENGoL8xQs2GJXUOQ9OEfX49xBL_s-CwII_a4SSTvQ6c,14933
mediapipe/tasks/python/audio/audio_embedder.py,sha256=bEqNloS2_njGlJoWXSfCSy0u0A-T892Bxpv5_HRAWoU,13114
mediapipe/tasks/python/audio/core/__init__.py,sha256=ZKC2XRtShVe6k6u6LxDt1pG7DQIn5nZnjurs6Pcvm6A,593
mediapipe/tasks/python/audio/core/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/audio/core/__pycache__/audio_record.cpython-312.pyc,,
mediapipe/tasks/python/audio/core/__pycache__/audio_task_running_mode.cpython-312.pyc,,
mediapipe/tasks/python/audio/core/__pycache__/base_audio_task_api.cpython-312.pyc,,
mediapipe/tasks/python/audio/core/audio_record.py,sha256=UwtaXuJUvFp6rTWYKtQzWHarIfUYUD22b_lTMhXQQSs,3655
mediapipe/tasks/python/audio/core/audio_task_running_mode.py,sha256=55Q1aLQSdET8AtzyqjH2DPOsC9qMZCRLzvQUWPyJXqA,1020
mediapipe/tasks/python/audio/core/base_audio_task_api.py,sha256=9n3vj4O1cgD28vMtbUmt0YRACSb3BXW9nPYGSzZDftE,6526
mediapipe/tasks/python/benchmark/__init__.py,sha256=epEucluzX0HinwBZoS7Tgb19j_qgfTuBf-vBkqemch8,587
mediapipe/tasks/python/benchmark/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/benchmark/__pycache__/benchmark_utils.cpython-312.pyc,,
mediapipe/tasks/python/benchmark/benchmark_utils.py,sha256=5qbqGxxYlJJQLJzbW0cwMCcGl4c8ZfKL3rNmm7xMVAE,2241
mediapipe/tasks/python/benchmark/vision/__init__.py,sha256=epEucluzX0HinwBZoS7Tgb19j_qgfTuBf-vBkqemch8,587
mediapipe/tasks/python/benchmark/vision/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/benchmark/vision/__pycache__/benchmark.cpython-312.pyc,,
mediapipe/tasks/python/benchmark/vision/benchmark.py,sha256=gifRumSkesmXVU51GHRct-UYf8S9Dn2isD28Aw7BClQ,3491
mediapipe/tasks/python/benchmark/vision/core/__init__.py,sha256=ZxHWTuEeRH77CDVcgDbCs5H-B9OomxX7oWZ3YGxG8VM,571
mediapipe/tasks/python/benchmark/vision/core/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/benchmark/vision/core/__pycache__/base_vision_benchmark_api.cpython-312.pyc,,
mediapipe/tasks/python/benchmark/vision/core/base_vision_benchmark_api.py,sha256=Yqqje22r4m0pp24m8682xc4kqtZkMlx4VF6zxOiq2b8,1331
mediapipe/tasks/python/components/__init__.py,sha256=KO3IKth2FV2gOlD70fltPxtixGnxyQJksZ-gfrZDQns,587
mediapipe/tasks/python/components/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/components/containers/__init__.py,sha256=75hMA8oN_4_cuA-EqO-Mp31-0pMmi-Lt1I3YO1c0jd8,2113
mediapipe/tasks/python/components/containers/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/audio_data.cpython-312.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/bounding_box.cpython-312.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/category.cpython-312.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/classification_result.cpython-312.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/detections.cpython-312.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/embedding_result.cpython-312.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/keypoint.cpython-312.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/landmark.cpython-312.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/landmark_detection_result.cpython-312.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/rect.cpython-312.pyc,,
mediapipe/tasks/python/components/containers/audio_data.py,sha256=niBVf_WkQgZWqTilV8qAkjlO6JXD_c68uXYoSUlst8w,4504
mediapipe/tasks/python/components/containers/bounding_box.py,sha256=UM0a43BvTp3XPg4OOhfqSsia1PXrwtMjFGLoVco-9ys,2214
mediapipe/tasks/python/components/containers/category.py,sha256=HpCo0FNXdjUeO2Vmyr0wKGkPI5yzGScS_x2Qe8sgvpg,2680
mediapipe/tasks/python/components/containers/classification_result.py,sha256=gYlgYzr2trVn-TK-WxfOPm9X29FXNi8sG7JFEU4V20E,4414
mediapipe/tasks/python/components/containers/detections.py,sha256=pwnH6hyyT4O3fo_W_b-FZEeP6hScxKmfpgyJKWp08Gw,5843
mediapipe/tasks/python/components/containers/embedding_result.py,sha256=kDmSkkfXOjTx_qb-BXG5DAzrN0CjvR9OjmYDt07cFwI,3324
mediapipe/tasks/python/components/containers/keypoint.py,sha256=QhRIJlCIX5W-28X_5xIr5BrMftjsnl4KwMVHkEQLrF4,2406
mediapipe/tasks/python/components/containers/landmark.py,sha256=hns8pbPz0FyAN1yk6bfEtKOXjnDuieP2_F6mVL5xboA,4397
mediapipe/tasks/python/components/containers/landmark_detection_result.py,sha256=CmT7medXmRjTxhr9XSJVd4ERYrG6Xm4VKE02cr51sp0,4112
mediapipe/tasks/python/components/containers/rect.py,sha256=yFhsQnPrfKhFIPd6tuFF3N561EEmPJs6_4mmUzDjLoE,3452
mediapipe/tasks/python/components/processors/__init__.py,sha256=Xo9Iw4VMAwQiV1qrtw7prq-j4I4POyAhzcdkVw_n4Sc,868
mediapipe/tasks/python/components/processors/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/components/processors/__pycache__/classifier_options.cpython-312.pyc,,
mediapipe/tasks/python/components/processors/classifier_options.py,sha256=zVQi-vTjJeZaTfniGWhN--Gon0BFirifqzaetFuKv04,3401
mediapipe/tasks/python/components/utils/__init__.py,sha256=KO3IKth2FV2gOlD70fltPxtixGnxyQJksZ-gfrZDQns,587
mediapipe/tasks/python/components/utils/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/components/utils/__pycache__/cosine_similarity.cpython-312.pyc,,
mediapipe/tasks/python/components/utils/cosine_similarity.py,sha256=gYdC7Tcce2-HOPy6fTC8vtl64gLJ9kjybxJen90KTVw,2297
mediapipe/tasks/python/core/__init__.py,sha256=KO3IKth2FV2gOlD70fltPxtixGnxyQJksZ-gfrZDQns,587
mediapipe/tasks/python/core/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/core/__pycache__/base_options.cpython-312.pyc,,
mediapipe/tasks/python/core/__pycache__/optional_dependencies.cpython-312.pyc,,
mediapipe/tasks/python/core/__pycache__/task_info.cpython-312.pyc,,
mediapipe/tasks/python/core/base_options.py,sha256=tW3Agx9PNt4aQzhhnlaPPZjqVUtyD8M8JEYKvU_fBf8,4028
mediapipe/tasks/python/core/optional_dependencies.py,sha256=Wjh_GT8NVojVSDjB7aHlBc_vjNFsLBi-5JkoVHBeqj8,1061
mediapipe/tasks/python/core/task_info.py,sha256=ceh4-O2U6CprhIIpXAmL3mzfHyomfad6_V8AgDkII6c,5375
mediapipe/tasks/python/genai/__init__.py,sha256=7rri6fT6wNurla8O2c5yKiLs9_3qIY0vKkyVAUDe-18,620
mediapipe/tasks/python/genai/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/genai/bundler/__init__.py,sha256=8W-wOVHs9UeOZYJWH1gpsP4CGKDOMHW2LDahLSltpzA,849
mediapipe/tasks/python/genai/bundler/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/genai/bundler/__pycache__/llm_bundler.cpython-312.pyc,,
mediapipe/tasks/python/genai/bundler/__pycache__/llm_bundler_test.cpython-312.pyc,,
mediapipe/tasks/python/genai/bundler/llm_bundler.py,sha256=i19DYzNQrs613RkG-DKXBi60AHBvvR5y6vTyIWpgGwc,4684
mediapipe/tasks/python/genai/bundler/llm_bundler_test.py,sha256=pG6ATY2KwnxrNLt5REtx1vLNU7HY4oX_C6DX294xZ6I,5721
mediapipe/tasks/python/genai/converter/__init__.py,sha256=jfUkinDJR5BVldnbJMbo5vIr2Xc5Z4TTnaCJTNoAUvg,893
mediapipe/tasks/python/genai/converter/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/converter_base.cpython-312.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/converter_factory.cpython-312.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/llm_converter.cpython-312.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/llm_converter_test.cpython-312.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/pytorch_converter.cpython-312.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/pytorch_converter_test.cpython-312.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/quantization_util.cpython-312.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/quantization_util_test.cpython-312.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/safetensors_converter.cpython-312.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/safetensors_converter_test.cpython-312.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/weight_bins_writer.cpython-312.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/weight_bins_writer_test.cpython-312.pyc,,
mediapipe/tasks/python/genai/converter/converter_base.py,sha256=kz_TeQlknJDa6kSSDtOyjgiapSkjWorVjKzCu0VmkZY,6621
mediapipe/tasks/python/genai/converter/converter_factory.py,sha256=2K16PZBQym0WhXM2HOdBMHMugykohoD4OTaOIo-UKko,2928
mediapipe/tasks/python/genai/converter/llm_converter.py,sha256=3RFtu3MPSCqe216DGHbM3hQDTTpr9U3zHQOZvvRtEIg,14321
mediapipe/tasks/python/genai/converter/llm_converter_test.py,sha256=33sQ8F3-YKmLbwMIvis1t_1kSZnRLMR6351oWefONm8,1995
mediapipe/tasks/python/genai/converter/pytorch_converter.py,sha256=b-GWYOzgD-ZRGgyqcXE9LG_JOL0Mqba4q0pc_imrbrg,10771
mediapipe/tasks/python/genai/converter/pytorch_converter_test.py,sha256=y_Mg9pOQtlUDh6uVmkz5LcUbk-pmDLA9L3KcxKR-OaA,3041
mediapipe/tasks/python/genai/converter/quantization_util.py,sha256=p9n7FmIE3QkrlDdiQ67P6h7-IJIxw4FUCpqSMBLtM8Q,17195
mediapipe/tasks/python/genai/converter/quantization_util_test.py,sha256=ICujhTFeREGuHGmNk1PlBpf1AUThFvv-Wl5UuZ-xWAk,9060
mediapipe/tasks/python/genai/converter/safetensors_converter.py,sha256=OU5o3RsmR2ZWQNQs2MpPba9AaFIT32jfbJa3OEQe16Q,20757
mediapipe/tasks/python/genai/converter/safetensors_converter_test.py,sha256=oCk4FnsjBJkEPlXtv8fdq9dn3I06LsSQRMi0BV_9mew,2802
mediapipe/tasks/python/genai/converter/weight_bins_writer.py,sha256=iu0u4W808AUzelpEOXd7RvIQMDJiKsHgXJ0U6z03xmU,4580
mediapipe/tasks/python/genai/converter/weight_bins_writer_test.py,sha256=z1qIqT4ioWKCT54t8y3Iy4gs36BBqnFw4DyO5yJzZMw,3069
mediapipe/tasks/python/metadata/__init__.py,sha256=YGHXQMz1ZGPcNgSXggu03b0USZKE8d9Xqvn6NDUl898,586
mediapipe/tasks/python/metadata/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/metadata/__pycache__/metadata.cpython-312.pyc,,
mediapipe/tasks/python/metadata/__pycache__/metadata_displayer_cli.cpython-312.pyc,,
mediapipe/tasks/python/metadata/flatbuffers_lib/_pywrap_flatbuffers.cp312-win_amd64.pyd,sha256=Hz9cfGcP_yosM3qvVJgwmvz-LIMwphGpVF4nNPxWELc,508416
mediapipe/tasks/python/metadata/metadata.py,sha256=EECQnM-Af0angD60jaBBOuNMgt7HExH6SqVtVMFNHGc,33763
mediapipe/tasks/python/metadata/metadata_displayer_cli.py,sha256=tLhF0B1mXG0igFTA9nPh8t1efRpRw2hQ00XpTPYdk_o,1202
mediapipe/tasks/python/metadata/metadata_writers/__init__.py,sha256=YGHXQMz1ZGPcNgSXggu03b0USZKE8d9Xqvn6NDUl898,586
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/face_stylizer.cpython-312.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/image_classifier.cpython-312.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/image_segmenter.cpython-312.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/metadata_info.cpython-312.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/metadata_writer.cpython-312.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/model_asset_bundle_utils.cpython-312.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/object_detector.cpython-312.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/text_classifier.cpython-312.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/writer_utils.cpython-312.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/face_stylizer.py,sha256=BgbMpP-kVez_FxqBUxUMgysFZRO23xBOZIkgwnuI-KA,5502
mediapipe/tasks/python/metadata/metadata_writers/image_classifier.py,sha256=fU4Xu4bm0fak8h7yjVAAQBIHpEH18ZEiXUSc-2dHgvk,3023
mediapipe/tasks/python/metadata/metadata_writers/image_segmenter.py,sha256=UAR6lvEaOa4_8Xg7HktJljuOOZrci2fI0_JDU6Na0OU,6358
mediapipe/tasks/python/metadata/metadata_writers/metadata_info.py,sha256=I9PjRWbU4-AiDO9dRx353RFDIWtvGit87aULDhJuT34,45392
mediapipe/tasks/python/metadata/metadata_writers/metadata_writer.py,sha256=v-T-qD0J6LBfV2IGYwCHm18L2RXcYrIdDG1LYb6GitY,31826
mediapipe/tasks/python/metadata/metadata_writers/model_asset_bundle_utils.py,sha256=HMUhrYzAJ0lQCAfKDOTQ82_EGDJfVS-ECVdZfFQGDM4,2622
mediapipe/tasks/python/metadata/metadata_writers/object_detector.py,sha256=IzKqY1S0p2RQ_Vd0nF08y16dHYLa-2HwB4zmuWmixVw,12970
mediapipe/tasks/python/metadata/metadata_writers/text_classifier.py,sha256=4tsw2b9MrsoHg3rFODT048xEUe_-t_HWzJuziM6au9E,5558
mediapipe/tasks/python/metadata/metadata_writers/writer_utils.py,sha256=1idd3qaXqIBpC7uTowbqM_3zOHWy_YPjiitOOfUuQdk,3094
mediapipe/tasks/python/test/__init__.py,sha256=KO3IKth2FV2gOlD70fltPxtixGnxyQJksZ-gfrZDQns,587
mediapipe/tasks/python/test/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/test/__pycache__/test_utils.cpython-312.pyc,,
mediapipe/tasks/python/test/audio/__init__.py,sha256=KO3IKth2FV2gOlD70fltPxtixGnxyQJksZ-gfrZDQns,587
mediapipe/tasks/python/test/audio/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/test/audio/__pycache__/audio_classifier_test.cpython-312.pyc,,
mediapipe/tasks/python/test/audio/__pycache__/audio_embedder_test.cpython-312.pyc,,
mediapipe/tasks/python/test/audio/audio_classifier_test.py,sha256=woyqErwUd2aErsySWs-wMezJZr0_kVpq5aDkJYY7Wh0,18571
mediapipe/tasks/python/test/audio/audio_embedder_test.py,sha256=c9-pLi1rNzawy9r2MTjS730JOkBBokrB0Drhssx2qzo,13347
mediapipe/tasks/python/test/test_utils.py,sha256=6fT5lCi8AtTEr44E1Ja4-tf4FYR7d89khv0DZsvbU_c,7122
mediapipe/tasks/python/test/text/__init__.py,sha256=KO3IKth2FV2gOlD70fltPxtixGnxyQJksZ-gfrZDQns,587
mediapipe/tasks/python/test/text/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/test/text/__pycache__/language_detector_test.cpython-312.pyc,,
mediapipe/tasks/python/test/text/__pycache__/text_classifier_test.cpython-312.pyc,,
mediapipe/tasks/python/test/text/__pycache__/text_embedder_test.cpython-312.pyc,,
mediapipe/tasks/python/test/text/language_detector_test.py,sha256=7SIXxZHJlVOsiFnrrngSgy3PHcejjRm4oBoK8CbOI5U,8857
mediapipe/tasks/python/test/text/text_classifier_test.py,sha256=y_rj-H0I6PC3AlTtxdTovtz8MTWpwjsWT1Qs91NrbHQ,9303
mediapipe/tasks/python/test/text/text_embedder_test.py,sha256=ezQjLKelg075kO9JQpjJ4C7ywhfiPHgEu4UmXxCiYYI,11107
mediapipe/tasks/python/test/vision/__init__.py,sha256=KO3IKth2FV2gOlD70fltPxtixGnxyQJksZ-gfrZDQns,587
mediapipe/tasks/python/test/vision/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/face_aligner_test.cpython-312.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/face_detector_test.cpython-312.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/face_landmarker_test.cpython-312.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/face_stylizer_test.cpython-312.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/hand_landmarker_test.cpython-312.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/holistic_landmarker_test.cpython-312.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/image_classifier_test.cpython-312.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/image_embedder_test.cpython-312.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/image_segmenter_test.cpython-312.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/interactive_segmenter_test.cpython-312.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/object_detector_test.cpython-312.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/pose_landmarker_test.cpython-312.pyc,,
mediapipe/tasks/python/test/vision/face_aligner_test.py,sha256=13z5DyOGJMG6gFl_hEKExXdwvIVMmqS4M3WLgNiU9b8,7595
mediapipe/tasks/python/test/vision/face_detector_test.py,sha256=VtQzPz5WFH4T9UDcVE2XwXfAd-NrHIv7mmGYFp5WJIQ,19645
mediapipe/tasks/python/test/vision/face_landmarker_test.py,sha256=w10GorD-W0Dyltj6sjZJnPoG3Z7wKcgHeR_rv4UJ8WA,21646
mediapipe/tasks/python/test/vision/face_stylizer_test.py,sha256=e4cBXaA_4a2x5XbmL_V__6fKgL-GJiJbkm-BBijnovQ,7704
mediapipe/tasks/python/test/vision/hand_landmarker_test.py,sha256=XwT5dyBC-0a5ELphviJh8Ui7ct06FMmgcHS5gM-4l5M,20258
mediapipe/tasks/python/test/vision/holistic_landmarker_test.py,sha256=T-uaLFP9lCX9aUqMWJOf8WLjKB2a1AFVNDk7EP5X8jM,20237
mediapipe/tasks/python/test/vision/image_classifier_test.py,sha256=JtYzO1N1kqv-fHVsZjAQv-WEdkLrhDiRZly5AGkX7L0,25249
mediapipe/tasks/python/test/vision/image_embedder_test.py,sha256=o7FJoICkRH5SL3bRIRUPL8feHrzIgrB7ZdBFgkRtP3Y,18218
mediapipe/tasks/python/test/vision/image_segmenter_test.py,sha256=xASHgafwnSuMqomcEBLRPdm3A--P3j6vWKIOr8Jbmrk,18824
mediapipe/tasks/python/test/vision/interactive_segmenter_test.py,sha256=6jtBLO6MDsFrpyWVYtHX1DpgZUkcTKBiGRPXMHE1Q3o,11999
mediapipe/tasks/python/test/vision/object_detector_test.py,sha256=O7TIKI4WgJQ3LCWpHVmPbEZ3vkXq7YpUb--7nvOXgUY,18469
mediapipe/tasks/python/test/vision/pose_landmarker_test.py,sha256=vntLpLehzTqn25PL72jpnBYxAdCxOYAnGZNGBJ9TbsI,19223
mediapipe/tasks/python/text/__init__.py,sha256=OLwFd4sxzE9zGLgle7r2z1n12V0GD1fcvmu1XqxrrYg,1423
mediapipe/tasks/python/text/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/text/__pycache__/language_detector.cpython-312.pyc,,
mediapipe/tasks/python/text/__pycache__/text_classifier.cpython-312.pyc,,
mediapipe/tasks/python/text/__pycache__/text_embedder.cpython-312.pyc,,
mediapipe/tasks/python/text/core/__init__.py,sha256=ZKC2XRtShVe6k6u6LxDt1pG7DQIn5nZnjurs6Pcvm6A,593
mediapipe/tasks/python/text/core/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/text/core/__pycache__/base_text_task_api.cpython-312.pyc,,
mediapipe/tasks/python/text/core/base_text_task_api.py,sha256=OHt7j_0n5c3HBdOrCb_BGWCdKWMKvDULp6tKA5mDZAc,1822
mediapipe/tasks/python/text/language_detector.py,sha256=VwJAKSJ85tmHjPw7riz9oo846lyUwDGQATVMUD5T0CU,8244
mediapipe/tasks/python/text/text_classifier.py,sha256=AJbYep6iL8vkf6JKRrGArr9sNd5ugLEliOK1tNr3DsE,7917
mediapipe/tasks/python/text/text_embedder.py,sha256=JB-jQyVgcbfH8tOuJjV2Qyk2ewyUn7bCSBrl9QGBZ1Y,7300
mediapipe/tasks/python/vision/__init__.py,sha256=Ib_UshmEOSNW0TjDFywXs2R4l6C7A_W4tbaKjcEIxqY,4162
mediapipe/tasks/python/vision/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/vision/__pycache__/face_aligner.cpython-312.pyc,,
mediapipe/tasks/python/vision/__pycache__/face_detector.cpython-312.pyc,,
mediapipe/tasks/python/vision/__pycache__/face_landmarker.cpython-312.pyc,,
mediapipe/tasks/python/vision/__pycache__/face_stylizer.cpython-312.pyc,,
mediapipe/tasks/python/vision/__pycache__/gesture_recognizer.cpython-312.pyc,,
mediapipe/tasks/python/vision/__pycache__/hand_landmarker.cpython-312.pyc,,
mediapipe/tasks/python/vision/__pycache__/holistic_landmarker.cpython-312.pyc,,
mediapipe/tasks/python/vision/__pycache__/image_classifier.cpython-312.pyc,,
mediapipe/tasks/python/vision/__pycache__/image_embedder.cpython-312.pyc,,
mediapipe/tasks/python/vision/__pycache__/image_segmenter.cpython-312.pyc,,
mediapipe/tasks/python/vision/__pycache__/interactive_segmenter.cpython-312.pyc,,
mediapipe/tasks/python/vision/__pycache__/object_detector.cpython-312.pyc,,
mediapipe/tasks/python/vision/__pycache__/pose_landmarker.cpython-312.pyc,,
mediapipe/tasks/python/vision/core/__init__.py,sha256=sVJS2p8J2PNVl8DLRPVY6KLpHenP_z3QVPRU0x_iL5g,571
mediapipe/tasks/python/vision/core/__pycache__/__init__.cpython-312.pyc,,
mediapipe/tasks/python/vision/core/__pycache__/base_vision_task_api.cpython-312.pyc,,
mediapipe/tasks/python/vision/core/__pycache__/image_processing_options.cpython-312.pyc,,
mediapipe/tasks/python/vision/core/__pycache__/vision_task_running_mode.cpython-312.pyc,,
mediapipe/tasks/python/vision/core/base_vision_task_api.py,sha256=BMqmHzlCHwga4w2P9tlXjR3uNQxfDgpbGZCW31m_eeU,8490
mediapipe/tasks/python/vision/core/image_processing_options.py,sha256=A2HPjQWO13g0ITpIIhmcJrjklaNsi7Y0qxV97ZjOk7w,1529
mediapipe/tasks/python/vision/core/vision_task_running_mode.py,sha256=RcQM54W3QWn4dByl-lJ1OtX8ede5Pt0P6m9aYlR2eoU,1123
mediapipe/tasks/python/vision/face_aligner.py,sha256=YlxO9uChuNR-yoQTCaGeXuWxHV_mP1AKZ3E4OBI-Udo,5827
mediapipe/tasks/python/vision/face_detector.py,sha256=Swt0AOBsDh5oPhlZMvA3m-Exw3CEh01qHtIKkfEhnbw,13259
mediapipe/tasks/python/vision/face_landmarker.py,sha256=dmh4mJT3DnB07PHWB2p7fqe2x0AgJ2b8Za-bK1B5GV4,94096
mediapipe/tasks/python/vision/face_stylizer.py,sha256=ToAhk1GZYX7xvxeGtI_a-yhlWlVsey_8CZWjNW9vltA,5761
mediapipe/tasks/python/vision/gesture_recognizer.py,sha256=8c7bGWhCaZ6wuMu0Z34OPsuKn_3He1S12m8NMfCgjns,19258
mediapipe/tasks/python/vision/hand_landmarker.py,sha256=1FgMzcN7HBJylK1cflk4h9rr7_GQ9AH2s7AlDLGMqaQ,18243
mediapipe/tasks/python/vision/holistic_landmarker.py,sha256=ES4AefmYfebHDPB0_TB7PaW6AYgzfAI-vCbt2BucSHs,23559
mediapipe/tasks/python/vision/image_classifier.py,sha256=sZuHU5SiF_oPnKKBZwzGYT5tILzA6xn90-IoXnsz7oc,14946
mediapipe/tasks/python/vision/image_embedder.py,sha256=0c7FDnUO7_wRNcCGYvhIpYI0q5HB4bpmCjNR7uj7Ec0,14383
mediapipe/tasks/python/vision/image_segmenter.py,sha256=LjjbWje4xjlE-aPj-oNFirz_7zAdXV52-NpykEV5_v0,16730
mediapipe/tasks/python/vision/interactive_segmenter.py,sha256=f3GIHmeHivVMTBSNf-lBrPsG-y0nBW2UFGwcZk5F8U4,10462
mediapipe/tasks/python/vision/object_detector.py,sha256=aKwDk3uSq8w4CBXWDaek3ujtvbwTG1ZUswOKIwUlyUY,16420
mediapipe/tasks/python/vision/pose_landmarker.py,sha256=6QCKc7NDoNSqhasXBpXbuVUuE5hEwyXx4-fFC71gnjs,16960
mediapipe/util/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/util/__pycache__/__init__.cpython-312.pyc,,
mediapipe/util/__pycache__/audio_decoder_pb2.cpython-312.pyc,,
mediapipe/util/__pycache__/color_pb2.cpython-312.pyc,,
mediapipe/util/__pycache__/label_map_pb2.cpython-312.pyc,,
mediapipe/util/__pycache__/render_data_pb2.cpython-312.pyc,,
mediapipe/util/analytics/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/util/analytics/__pycache__/__init__.cpython-312.pyc,,
mediapipe/util/analytics/__pycache__/mediapipe_log_extension_pb2.cpython-312.pyc,,
mediapipe/util/analytics/__pycache__/mediapipe_logging_enums_pb2.cpython-312.pyc,,
mediapipe/util/analytics/mediapipe_log_extension_pb2.py,sha256=y4UwgYeyc9_rZLgd8jEHAPFW6T3xr28EmFUoFq259Zo,4663
mediapipe/util/analytics/mediapipe_logging_enums_pb2.py,sha256=_yS7xkWVFOTUEz67d26l31_xBRbaEC1v8iqgEdHwT6c,3724
mediapipe/util/audio_decoder_pb2.py,sha256=zzuMhp5qC7Tu-TABN1l3FKOcTmfgloeAhgOvBX6SJSg,2385
mediapipe/util/color_pb2.py,sha256=o5FQNnxoUK5F4oqUwhX1h5SAr5fw95FMReO6GFEh9yI,1788
mediapipe/util/label_map_pb2.py,sha256=3sUPGpHeEteApkO_yfHUpMSRyUJOi8eEGq98COEU3lY,1271
mediapipe/util/render_data_pb2.py,sha256=yaGgVPM5MHAI_Aoe1dHcSLbv1no9Ngdi4lPhUjn9IIY,7781
mediapipe/util/sequence/__init__.py,sha256=bglKd2k2C7QGT1i-vstURXPJX2Cvq9FO9opr6cVeBp0,571
mediapipe/util/sequence/__pycache__/__init__.cpython-312.pyc,,
mediapipe/util/sequence/__pycache__/media_sequence.cpython-312.pyc,,
mediapipe/util/sequence/__pycache__/media_sequence_test.cpython-312.pyc,,
mediapipe/util/sequence/__pycache__/media_sequence_util.cpython-312.pyc,,
mediapipe/util/sequence/__pycache__/media_sequence_util_test.cpython-312.pyc,,
mediapipe/util/sequence/media_sequence.py,sha256=1-6E4J_Y8LsnBB7v_U2gQrVhi8oE-0r7aeuUBVH5Jm8,36596
mediapipe/util/sequence/media_sequence_test.py,sha256=pJ3YPLurEtS9t_PymjQCZrjDArchN_MGpvWYALRjwng,13823
mediapipe/util/sequence/media_sequence_util.py,sha256=OTj8ZHKRWTxI08ko0p_9U2L9SR3hXQhYD_mSDER40zE,27189
mediapipe/util/sequence/media_sequence_util_test.py,sha256=nuN9-HW3kw2kZbraCH76qhbaSyrPYZ3Fi_lXW9PvyZE,18180
mediapipe/util/tracking/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/util/tracking/__pycache__/__init__.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/box_detector_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/box_tracker_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/camera_motion_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/flow_packager_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/frame_selection_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/frame_selection_solution_evaluator_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/motion_analysis_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/motion_estimation_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/motion_models_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/motion_saliency_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/push_pull_filtering_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/region_flow_computation_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/region_flow_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/tone_estimation_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/tone_models_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/tracked_detection_manager_config_pb2.cpython-312.pyc,,
mediapipe/util/tracking/__pycache__/tracking_pb2.cpython-312.pyc,,
mediapipe/util/tracking/box_detector_pb2.py,sha256=ML6_emCjJ1jw3tr32bgkzjZ10qgbzj91iu4D3JuqtrI,3669
mediapipe/util/tracking/box_tracker_pb2.py,sha256=EAxicnJ8VufUmMVFz340OlCqQ1QpmUpJnJk2tbY4hu0,2707
mediapipe/util/tracking/camera_motion_pb2.py,sha256=NZ8wSrb2_2R27Fcd_DjbedoV-4aapWrt5i1GqPRsGsM,3847
mediapipe/util/tracking/flow_packager_pb2.py,sha256=J9i1M5az88YfkyqeWh1DEksMF1mDi-iP3pywbJhHMwg,6603
mediapipe/util/tracking/frame_selection_pb2.py,sha256=ecHZwMUOP6zHXvJK-kfcCOtDfQbjkflewNrePVzQIDo,2994
mediapipe/util/tracking/frame_selection_solution_evaluator_pb2.py,sha256=nuGwng4MxG4xsSw-3kh3coyb-oYFeY7QRWLDSnbITyc,1586
mediapipe/util/tracking/motion_analysis_pb2.py,sha256=u6JCgYHGlxRZw1ZOIO9vtRLaMXQq3lavxfmgCZ-wqBQ,4248
mediapipe/util/tracking/motion_estimation_pb2.py,sha256=BtBJLw2V1HzLN1QMO-_bpykS5Vvs3fMtD8AdB_a_qJ4,15269
mediapipe/util/tracking/motion_models_pb2.py,sha256=WQLtoPCaxixfwqb9-oFEeKaHRAIwt9VnWABazw4oZjM,3639
mediapipe/util/tracking/motion_saliency_pb2.py,sha256=dqVpGfNzlnqyXaDlQaOuRH2MUf86dXbMv5OBz64J5Lw,2048
mediapipe/util/tracking/push_pull_filtering_pb2.py,sha256=RP3OzV-jtVMJk2VIuSXI10Iw9edCpNuX6FX1JFlT23A,1398
mediapipe/util/tracking/region_flow_computation_pb2.py,sha256=nLjH9W0okH7vLJc-kQ1oYQKXRRuJiDH-gmS9Ojso5eE,13052
mediapipe/util/tracking/region_flow_pb2.py,sha256=30OzotW_s6qQitGjCOjx_GfuG-76ltWClvSI77dAmLs,6556
mediapipe/util/tracking/tone_estimation_pb2.py,sha256=aP-IoYE4pOt6t9jeaWvu552FnXOQmMxS7cAq5REpCHc,5749
mediapipe/util/tracking/tone_models_pb2.py,sha256=dy-qN0WLr9TbEWuKWai-ZZGrKG3ImC0s28b6745Ciis,2446
mediapipe/util/tracking/tracked_detection_manager_config_pb2.py,sha256=53rpKFO__WAWKQOxmisLsg55_KFLgPebf4JJol0ud1s,1290
mediapipe/util/tracking/tracking_pb2.py,sha256=54fxe1PQOhzpnTdsnGI5_chZ_FVcnBfUHredIxhK-K8,11143
